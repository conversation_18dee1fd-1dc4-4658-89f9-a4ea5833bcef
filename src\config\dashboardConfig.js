// Dashboard Configuration
// This file allows you to customize all aspects of the dashboard

export const DASHBOARD_CONFIG = {
  // Dashboard Title
  title: "Dashboard Incident MCDTech - All Projects",
  
  // Auto-refresh interval in milliseconds (30 seconds for real-time)
  refreshInterval: 30000,
  
  // JQL Queries Configuration
  jql: {
    // Base project filter - change this to your project key
    baseProject: "", // Empty = ALL projects in your Jira instance

    // Additional filters you can add
    additionalFilters: "", // e.g., "AND assignee = currentUser()"
  },
  
  // Status Cards Configuration
  statusCards: [
    {
      id: "non_traites",
      title: "Non traités",
      color: "#5e72e4", // Blue
      textColor: "white",
      jiraStatuses: ["Open"], // Map your Jira statuses here
      visible: true,
    },
    {
      id: "en_cours",
      title: "En cours",
      color: "#11cdef", // Light blue
      textColor: "white",
      jiraStatuses: ["In Progress"],
      visible: true,
    },
    {
      id: "en_attente_client",
      title: "En attente client",
      color: "#5e72e4", // Blue
      textColor: "white",
      jiraStatuses: ["Waiting for customer"],
      visible: true,
    },
    {
      id: "hardware",
      title: "Hardware",
      color: "#f5d700", // Yellow
      textColor: "#000", // Black text
      jiraStatuses: ["En attente du fournissuer", "Escalated"], // You may need to create this status or map to existing ones
      visible: true,
    },
    {
      id: "resolu",
      title: "Résolu",
      color: "#2dce89", // Green
      textColor: "white",
      jiraStatuses: ["Resolved", "Closed"],
      visible: true,
    },
    {
      id: "europe",
      title: "Europe",
      color: "#f093fb", // Pink
      textColor: "white",
      jiraStatuses: ["Europe"], // You may need to create this status or map to existing ones
      visible: true,
    }
  ],
  
  // Priority Cards Configuration
  priorityCards: [
    {
      id: "tres_urgent",
      title: "Incident Très Urgent",
      color: "#dc3545", // Red
      textColor: "white",
      jiraPriorities: ["Urgent"],
      visible: true,
    },
    {
      id: "urgent",
      title: "Incident urgent",
      color: "#fd7e14", // Orange
      textColor: "white",
      jiraPriorities: ["Moyen"],
      visible: true,
    },
    {
      id: "moyen",
      title: "Incident moyen",
      color: "#6c757d", // Gray
      textColor: "white",
      jiraPriorities: ["Faible", "Très faible"],
      visible: true,
    }
  ],
  
  // Charts Configuration
  charts: {
    // Organization/Project chart
    organizationChart: {
      title: "Tickets by Project",
      maxItems: 10, // Show top 10 projects
      groupBy: "project", // Options: "project", "assignee", "component"
    },
    
    // Monthly trends chart
    monthlyTrends: {
      title: "Monthly Trends",
      months: [
        { name: "January", num: 1 },
        { name: "February", num: 2 }, 
        { name: "March", num: 3 },
        { name: "April", num: 4 },
        { name: "May", num: 5 },
        { name: "June", num: 6 }
      ],
      currentYear: new Date().getFullYear(),
    }
  },
  
  // Colors and Styling
  theme: {
    headerColor: "#f39c12", // Orange header
    backgroundColor: "#f8f9fa",
    cardBorderRadius: 8,
    cardShadow: 3,
  }
};

// Helper function to build JQL query
export const buildJQL = (baseQuery, config = DASHBOARD_CONFIG.jql) => {
  let jql = baseQuery;

  // Handle ORDER BY clause properly
  let orderByClause = '';
  if (jql.includes('ORDER BY')) {
    const parts = jql.split('ORDER BY');
    jql = parts[0].trim();
    orderByClause = ' ORDER BY' + parts[1];
  }

  // Add project filter if specified
  if (config.baseProject) {
    if (jql.includes("WHERE") || jql.includes("AND") || jql.includes("project")) {
      // Replace existing project filter or add AND
      if (jql.includes("project IS NOT EMPTY")) {
        jql = jql.replace("project IS NOT EMPTY", `project = "${config.baseProject}"`);
      } else if (!jql.includes(`project = "${config.baseProject}"`)) {
        jql += ` AND project = "${config.baseProject}"`;
      }
    } else {
      jql = `project = "${config.baseProject}"`;
    }
  }

  // Add additional filters if specified
  if (config.additionalFilters) {
    if (jql.trim() === '' || jql === 'project IS NOT EMPTY') {
      jql = config.additionalFilters;
    } else {
      jql += ` AND ${config.additionalFilters}`;
    }
  }

  // Add ORDER BY clause back
  jql += orderByClause;

  return jql;
};

// Helper function to map Jira status to dashboard status
export const mapJiraStatusToCard = (jiraStatus) => {
  for (const card of DASHBOARD_CONFIG.statusCards) {
    if (card.jiraStatuses.some(status => 
      status.toLowerCase() === jiraStatus.toLowerCase()
    )) {
      return card.id;
    }
  }
  return null; // Status not mapped
};

// Helper function to map Jira priority to dashboard priority
export const mapJiraPriorityToCard = (jiraPriority) => {
  for (const card of DASHBOARD_CONFIG.priorityCards) {
    if (card.jiraPriorities.some(priority => 
      priority.toLowerCase() === jiraPriority.toLowerCase()
    )) {
      return card.id;
    }
  }
  return null; // Priority not mapped
};
