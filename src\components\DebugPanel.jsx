import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Box,
  Button,
  Chip,
  Grid,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Divider
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  BugReport as BugIcon,
  Storage as StorageIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import optimizedJiraApi from '../services/optimizedJiraApi';

const DebugPanel = ({ currentConfig, lastUpdated, dashboardStats }) => {
  const [debugInfo, setDebugInfo] = useState({});
  const [apiLogs, setApiLogs] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  const fetchDebugInfo = async () => {
    setRefreshing(true);
    try {
      // Get storage info
      const storageInfo = optimizedJiraApi.getStorageInfo();
      
      // Get API status
      const apiStatus = {
        baseURL: 'http://localhost:3001/api',
        timeout: '15000ms',
        retryCount: optimizedJiraApi.retryCount || 0,
        maxRetries: optimizedJiraApi.maxRetries || 2
      };

      // Get configuration status
      const configStatus = {
        refreshInterval: currentConfig.refreshInterval,
        enableRealTimeUpdates: currentConfig.enableRealTimeUpdates,
        yearFilter: currentConfig.filters?.year || 'all',
        displayFields: currentConfig.displayFields || [],
        statusCardsCount: currentConfig.statusCards?.length || 0,
        priorityCardsCount: currentConfig.priorityCards?.length || 0
      };

      setDebugInfo({
        storage: storageInfo,
        api: apiStatus,
        config: configStatus,
        lastUpdate: lastUpdated,
        stats: dashboardStats
      });

      // Simulate API logs (in a real implementation, you'd get these from the API service)
      setApiLogs([
        { time: new Date().toLocaleTimeString(), type: 'info', message: 'Real-time check initiated' },
        { time: new Date().toLocaleTimeString(), type: 'success', message: 'JQL query built successfully' },
        { time: new Date().toLocaleTimeString(), type: 'info', message: 'Checking for new tickets...' }
      ]);

    } catch (error) {
      console.error('Error fetching debug info:', error);
    } finally {
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchDebugInfo();
  }, [currentConfig, lastUpdated, dashboardStats]);

  const handleClearCache = async () => {
    if (window.confirm('Are you sure you want to clear all cached data?')) {
      optimizedJiraApi.clearStorage();
      await fetchDebugInfo();
    }
  };

  const handleTestAPI = async () => {
    setRefreshing(true);
    try {
      // Test API connectivity
      const response = await fetch('http://localhost:3001/api/jira/test');
      const result = await response.json();
      
      setApiLogs(prev => [
        ...prev,
        { 
          time: new Date().toLocaleTimeString(), 
          type: response.ok ? 'success' : 'error', 
          message: response.ok ? 'API test successful' : `API test failed: ${result.error}` 
        }
      ]);
    } catch (error) {
      setApiLogs(prev => [
        ...prev,
        { 
          time: new Date().toLocaleTimeString(), 
          type: 'error', 
          message: `API test failed: ${error.message}` 
        }
      ]);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <Paper sx={{ p: 3, mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <BugIcon /> Debug Panel
        </Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchDebugInfo}
            disabled={refreshing}
            size="small"
          >
            Refresh
          </Button>
          <Button
            variant="outlined"
            startIcon={<StorageIcon />}
            onClick={handleClearCache}
            color="warning"
            size="small"
          >
            Clear Cache
          </Button>
          <Button
            variant="outlined"
            onClick={handleTestAPI}
            disabled={refreshing}
            size="small"
          >
            Test API
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Configuration Status */}
        <Grid item xs={12} md={6}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">⚙️ Configuration Status</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Refresh Interval" 
                    secondary={`${debugInfo.config?.refreshInterval || 0} seconds`} 
                  />
                  <Chip 
                    label={debugInfo.config?.enableRealTimeUpdates ? 'Enabled' : 'Disabled'} 
                    color={debugInfo.config?.enableRealTimeUpdates ? 'success' : 'error'}
                    size="small"
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Year Filter" 
                    secondary={debugInfo.config?.yearFilter || 'all'} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Display Fields" 
                    secondary={`${debugInfo.config?.displayFields?.length || 0} additional fields`} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Status Cards" 
                    secondary={`${debugInfo.config?.statusCardsCount || 0} cards configured`} 
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* Storage Status */}
        <Grid item xs={12} md={6}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">💾 Storage Status</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <List dense>
                <ListItem>
                  <ListItemText 
                    primary="Cached Tickets" 
                    secondary={`${debugInfo.storage?.ticketsCount || 0} tickets`} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Last Cache Update" 
                    secondary={debugInfo.storage?.lastUpdate || 'Never'} 
                  />
                </ListItem>
                <ListItem>
                  <ListItemText 
                    primary="Storage Size" 
                    secondary={`${debugInfo.storage?.storageSize || 0} KB`} 
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
        </Grid>

        {/* API Logs */}
        <Grid item xs={12}>
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h6">📋 Recent API Activity</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                {apiLogs.length === 0 ? (
                  <Typography color="text.secondary">No recent activity</Typography>
                ) : (
                  apiLogs.slice(-10).reverse().map((log, index) => (
                    <Box key={index} sx={{ mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                          {log.time}
                        </Typography>
                        <Chip 
                          label={log.type} 
                          size="small" 
                          color={log.type === 'error' ? 'error' : log.type === 'success' ? 'success' : 'default'}
                        />
                        <Typography variant="body2">
                          {log.message}
                        </Typography>
                      </Box>
                      {index < apiLogs.length - 1 && <Divider sx={{ mt: 1 }} />}
                    </Box>
                  ))
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
        </Grid>
      </Grid>

      {/* Quick Stats */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>Quick Stats</Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Typography variant="h6" color="primary">
              {dashboardStats?.totalTickets?.toLocaleString() || '0'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Total Tickets
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="h6" color="success.main">
              {debugInfo.storage?.ticketsCount || '0'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Cached Tickets
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="h6" color="warning.main">
              {debugInfo.api?.retryCount || '0'}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              API Retries
            </Typography>
          </Grid>
          <Grid item xs={6} sm={3}>
            <Typography variant="h6" color="info.main">
              {debugInfo.config?.refreshInterval || '0'}s
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Refresh Rate
            </Typography>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default DebugPanel;
