// Persistent Storage Service for Jira Dashboard
// Handles server-side file storage and database persistence

class PersistentStorageService {
  constructor() {
    this.baseUrl = 'http://localhost:3001';
    this.storageEndpoint = '/api/storage';
  }

  // Save tickets to server-side file storage
  async saveTicketsToServer(tickets) {
    try {
      console.log('💾 Saving ' + tickets.length + ' tickets to server storage...');
      
      const response = await fetch(this.baseUrl + this.storageEndpoint + '/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tickets: tickets,
          timestamp: new Date().toISOString(),
          totalCount: tickets.length
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Successfully saved tickets to server:', result.message);
        return true;
      } else {
        console.warn('⚠️ Server storage failed:', response.statusText);
        return false;
      }
    } catch (error) {
      console.warn('⚠️ Server storage error:', error.message);
      return false;
    }
  }

  // Load tickets from server-side file storage
  async loadTicketsFromServer() {
    try {
      console.log('📂 Loading tickets from server storage...');
      
      const response = await fetch(this.baseUrl + this.storageEndpoint + '/load');
      
      if (response.ok) {
        const data = await response.json();
        if (data.tickets && data.tickets.length > 0) {
          console.log('✅ Loaded ' + data.tickets.length + ' tickets from server storage');
          return {
            tickets: data.tickets,
            timestamp: data.timestamp,
            totalCount: data.totalCount
          };
        } else {
          console.log('📂 No tickets found in server storage');
          return null;
        }
      } else {
        console.warn('⚠️ Server storage load failed:', response.statusText);
        return null;
      }
    } catch (error) {
      console.warn('⚠️ Server storage load error:', error.message);
      return null;
    }
  }

  // Check if server storage is available
  async isServerStorageAvailable() {
    try {
      const response = await fetch(this.baseUrl + this.storageEndpoint + '/health', {
        method: 'GET',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Get storage statistics
  async getStorageStats() {
    try {
      const response = await fetch(this.baseUrl + this.storageEndpoint + '/stats');
      if (response.ok) {
        return await response.json();
      }
      return null;
    } catch (error) {
      console.warn('⚠️ Failed to get storage stats:', error.message);
      return null;
    }
  }

  // Clear server storage
  async clearServerStorage() {
    try {
      const response = await fetch(this.baseUrl + this.storageEndpoint + '/clear', {
        method: 'DELETE'
      });
      
      if (response.ok) {
        console.log('🗑️ Server storage cleared successfully');
        return true;
      } else {
        console.warn('⚠️ Failed to clear server storage');
        return false;
      }
    } catch (error) {
      console.warn('⚠️ Server storage clear error:', error.message);
      return false;
    }
  }

  // Save tickets with multiple redundancy strategies
  async saveWithRedundancy(tickets) {
    const results = {
      localStorage: false,
      indexedDB: false,
      serverFile: false,
      database: false
    };

    // Try localStorage (handled by optimizedJiraApi)
    try {
      // This is handled by the main service
      results.localStorage = true;
    } catch (error) {
      console.warn('⚠️ localStorage save failed');
    }

    // Try server file storage
    results.serverFile = await this.saveTicketsToServer(tickets);

    // Try database storage (if available)
    results.database = await this.saveToDatabaseIfAvailable(tickets);

    const successCount = Object.values(results).filter(Boolean).length;
    console.log('💾 Redundant storage results:', results, '(' + successCount + '/4 successful)');

    return results;
  }

  // Load tickets with fallback priority
  async loadWithFallback() {
    // Priority order: Server file > Database > IndexedDB > localStorage
    
    // Try server file storage first
    let data = await this.loadTicketsFromServer();
    if (data && data.tickets.length > 0) {
      console.log('📂 Using server file storage (' + data.tickets.length + ' tickets)');
      return data;
    }

    // Try database storage
    data = await this.loadFromDatabaseIfAvailable();
    if (data && data.tickets.length > 0) {
      console.log('📂 Using database storage (' + data.tickets.length + ' tickets)');
      return data;
    }

    // IndexedDB and localStorage are handled by optimizedJiraApi
    console.log('📂 Falling back to browser storage');
    return null;
  }

  // Placeholder for database operations
  async saveToDatabaseIfAvailable(tickets) {
    // This would connect to MongoDB, PostgreSQL, etc.
    // For now, return false (not implemented)
    return false;
  }

  async loadFromDatabaseIfAvailable() {
    // This would connect to MongoDB, PostgreSQL, etc.
    // For now, return null (not implemented)
    return null;
  }
}

const persistentStorage = new PersistentStorageService();
export default persistentStorage;
