version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: jira-dashboard-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin_pass_2025
      MONGO_INITDB_DATABASE: jira-dashboard
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - jira-network

  # Optional: MongoDB Express for database management
  mongo-express:
    image: mongo-express:1.0.0
    container_name: jira-dashboard-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: admin_pass_2025
      ME_CONFIG_MONGODB_URL: *********************************************/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - jira-network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  jira-network:
    driver: bridge
