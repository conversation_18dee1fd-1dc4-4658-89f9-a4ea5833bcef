# Jira Dashboard Auto-Start Installer
# This script sets up auto-start functionality for the Jira Dashboard

param(
    [switch]$Install,
    [switch]$Uninstall,
    [switch]$Status
)

# Require administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir
$autoStartScript = Join-Path $scriptDir "auto-start.ps1"

# Registry path for startup programs
$registryPath = "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run"
$appName = "JiraDashboard"

# Task Scheduler settings
$taskName = "Jira Dashboard Auto-Start"
$taskDescription = "Automatically starts Jira Dashboard on system startup"

function Install-AutoStart {
    Write-Host "Installing Jira Dashboard Auto-Start..." -ForegroundColor Green
    
    # Method 1: Registry entry (runs for all users)
    try {
        $command = "powershell.exe -WindowStyle Hidden -ExecutionPolicy Bypass -File `"$autoStartScript`""
        Set-ItemProperty -Path $registryPath -Name $appName -Value $command -Force
        Write-Host "✅ Registry entry created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to create registry entry: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Method 2: Task Scheduler (more reliable)
    try {
        # Remove existing task if it exists
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
        
        # Create new task
        $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-WindowStyle Hidden -ExecutionPolicy Bypass -File `"$autoStartScript`""
        $trigger = New-ScheduledTaskTrigger -AtStartup
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
        
        # Add delay to ensure system is ready
        $trigger.Delay = "PT2M"  # 2 minute delay
        
        Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Description $taskDescription
        Write-Host "✅ Scheduled task created successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to create scheduled task: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Create desktop shortcut
    try {
        $WshShell = New-Object -comObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut("$env:USERPROFILE\Desktop\Jira Dashboard.lnk")
        $Shortcut.TargetPath = "powershell.exe"
        $Shortcut.Arguments = "-ExecutionPolicy Bypass -File `"$autoStartScript`""
        $Shortcut.WorkingDirectory = $projectDir
        $Shortcut.IconLocation = "shell32.dll,13"
        $Shortcut.Description = "Start Jira Dashboard in Full Screen"
        $Shortcut.Save()
        Write-Host "✅ Desktop shortcut created" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to create desktop shortcut: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n🎉 Auto-start installation completed!" -ForegroundColor Green
    Write-Host "The Jira Dashboard will now start automatically when Windows boots." -ForegroundColor Yellow
    Write-Host "You can also use the desktop shortcut to start it manually." -ForegroundColor Yellow
}

function Uninstall-AutoStart {
    Write-Host "Uninstalling Jira Dashboard Auto-Start..." -ForegroundColor Yellow
    
    # Remove registry entry
    try {
        Remove-ItemProperty -Path $registryPath -Name $appName -ErrorAction SilentlyContinue
        Write-Host "✅ Registry entry removed" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to remove registry entry: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Remove scheduled task
    try {
        Unregister-ScheduledTask -TaskName $taskName -Confirm:$false -ErrorAction SilentlyContinue
        Write-Host "✅ Scheduled task removed" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to remove scheduled task: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Remove desktop shortcut
    try {
        $shortcutPath = "$env:USERPROFILE\Desktop\Jira Dashboard.lnk"
        if (Test-Path $shortcutPath) {
            Remove-Item $shortcutPath -Force
            Write-Host "✅ Desktop shortcut removed" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "❌ Failed to remove desktop shortcut: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n✅ Auto-start uninstallation completed!" -ForegroundColor Green
}

function Get-AutoStartStatus {
    Write-Host "Jira Dashboard Auto-Start Status:" -ForegroundColor Cyan
    
    # Check registry entry
    $registryExists = $false
    try {
        $regValue = Get-ItemProperty -Path $registryPath -Name $appName -ErrorAction SilentlyContinue
        if ($regValue) {
            Write-Host "✅ Registry entry: INSTALLED" -ForegroundColor Green
            $registryExists = $true
        } else {
            Write-Host "❌ Registry entry: NOT INSTALLED" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Registry entry: NOT INSTALLED" -ForegroundColor Red
    }
    
    # Check scheduled task
    $taskExists = $false
    try {
        $task = Get-ScheduledTask -TaskName $taskName -ErrorAction SilentlyContinue
        if ($task) {
            Write-Host "✅ Scheduled task: INSTALLED ($($task.State))" -ForegroundColor Green
            $taskExists = $true
        } else {
            Write-Host "❌ Scheduled task: NOT INSTALLED" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Scheduled task: NOT INSTALLED" -ForegroundColor Red
    }
    
    # Check desktop shortcut
    $shortcutPath = "$env:USERPROFILE\Desktop\Jira Dashboard.lnk"
    if (Test-Path $shortcutPath) {
        Write-Host "✅ Desktop shortcut: EXISTS" -ForegroundColor Green
    } else {
        Write-Host "❌ Desktop shortcut: NOT FOUND" -ForegroundColor Red
    }
    
    if ($registryExists -or $taskExists) {
        Write-Host "`n🟢 Auto-start is ENABLED" -ForegroundColor Green
    } else {
        Write-Host "`n🔴 Auto-start is DISABLED" -ForegroundColor Red
    }
}

# Main execution
if ($Install) {
    Install-AutoStart
}
elseif ($Uninstall) {
    Uninstall-AutoStart
}
elseif ($Status) {
    Get-AutoStartStatus
}
else {
    Write-Host "Jira Dashboard Auto-Start Manager" -ForegroundColor Cyan
    Write-Host "=================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\install-auto-start.ps1 -Install    # Install auto-start"
    Write-Host "  .\install-auto-start.ps1 -Uninstall  # Remove auto-start"
    Write-Host "  .\install-auto-start.ps1 -Status     # Check status"
    Write-Host ""
    Write-Host "Note: This script must be run as Administrator" -ForegroundColor Red
}
