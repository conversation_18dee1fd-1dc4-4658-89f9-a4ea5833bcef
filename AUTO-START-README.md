# 🚀 Jira Dashboard Auto-Start Feature

This feature allows the Jira Dashboard to automatically start when your PC boots up and open in full screen mode, perfect for dedicated dashboard displays.

## ✨ Features

- **🔄 Auto-start on Windows boot** - Dashboard starts automatically when PC restarts
- **⛶ Full screen mode** - Opens in full screen for dedicated display
- **🎯 Easy setup** - Simple one-click installation
- **🔧 Configurable** - Enable/disable from the web interface
- **🖥️ Multiple browser support** - Works with Chrome, Edge, Firefox

## 🎮 How to Use

### Method 1: Web Interface (Recommended)

1. **Open the Jira Dashboard** in your browser
2. **Click the "🟢 Auto-Start OFF" button** in the top toolbar
3. **Follow the instructions** to complete Windows setup
4. **The button will turn green "🔴 Auto-Start ON"** when enabled

### Method 2: Manual Setup

1. **Right-click on `setup-auto-start.bat`**
2. **Select "Run as administrator"**
3. **Choose option 1** to install auto-start
4. **Follow the prompts**

## 📋 Setup Options

The setup script provides these options:

1. **Install Auto-Start** - Enable automatic startup
2. **Uninstall Auto-Start** - Disable automatic startup  
3. **Check Status** - See current configuration
4. **Exit** - Close the setup

## 🔧 What Gets Installed

When you enable auto-start, the system creates:

- **✅ Windows Registry Entry** - For system-level auto-start
- **✅ Scheduled Task** - More reliable startup method
- **✅ Desktop Shortcut** - Manual launch option
- **✅ Configuration File** - Stores your preferences

## 🎯 Full Screen Features

The dashboard includes these full screen capabilities:

- **⛶ Full Screen Button** - Toggle full screen mode manually
- **🔲 Exit Full Screen** - Return to windowed mode
- **⌨️ Keyboard Shortcut** - Press F11 or Esc to exit full screen
- **🔄 Auto full screen** - Automatically enters full screen on auto-start

## 🛠️ Troubleshooting

### Auto-start not working?

1. **Check if running as Administrator**
   ```
   Right-click setup-auto-start.bat → "Run as administrator"
   ```

2. **Verify installation status**
   ```
   Run setup-auto-start.bat → Choose option 3 (Check Status)
   ```

3. **Check Windows startup programs**
   ```
   Task Manager → Startup tab → Look for "Jira Dashboard"
   ```

### Browser not opening in full screen?

1. **Try different browser** - Chrome and Edge work best
2. **Check browser permissions** - Allow full screen access
3. **Manual full screen** - Use the ⛶ Full Screen button

### Dashboard not loading?

1. **Check if server is running**
   ```
   Open http://localhost:3000 manually
   ```

2. **Wait for startup** - Server takes 15-30 seconds to start
3. **Check network connectivity** - Ensure internet access

## 📁 File Structure

```
jira-dashboard/
├── setup-auto-start.bat          # Main setup script
├── scripts/
│   ├── auto-start.ps1            # PowerShell startup script
│   ├── auto-start.bat            # Batch startup script
│   └── install-auto-start.ps1    # Installation script
└── config/
    └── auto-start.json           # Configuration file
```

## ⚙️ Configuration

The auto-start configuration is stored in `config/auto-start.json`:

```json
{
  "enabled": true,
  "url": "http://localhost:3000",
  "fullScreen": true,
  "lastModified": "2024-01-01T00:00:00.000Z"
}
```

## 🔒 Security Notes

- **Administrator privileges required** - For Windows startup configuration
- **Local network only** - Dashboard runs on localhost:3000
- **No external access** - Secure by default

## 🆘 Support

If you encounter issues:

1. **Check the console** - Press F12 in browser for error messages
2. **Review logs** - Check the terminal where you started the server
3. **Restart services** - Stop and restart the dashboard server
4. **Reinstall auto-start** - Uninstall and reinstall the auto-start feature

## 🎉 Success!

When properly configured, your Jira Dashboard will:

- ✅ Start automatically when Windows boots
- ✅ Open in full screen mode
- ✅ Display real-time ticket data
- ✅ Update every 10 seconds
- ✅ Work perfectly for dedicated displays

Perfect for office dashboards, monitoring screens, and team displays! 🚀
