@echo off
title Create Portable Jira Dashboard Installer

echo =========================================================
echo    Create Portable Jira Dashboard Installer
echo =========================================================
echo.
echo This will create a portable installer package that you can
echo copy to other PCs for easy installation.
echo.

REM Create portable installer directory
set "PORTABLE_DIR=Jira-Dashboard-Portable-Installer"
if exist "%PORTABLE_DIR%" (
    echo Cleaning existing portable installer...
    rmdir /s /q "%PORTABLE_DIR%"
)

echo Creating portable installer directory...
mkdir "%PORTABLE_DIR%"

echo.
echo Copying essential files...

REM Copy main project files
xcopy /E /I /H /Y "src" "%PORTABLE_DIR%\src\" >nul
xcopy /E /I /H /Y "public" "%PORTABLE_DIR%\public\" >nul
xcopy /E /I /H /Y "scripts" "%PORTABLE_DIR%\scripts\" >nul

REM Copy configuration files
copy /Y "package.json" "%PORTABLE_DIR%\" >nul
copy /Y "vite.config.js" "%PORTABLE_DIR%\" >nul
copy /Y "server.js" "%PORTABLE_DIR%\" >nul
copy /Y "tailwind.config.js" "%PORTABLE_DIR%\" >nul
copy /Y "postcss.config.js" "%PORTABLE_DIR%\" >nul
copy /Y ".env.example" "%PORTABLE_DIR%\" >nul

REM Copy installer files
copy /Y "INSTALL-EVERYTHING.bat" "%PORTABLE_DIR%\" >nul
copy /Y "simple-auto-install.ps1" "%PORTABLE_DIR%\" >nul
copy /Y "setup-auto-start.bat" "%PORTABLE_DIR%\" >nul
copy /Y "start-production.bat" "%PORTABLE_DIR%\" >nul

REM Copy documentation
copy /Y "README-COMPLETE-SETUP.md" "%PORTABLE_DIR%\" >nul
copy /Y "PRODUCTION-SETUP.md" "%PORTABLE_DIR%\" >nul
copy /Y "AUTO-START-README.md" "%PORTABLE_DIR%\" >nul

REM Copy logo and assets
if exist "public\dbsys-logo.png" copy /Y "public\dbsys-logo.png" "%PORTABLE_DIR%\public\" >nul

echo.
echo Creating installation instructions...

REM Create simple installation instructions
echo # Jira Dashboard - Portable Installer > "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ## Quick Installation on Any Windows PC >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ### Step 1: Copy Files >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 1. Copy this entire folder to the target PC >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 2. Place it anywhere (Desktop, Documents, etc.) >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ### Step 2: Install >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 1. Right-click on **INSTALL-EVERYTHING.bat** >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 2. Select **"Run as administrator"** >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 3. Click **"Yes"** when prompted >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 4. Wait 5-10 minutes for installation >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo 5. Done! Dashboard starts automatically >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ### What Gets Installed: >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Node.js (if not already installed) >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - MongoDB (local installation) >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - All application dependencies >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Windows Service (auto-start on boot) >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Full screen dashboard >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ### System Requirements: >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Windows 10/11 >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Administrator access >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - Internet connection (for initial setup) >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo - 4GB RAM minimum >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo. >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"
echo ### Dashboard URL: http://localhost:3000 >> "%PORTABLE_DIR%\INSTALL-INSTRUCTIONS.md"

REM Create a simple README for the portable installer
echo @echo off > "%PORTABLE_DIR%\README-FIRST.bat"
echo title Jira Dashboard - Installation Instructions >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo ========================================================= >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo    Jira Dashboard - Portable Installer >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo ========================================================= >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo Welcome to the Jira Dashboard Portable Installer! >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo This package contains everything needed to install >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo the Jira Dashboard on any Windows PC. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo INSTALLATION STEPS: >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo =================== >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo 1. Right-click on "INSTALL-EVERYTHING.bat" >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo 2. Select "Run as administrator" >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo 3. Click "Yes" when prompted >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo 4. Wait 5-10 minutes for installation >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo 5. Done! Dashboard starts automatically >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo FEATURES: >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo ========= >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Automatic installation of all components >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Auto-start with Windows >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Full screen dashboard mode >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Real-time data updates every 10 seconds >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - RESTAURANTS chart and priority cards >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Professional DBSYS logo >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Auto-restart on crashes >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo SYSTEM REQUIREMENTS: >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo ==================== >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Windows 10/11 >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Administrator access required >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - Internet connection for initial setup >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo - 4GB RAM minimum (8GB recommended) >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo Dashboard URL: http://localhost:3000 >> "%PORTABLE_DIR%\README-FIRST.bat"
echo echo. >> "%PORTABLE_DIR%\README-FIRST.bat"
echo pause >> "%PORTABLE_DIR%\README-FIRST.bat"

echo.
echo Creating logs directory...
mkdir "%PORTABLE_DIR%\logs" >nul 2>&1

echo.
echo =========================================================
echo    Portable Installer Created Successfully!
echo =========================================================
echo.
echo Location: %CD%\%PORTABLE_DIR%
echo.
echo The portable installer contains:
echo   [✓] Complete source code
echo   [✓] All configuration files  
echo   [✓] Installation scripts
echo   [✓] Auto-start setup
echo   [✓] Documentation
echo   [✓] DBSYS logo and assets
echo.
echo TO DEPLOY TO OTHER PCs:
echo ========================
echo 1. Copy the entire "%PORTABLE_DIR%" folder
echo 2. Transfer to target PC (USB, network, etc.)
echo 3. On target PC: Right-click "INSTALL-EVERYTHING.bat"
echo 4. Select "Run as administrator"
echo 5. Wait for installation to complete
echo 6. Dashboard will start automatically!
echo.
echo The installer will automatically:
echo   - Install Node.js (if needed)
echo   - Install MongoDB locally
echo   - Install all dependencies
echo   - Build production version
echo   - Setup Windows Service
echo   - Configure auto-start
echo   - Open dashboard in full screen
echo.

set /p open_folder="Would you like to open the portable installer folder? (y/n): "
if /i "%open_folder%"=="y" (
    explorer "%PORTABLE_DIR%"
)

echo.
echo Ready for deployment to other PCs! 🚀
pause
