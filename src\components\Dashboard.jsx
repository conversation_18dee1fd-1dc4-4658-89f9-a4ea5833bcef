import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Typography,
  Box,
  Paper,
  CircularProgress,
  LinearProgress,
  Alert,
  Fab,
  <PERSON><PERSON>,
  Chip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  <PERSON>ne as TuneIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import StatusCard from './StatusCard';
import OrganizationChart from './OrganizationChart';
import MonthlyTrendsChart from './MonthlyTrendsChart';
import ConfigPanel from './ConfigPanel';
import EnhancedConfigPanel from './EnhancedConfigPanel';
import DebugPanel from './DebugPanel';
import TicketsTable from './TicketsTable';
import optimizedJiraApi from '../services/optimizedJiraApi';
import {
  loadDashboardConfig,
  saveDashboardConfig,
  getVisibleStatusCards,
  getVisiblePriorityCards,
  getVisibleOrganizationCards
} from '../config/enhancedDashboardConfig';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [dashboardStats, setDashboardStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [configOpen, setConfigOpen] = useState(false);
  const [enhancedConfigOpen, setEnhancedConfigOpen] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(loadDashboardConfig());
  const [lastUpdated, setLastUpdated] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  const [tableFilters, setTableFilters] = useState({});
  const [allTickets, setAllTickets] = useState([]);
  const [newTicketsCount, setNewTicketsCount] = useState(0);
  const [loadingProgress, setLoadingProgress] = useState({ loaded: 0, total: 0, percentage: 0 });
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  const [useMongoDb, setUseMongoDb] = useState(false); // Toggle between MongoDB and direct Jira API

  const fetchData = async (isRealTimeCheck = false, forceRefresh = false) => {
    try {
      if (!isRealTimeCheck) {
        setLoading(true);
      }

      // Get ALL tickets with real-time updates
      const logMessage = forceRefresh ? '🔄 Force refreshing ALL data...' :
                         isRealTimeCheck ? '🔄 Checking for new tickets...' :
                         '🔄 Fetching ALL dashboard data...';
      console.log(logMessage);

      let allData;

      // The optimizedJiraApi now includes MongoDB integration
      // It automatically uses MongoDB when available, falls back to direct API when not
      let apiService = optimizedJiraApi;

      if (useMongoDb) {
        console.log('🗄️ Using MongoDB-enhanced API service (auto-detects MongoDB availability)');
      } else {
        console.log('📡 Using direct Jira API service (MongoDB disabled)');
      }

      if (isRealTimeCheck && initialLoadComplete && !forceRefresh) {
        // Only check for new tickets if initial load is complete
        allData = await apiService.checkForNewTickets();
      } else if (!isRealTimeCheck || !initialLoadComplete || forceRefresh) {
        // Full load on initial load, manual refresh, or force refresh
        allData = await apiService.getAllDashboardData(forceRefresh);
      } else {
        // Skip if real-time check but initial load not complete
        console.log('⏳ Initial load not complete, skipping real-time check...');
        return;
      }

      // If no data returned (real-time check with no new tickets, or API error), return early
      if (!allData) {
        if (isRealTimeCheck) {
          console.log('✅ No new tickets found');
        } else {
          console.log('⚠️ No data returned from API');
        }
        return;
      }

      // Validate that we have the required data structure
      if (!allData.status || !allData.priority || !allData.metadata) {
        console.error('❌ Invalid data structure returned from API:', allData);
        throw new Error('Invalid data structure returned from API');
      }

      setDashboardData({
        status: allData.status,
        priority: allData.priority,
        organizations: allData.organization || {},
        trends: allData.trends || []
      });

      setDashboardStats({
        totalTickets: allData.metadata.totalTickets || allData.metadata.actualTickets || 0,
        ticketsToday: allData.stats?.ticketsToday || 0,
        recentTickets: allData.stats?.recentTickets || 0
      });

      // Store ALL tickets for real-time filtering
      setAllTickets(allData.allTickets || []);
      setLastUpdated(allData.metadata?.lastUpdated || new Date().toLocaleString());
      setError(null);

      // Show notification for new tickets
      if (isRealTimeCheck && allData.metadata?.isRealTimeUpdate) {
        const newTicketsLength = allData.allTickets?.length || 0;
        const currentTicketsLength = allTickets.length;
        const newCount = newTicketsLength - currentTicketsLength;

        if (newCount > 0) {
          setNewTicketsCount(newCount);
          console.log(`🆕 ${newCount} new tickets detected!`);
          // Clear notification after 5 seconds
          setTimeout(() => setNewTicketsCount(0), 5000);
        }
      }

      console.log('✅ Dashboard data loaded successfully');
      const totalTickets = allData.metadata.totalTickets || 0;
      const loadedTickets = allData.metadata.actualTickets || 0;
      const progressPercentage = totalTickets > 0 ? ((loadedTickets / totalTickets) * 100).toFixed(1) : 0;

      setLoadingProgress({
        loaded: loadedTickets,
        total: totalTickets,
        percentage: parseFloat(progressPercentage)
      });

      // Mark initial load as complete when we have substantial data
      if (!initialLoadComplete && loadedTickets > 1000) {
        setInitialLoadComplete(true);
        console.log('🎉 Initial load complete! Switching to real-time mode...');
      }

      console.log(`📊 Total: ${totalTickets.toLocaleString()} tickets (${loadedTickets.toLocaleString()} loaded - ${progressPercentage}%)`);
    } catch (err) {
      console.error('Dashboard error:', err);
      if (err.code === 'ECONNREFUSED' || err.message.includes('Network Error')) {
        setError('Proxy server is not running. Please start the server with: npm run server');
      } else if (err.code === 'ENOTFOUND' || err.message.includes('getaddrinfo ENOTFOUND')) {
        setError('Network connectivity issue: Cannot reach Jira server (ma-mcd.atlassian.net). Please check VPN connection or network access.');
      } else if (err.response?.status === 401) {
        setError('Authentication failed: Please check Jira credentials in .env file.');
      } else if (err.response?.status === 403) {
        setError('Access denied: Insufficient permissions to access Jira data.');
      } else if (err.response?.status >= 500) {
        setError('Jira server error: Please try again later.');
      } else {
        setError(err.response?.data?.error || err.message || 'Failed to fetch dashboard data. Please check your Jira configuration.');
      }
      if (!isRealTimeCheck) {
        setDashboardData(null);
        setDashboardStats(null);
      }
    } finally {
      if (!isRealTimeCheck) {
        setLoading(false);
      }
      setRefreshing(false);
    }
  };

  const handleConfigChange = (newConfig) => {
    setCurrentConfig(newConfig);
    saveDashboardConfig(newConfig);
    // Trigger data refresh with new configuration
    fetchData();
  };

  const handleEnhancedConfigChange = (newConfig) => {
    setCurrentConfig(newConfig);
    saveDashboardConfig(newConfig);
    // Trigger data refresh with new configuration
    fetchData();
  };

  const handleManualRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleClearCache = async () => {
    if (window.confirm('Are you sure you want to clear all cached data? This will force a full reload of all tickets.')) {
      optimizedJiraApi.clearStorage();
      setRefreshing(true);
      await fetchData();
      setRefreshing(false);
    }
  };

  const handleStatusCardClick = (statusTitle) => {
    console.log(`🎯 Status card clicked: ${statusTitle}`);
    setTableFilters({ status: statusTitle });
  };

  const handlePriorityCardClick = (priorityTitle) => {
    console.log(`🎯 Priority card clicked: ${priorityTitle}`);
    setTableFilters({ priority: priorityTitle });
  };

  const handleOrganizationCardClick = (organizationTitle) => {
    console.log(`🎯 Organization card clicked: ${organizationTitle}`);
    setTableFilters({ project: organizationTitle });
  };

  const handleClearFilters = () => {
    console.log('🔄 Clearing table filters');
    setTableFilters({});
  };

  useEffect(() => {
    fetchData();

    // Set up real-time updates every 10 seconds
    const interval = setInterval(() => {
      console.log('⏰ 10-second check for new tickets...');
      fetchData(true); // isRealTimeCheck = true
    }, 10000); // 10 seconds

    // Add global methods for debugging
    window.clearJiraCache = async () => {
      console.log('🗑️ Clearing ALL Jira cache (MongoDB + localStorage cleanup)...');
      await optimizedJiraApi.clearStorage();
      fetchData(false, true); // Force refresh
    };

    window.forceRefresh = () => {
      console.log('🔄 Force refreshing dashboard...');
      fetchData(false, true); // Force refresh
    };

    window.getStorageInfo = async () => {
      const info = await optimizedJiraApi.getComprehensiveStorageInfo();
      console.log('📊 Comprehensive Storage Info:', info);
      return info;
    };

    window.forceMongoSync = async () => {
      console.log('🔄 Forcing MongoDB full sync...');
      await fetchData(false, true); // This will trigger MongoDB sync
    };

    window.getMongoStats = async () => {
      const stats = await optimizedJiraApi.getMongoStats();
      console.log('📊 MongoDB Stats:', stats);
      return stats;
    };

    window.getTotalJiraCount = async () => {
      try {
        const response = await fetch('http://localhost:3001/api/mongodb/total-count');
        const data = await response.json();
        console.log('📊 Total Jira Tickets:', data);
        return data;
      } catch (error) {
        console.error('❌ Failed to get total count:', error);
      }
    };

    window.forceCompleteSync = async () => {
      console.log('🔄 Forcing COMPLETE sync (clearing DB and fetching ALL 77k+ tickets)...');
      try {
        const response = await fetch('http://localhost:3001/api/mongodb/force-sync', {
          method: 'POST'
        });
        const data = await response.json();
        console.log('✅ Complete sync result:', data);
        // Refresh the dashboard
        await fetchData(false, true);
        return data;
      } catch (error) {
        console.error('❌ Complete sync failed:', error);
      }
    };

    window.clearAndResync = async () => {
      console.log('🗑️ CLEARING MongoDB and starting FRESH sync with ALL 77k+ tickets...');
      try {
        const response = await fetch('http://localhost:3001/api/mongodb/clear-and-resync', {
          method: 'POST'
        });
        const data = await response.json();
        console.log('✅ Clear and resync result:', data);
        // Refresh the dashboard
        await fetchData(false, true);
        return data;
      } catch (error) {
        console.error('❌ Clear and resync failed:', error);
      }
    };

    return () => {
      clearInterval(interval);
      console.log('🛑 Stopped real-time updates');
    };
  }, []); // Only fetch data on mount

  // Separate effect for real-time updates to prevent infinite loop
  useEffect(() => {
    if (currentConfig.enableRealTimeUpdates && currentConfig.refreshInterval > 0 && initialLoadComplete) {
      const interval = setInterval(() => {
        fetchData(true); // Real-time check for new tickets
      }, currentConfig.refreshInterval * 1000);
      return () => clearInterval(interval);
    }
  }, [currentConfig.enableRealTimeUpdates, currentConfig.refreshInterval, initialLoadComplete]);

  // Additional fast polling for new tickets (every 15 seconds, only after initial load)
  useEffect(() => {
    if (initialLoadComplete) {
      const fastInterval = setInterval(() => {
        fetchData(true); // Check for new tickets every 15 seconds
      }, 15000);

      return () => clearInterval(fastInterval);
    }
  }, [initialLoadComplete]);

  if (loading) {
    return (
      <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={3}>
        <CircularProgress size={60} />
        <Typography variant="h6" color="primary">
          Loading Jira Dashboard...
        </Typography>
        {loadingProgress.total > 0 && (
          <Box textAlign="center">
            <Typography variant="body1" color="text.secondary">
              Loading tickets: {loadingProgress.loaded.toLocaleString()} / {loadingProgress.total.toLocaleString()}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Progress: {loadingProgress.percentage}%
            </Typography>
            <Box sx={{ width: 300, mt: 2 }}>
              <LinearProgress
                variant="determinate"
                value={loadingProgress.percentage}
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
            {loadingProgress.loaded > 100 && (
              <Button
                variant="outlined"
                color="primary"
                onClick={() => {
                  setLoading(false);
                  console.log('🚀 User requested to show partial data');
                }}
                sx={{ mt: 2 }}
              >
                Show Partial Data ({loadingProgress.loaded.toLocaleString()} tickets)
              </Button>
            )}
          </Box>
        )}
      </Box>
    );
  }

  if (error && !dashboardData) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{
          backgroundColor: '#f39c12',
          color: 'white',
          p: 3,
          mb: 4,
          borderRadius: 2,
          textAlign: 'center'
        }}>
          <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold' }}>
            Dashboard Incident MCDTech
          </Typography>
        </Box>

        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ mb: 2 }}>
            Configuration Required
          </Typography>
          <Typography sx={{ mb: 2 }}>
            {error}
          </Typography>
          <Typography variant="body2">
            Please configure your Jira credentials in the .env file:
          </Typography>
          <Box component="pre" sx={{
            backgroundColor: '#f5f5f5',
            p: 2,
            mt: 2,
            borderRadius: 1,
            fontSize: '0.875rem',
            overflow: 'auto'
          }}>
{`VITE_JIRA_BASE_URL=https://your-domain.atlassian.net
VITE_JIRA_EMAIL=<EMAIL>
VITE_JIRA_API_TOKEN=your-api-token`}
          </Box>
          <Typography variant="body2" sx={{ mt: 2 }}>
            Get your API token at: <a href="https://id.atlassian.com/manage-profile/security/api-tokens" target="_blank" rel="noopener noreferrer">
              https://id.atlassian.com/manage-profile/security/api-tokens
            </a>
          </Typography>
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Enhanced Header */}
      <Box sx={{
        backgroundColor: currentConfig.primaryColor || '#f39c12',
        color: 'white',
        p: 3,
        mb: 4,
        borderRadius: 2,
        textAlign: 'center',
        position: 'relative'
      }}>
        <Typography variant="h3" component="h1" sx={{ fontWeight: 'bold' }}>
          {currentConfig.title}
        </Typography>
        <Typography variant="subtitle1" sx={{ mt: 1, opacity: 0.9 }}>
          {currentConfig.subtitle} • Total tickets: {dashboardStats?.totalTickets?.toLocaleString() || 'Loading...'} • Last updated: {lastUpdated || 'Loading...'} • Source: {useMongoDb ? 'MongoDB' : 'Direct Jira API'} • Cache: {optimizedJiraApi.getStorageInfo().ticketsCount} tickets
        </Typography>

        {/* New Tickets Notification */}
        {newTicketsCount > 0 && (
          <Box sx={{
            position: 'absolute',
            top: 16,
            left: 16,
            backgroundColor: '#27ae60',
            color: 'white',
            px: 2,
            py: 1,
            borderRadius: 2,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%': { opacity: 1 },
              '50%': { opacity: 0.7 },
              '100%': { opacity: 1 }
            }
          }}>
            <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
              🆕 {newTicketsCount} new ticket{newTicketsCount > 1 ? 's' : ''}!
            </Typography>
          </Box>
        )}

        {/* Header Controls */}
        <Box sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          display: 'flex',
          gap: 1
        }}>
          <Button
            variant="outlined"
            size="small"
            startIcon={refreshing ? <CircularProgress size={16} color="inherit" /> : <RefreshIcon />}
            onClick={handleManualRefresh}
            disabled={refreshing}
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.5)',
              '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
            }}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<TuneIcon />}
            onClick={() => setEnhancedConfigOpen(true)}
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.5)',
              '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
            }}
          >
            Configure
          </Button>
          <Button
            variant="outlined"
            size="small"
            startIcon={<DeleteIcon />}
            onClick={handleClearCache}
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.5)',
              '&:hover': { borderColor: 'white', backgroundColor: 'rgba(255,255,255,0.1)' }
            }}
          >
            Clear Cache
          </Button>
          <Button
            variant={useMongoDb ? "contained" : "outlined"}
            size="small"
            onClick={() => {
              setUseMongoDb(!useMongoDb);
              setInitialLoadComplete(false);
              fetchData(false);
            }}
            sx={{
              backgroundColor: useMongoDb ? 'rgba(255,255,255,0.2)' : 'transparent',
              borderColor: 'rgba(255,255,255,0.5)',
              color: 'white',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)',
                borderColor: 'white'
              }
            }}
          >
            {useMongoDb ? '🗄️ MongoDB' : '🔗 Direct'}
          </Button>
        </Box>

        {/* Status Indicators */}
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 1, flexWrap: 'wrap' }}>
          {currentConfig.enableRealTimeUpdates && (
            <Chip
              label={`Auto-refresh: ${currentConfig.refreshInterval}s`}
              size="small"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
          )}
          {dashboardStats && (
            <Chip
              label={`Today: ${dashboardStats.ticketsToday} tickets`}
              size="small"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
          )}
          {dashboardData?.status?._metadata && currentConfig.showMetadata && (
            <Chip
              label={`Sample: ${dashboardData.status._metadata.sampleSize} (${dashboardData.status._metadata.scaleFactor}x)`}
              size="small"
              sx={{ backgroundColor: 'rgba(255,255,255,0.2)', color: 'white' }}
            />
          )}
        </Box>
      </Box>

      {error && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Enhanced Status Cards */}
      {getVisibleStatusCards(currentConfig).length > 0 && (
        <>
          <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
            📊 Status Overview
          </Typography>
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {getVisibleStatusCards(currentConfig).map((card, index) => (
              <Grid item xs={12} sm={6} md={2} key={card.id}>
                <StatusCard
                  title={card.title}
                  count={dashboardData?.status[card.title] || 0}
                  color={card.color}
                  textColor={card.textColor}
                  clickable={true}
                  onClick={() => handleStatusCardClick(card.title)}
                />
              </Grid>
            ))}
          </Grid>
        </>
      )}

      {/* Enhanced Priority Cards */}
      {getVisiblePriorityCards(currentConfig).length > 0 && (
        <>
          <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
            🎯 Priority Overview
          </Typography>
          <Grid container spacing={3} sx={{ mb: 4 }}>
            {getVisiblePriorityCards(currentConfig).map((card, index) => (
              <Grid item xs={12} md={12 / getVisiblePriorityCards(currentConfig).length} key={card.id}>
                <StatusCard
                  title={card.title}
                  count={dashboardData?.priority[card.title] || 0}
                  color={card.color}
                  textColor={card.textColor}
                  clickable={true}
                  onClick={() => handlePriorityCardClick(card.title)}
                />
              </Grid>
            ))}
          </Grid>
        </>
      )}

      {/* Enhanced Organization Cards - Temporarily disabled due to mapping issues */}

      {/* Enhanced Charts */}
      <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
        📈 Analytics & Trends
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        {currentConfig.charts?.projectChart?.enabled && (
          <Grid item xs={12} md={6}>
            <Paper sx={{
              p: 3,
              height: 400,
              borderRadius: 2,
              boxShadow: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                {currentConfig.charts.projectChart.title}
              </Typography>
              <OrganizationChart data={
                dashboardData?.organizations
                  ? Object.entries(dashboardData.organizations).map(([name, count]) => ({ name, count }))
                  : []
              } />
            </Paper>
          </Grid>
        )}
        {currentConfig.charts?.trendsChart?.enabled && (
          <Grid item xs={12} md={6}>
            <Paper sx={{
              p: 3,
              height: 400,
              borderRadius: 2,
              boxShadow: 3
            }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
                {currentConfig.charts.trendsChart.title}
              </Typography>
              <MonthlyTrendsChart data={dashboardData?.trends || []} />
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Enhanced Tickets Table */}
      {currentConfig.showTicketsTable && (
        <>
          <Typography variant="h5" sx={{ mb: 2, fontWeight: 'bold' }}>
            📋 All Tickets
          </Typography>
          <TicketsTable
            allTickets={allTickets}
            initialFilters={tableFilters}
            onClearFilters={handleClearFilters}
            config={currentConfig}
          />
        </>
      )}

      {/* Debug Panel */}
      {currentConfig.showMetadata && (
        <DebugPanel
          currentConfig={currentConfig}
          lastUpdated={lastUpdated}
          dashboardStats={dashboardStats}
        />
      )}

      {/* Floating Action Buttons */}
      <Box sx={{ position: 'fixed', bottom: 16, right: 16, zIndex: 1000 }}>
        {/* Refresh Button */}
        <Fab
          color="secondary"
          aria-label="refresh"
          sx={{ mr: 1 }}
          onClick={() => {
            console.log('🔄 Force refresh requested by user');
            fetchData(false, true); // forceRefresh = true
          }}
        >
          <RefreshIcon />
        </Fab>

        {/* Settings Button */}
        <Fab
          color="primary"
          aria-label="settings"
          onClick={() => setConfigOpen(true)}
        >
          <SettingsIcon />
        </Fab>
      </Box>

      {/* Legacy Configuration Panel */}
      <ConfigPanel
        open={configOpen}
        onClose={() => setConfigOpen(false)}
        onConfigChange={handleConfigChange}
      />

      {/* Enhanced Configuration Panel */}
      <EnhancedConfigPanel
        open={enhancedConfigOpen}
        onClose={() => setEnhancedConfigOpen(false)}
        config={currentConfig}
        onConfigChange={handleEnhancedConfigChange}
      />
    </Container>
  );
};

export default Dashboard;
