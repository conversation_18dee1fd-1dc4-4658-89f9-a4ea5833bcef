#!/usr/bin/env node

import { spawn } from 'child_process';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// MongoDB configuration
const MONGODB_PORT = 27017;
const MONGODB_DATA_DIR = join(__dirname, '..', 'mongodb-data');
const MONGODB_LOG_DIR = join(__dirname, '..', 'mongodb-logs');
const MONGODB_CONFIG_FILE = join(__dirname, '..', 'mongodb.conf');

// Database and user configuration
const DB_NAME = 'jira-dashboard';
const DB_USER = 'jira_user';
const DB_PASSWORD = 'jira_pass_2025';

// Ensure directories exist
function ensureDirectories() {
  if (!existsSync(MONGODB_DATA_DIR)) {
    mkdirSync(MONGODB_DATA_DIR, { recursive: true });
    console.log(`📁 Created MongoDB data directory: ${MONGODB_DATA_DIR}`);
  }

  if (!existsSync(MONGODB_LOG_DIR)) {
    mkdirSync(MONGODB_LOG_DIR, { recursive: true });
    console.log(`📁 Created MongoDB log directory: ${MONGODB_LOG_DIR}`);
  }
}

// Create MongoDB configuration file
function createMongoConfig() {
  const configContent = `
# MongoDB Configuration File
storage:
  dbPath: ${MONGODB_DATA_DIR.replace(/\\/g, '/')}
  journal:
    enabled: true

systemLog:
  destination: file
  logAppend: true
  path: ${join(MONGODB_LOG_DIR, 'mongodb.log').replace(/\\/g, '/')}

net:
  port: ${MONGODB_PORT}
  bindIp: 127.0.0.1

security:
  authorization: enabled

processManagement:
  fork: true
  pidFilePath: ${join(MONGODB_LOG_DIR, 'mongodb.pid').replace(/\\/g, '/')}
`;

  writeFileSync(MONGODB_CONFIG_FILE, configContent.trim());
  console.log(`📝 Created MongoDB config file: ${MONGODB_CONFIG_FILE}`);
}

// Check if MongoDB is running
function checkMongoDBRunning() {
  return new Promise((resolve) => {
    const testConnection = spawn('mongosh', [
      '--host', `localhost:${MONGODB_PORT}`,
      '--eval', 'db.runCommand("ping")',
      '--quiet'
    ]);

    testConnection.on('close', (code) => {
      resolve(code === 0);
    });

    testConnection.on('error', () => {
      resolve(false);
    });
  });
}

// Start MongoDB without authentication first
function startMongoDBInitial() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting MongoDB (initial setup without auth)...');

    const mongodArgs = [
      '--dbpath', MONGODB_DATA_DIR,
      '--port', MONGODB_PORT.toString(),
      '--logpath', join(MONGODB_LOG_DIR, 'mongodb.log'),
      '--logappend',
      '--bind_ip', '127.0.0.1',
      '--fork'
    ];

    const mongod = spawn('mongod', mongodArgs);

    mongod.on('close', (code) => {
      if (code === 0) {
        console.log('✅ MongoDB started successfully (no auth)');
        resolve();
      } else {
        reject(new Error(`MongoDB failed to start with code ${code}`));
      }
    });

    mongod.on('error', (error) => {
      reject(error);
    });
  });
}

// Create admin user and database user
function createUsers() {
  return new Promise((resolve, reject) => {
    console.log('👤 Creating MongoDB users...');

    const createUserScript = `
// Create admin user
use admin
db.createUser({
  user: "admin",
  pwd: "admin_pass_2025",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" }, "readWriteAnyDatabase" ]
})

// Create database user
use ${DB_NAME}
db.createUser({
  user: "${DB_USER}",
  pwd: "${DB_PASSWORD}",
  roles: [ { role: "readWrite", db: "${DB_NAME}" } ]
})

// Create indexes for performance
db.tickets.createIndex({ "key": 1 }, { unique: true })
db.tickets.createIndex({ "status": 1 })
db.tickets.createIndex({ "priority": 1 })
db.tickets.createIndex({ "organization": 1 })
db.tickets.createIndex({ "project": 1 })
db.tickets.createIndex({ "created": -1 })
db.tickets.createIndex({ "updated": -1 })
db.tickets.createIndex({ "lastSynced": -1 })
db.tickets.createIndex({ "isActive": 1 })

print("✅ Users and indexes created successfully")
`;

    const scriptFile = join(__dirname, 'temp-setup.js');
    writeFileSync(scriptFile, createUserScript);

    const mongosh = spawn('mongosh', [
      '--host', `localhost:${MONGODB_PORT}`,
      '--file', scriptFile
    ]);

    let output = '';
    mongosh.stdout.on('data', (data) => {
      output += data.toString();
    });

    mongosh.stderr.on('data', (data) => {
      console.error('MongoDB setup error:', data.toString());
    });

    mongosh.on('close', (code) => {
      // Clean up temp file
      try {
        require('fs').unlinkSync(scriptFile);
      } catch (e) {
        // Ignore cleanup errors
      }

      if (code === 0) {
        console.log('✅ MongoDB users created successfully');
        resolve();
      } else {
        reject(new Error(`User creation failed with code ${code}`));
      }
    });

    mongosh.on('error', (error) => {
      reject(error);
    });
  });
}

// Stop MongoDB
function stopMongoDB() {
  return new Promise((resolve) => {
    console.log('⏹️ Stopping MongoDB...');
    
    const mongosh = spawn('mongosh', [
      '--host', `localhost:${MONGODB_PORT}`,
      '--eval', 'db.shutdownServer()',
      'admin'
    ]);

    mongosh.on('close', () => {
      console.log('✅ MongoDB stopped');
      resolve();
    });

    mongosh.on('error', () => {
      // Ignore errors when stopping
      resolve();
    });
  });
}

// Start MongoDB with authentication
function startMongoDBWithAuth() {
  return new Promise((resolve, reject) => {
    console.log('🔐 Starting MongoDB with authentication...');

    const mongod = spawn('mongod', ['--config', MONGODB_CONFIG_FILE]);

    mongod.on('close', (code) => {
      if (code === 0) {
        console.log('✅ MongoDB started with authentication');
        resolve();
      } else {
        reject(new Error(`MongoDB failed to start with auth, code ${code}`));
      }
    });

    mongod.on('error', (error) => {
      reject(error);
    });
  });
}

// Test connection with authentication
function testConnection() {
  return new Promise((resolve, reject) => {
    console.log('🔍 Testing connection with authentication...');

    const mongosh = spawn('mongosh', [
      '--host', `localhost:${MONGODB_PORT}`,
      '--username', DB_USER,
      '--password', DB_PASSWORD,
      '--authenticationDatabase', DB_NAME,
      '--eval', 'db.runCommand("ping")',
      '--quiet'
    ]);

    mongosh.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Authentication test successful');
        resolve();
      } else {
        reject(new Error('Authentication test failed'));
      }
    });

    mongosh.on('error', (error) => {
      reject(error);
    });
  });
}

// Main setup function
async function setupMongoDB() {
  try {
    console.log('🚀 Setting up MongoDB with authentication...');
    
    // Ensure directories exist
    ensureDirectories();
    
    // Create config file
    createMongoConfig();
    
    // Check if MongoDB is already running
    const isRunning = await checkMongoDBRunning();
    if (isRunning) {
      console.log('⚠️ MongoDB is already running. Please stop it first.');
      return;
    }

    // Start MongoDB without auth
    await startMongoDBInitial();
    
    // Wait for MongoDB to be ready
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Create users
    await createUsers();
    
    // Stop MongoDB
    await stopMongoDB();
    
    // Wait for shutdown
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Start with authentication
    await startMongoDBWithAuth();
    
    // Wait for startup
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test connection
    await testConnection();
    
    console.log('🎉 MongoDB setup completed successfully!');
    console.log('📊 Database:', DB_NAME);
    console.log('👤 User:', DB_USER);
    console.log('🔗 Connection string: *****************************************************************');
    console.log('');
    console.log('✅ You can now use MongoDB mode in your dashboard!');
    
  } catch (error) {
    console.error('❌ MongoDB setup failed:', error.message);
    console.log('💡 Please ensure MongoDB is installed and accessible in your PATH');
    console.log('💡 You can install MongoDB from: https://www.mongodb.com/try/download/community');
    process.exit(1);
  }
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  setupMongoDB();
}

export { setupMongoDB };
