import axios from 'axios';

// Dynamic import for MongoDB to handle cases where it's not available
let mongoService = null;
let Ticket = null;
let useInMemory = false;

async function initializeMongoDB() {
  if (!mongoService) {
    try {
      const mongoModule = await import('../database/mongodb.js');
      mongoService = mongoModule.mongoService;
      Ticket = mongoModule.Ticket;

      // Test connection
      await mongoService.connect();
      console.log('✅ Using MongoDB for storage');
      useInMemory = false;

    } catch (error) {
      console.warn('⚠️ MongoDB not available, using in-memory storage:', error.message);

      // Fallback to in-memory storage
      const memoryModule = await import('./inMemoryStorage.js');
      mongoService = memoryModule.mockMongoService;
      Ticket = memoryModule.MockTicket;
      useInMemory = true;

      await mongoService.connect();
      console.log('✅ Using in-memory storage as fallback');
    }
  }
  return { mongoService, Ticket, useInMemory };
}

class TicketSyncService {
  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 30000,
    });
    this.isInitialSyncComplete = false;
    this.lastSyncTime = null;
    this.syncInProgress = false;
  }

  // Extract organization from ticket (same logic as before)
  extractOrganizationFromTicket(ticket) {
    const fields = ticket.fields || {};
    
    let organization = 'Other';
    
    // 1. Check project key first
    const projectKey = fields.project?.key || '';
    if (projectKey === 'STORE') {
      organization = 'STORE';
    } else if (['IT', 'SUPPORT', 'TECH'].includes(projectKey)) {
      organization = 'IT Support';
    } else if (['OPS', 'OPERATIONS'].includes(projectKey)) {
      organization = 'Operations';
    } else if (['MAINT', 'MAINTENANCE'].includes(projectKey)) {
      organization = 'Maintenance';
    }
    
    // 2. Check custom fields for organization info
    const customFields = [
      fields.customfield_10000,
      fields.customfield_10001,
      fields.customfield_10002,
      fields.customfield_10003,
      fields.customfield_10004,
      fields.customfield_10005
    ];
    
    for (const customField of customFields) {
      if (customField) {
        const value = typeof customField === 'string' ? customField : customField.value || customField.name || '';
        const lowerValue = value.toLowerCase();
        
        if (lowerValue.includes('store') || lowerValue.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerValue.includes('it') || lowerValue.includes('support') || lowerValue.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerValue.includes('ops') || lowerValue.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerValue.includes('maint') || lowerValue.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }
    
    // 3. Check labels
    if (fields.labels && Array.isArray(fields.labels)) {
      for (const label of fields.labels) {
        const lowerLabel = label.toLowerCase();
        if (lowerLabel.includes('store') || lowerLabel.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerLabel.includes('it') || lowerLabel.includes('support') || lowerLabel.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerLabel.includes('ops') || lowerLabel.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerLabel.includes('maint') || lowerLabel.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }
    
    // 4. Check components
    if (fields.components && Array.isArray(fields.components)) {
      for (const component of fields.components) {
        const componentName = (component.name || '').toLowerCase();
        if (componentName.includes('store') || componentName.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (componentName.includes('it') || componentName.includes('support') || componentName.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (componentName.includes('ops') || componentName.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (componentName.includes('maint') || componentName.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }
    
    return organization;
  }

  // Transform Jira ticket to MongoDB document
  transformTicketForDB(jiraTicket) {
    const fields = jiraTicket.fields || {};
    
    return {
      key: jiraTicket.key,
      summary: fields.summary || 'No summary',
      status: fields.status?.name || 'Unknown',
      priority: fields.priority?.name || 'None',
      assignee: fields.assignee?.displayName || 'Unassigned',
      project: fields.project?.name || fields.project?.key || 'Unknown',
      projectKey: fields.project?.key || 'Unknown',
      organization: this.extractOrganizationFromTicket(jiraTicket),
      created: new Date(fields.created || Date.now()),
      updated: new Date(fields.updated || Date.now()),
      description: fields.description || '',
      rawFields: {
        customfield_10000: fields.customfield_10000,
        customfield_10001: fields.customfield_10001,
        customfield_10002: fields.customfield_10002,
        customfield_10003: fields.customfield_10003,
        customfield_10004: fields.customfield_10004,
        customfield_10005: fields.customfield_10005,
        labels: fields.labels || [],
        components: fields.components || []
      },
      lastSynced: new Date(),
      isActive: true
    };
  }

  // Initial sync - load all tickets from Jira to MongoDB
  async performInitialSync() {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress, skipping...');
      return;
    }

    try {
      this.syncInProgress = true;
      console.log('🚀 Starting initial sync of ALL tickets to MongoDB...');

      // Initialize MongoDB or in-memory storage
      const { mongoService: mongo, Ticket: TicketModel, useInMemory: isInMemory } = await initializeMongoDB();

      if (isInMemory) {
        console.log('📝 Using in-memory storage - data will not persist between restarts');
      }

      // Connect to MongoDB
      await mongo.connect();

      // Get total count first
      const countResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          maxResults: 0
        }
      });

      const totalTickets = countResponse.data.total;
      console.log(`📊 Total tickets to sync: ${totalTickets.toLocaleString()}`);

      // Batch processing
      const batchSize = 100;
      const totalBatches = Math.ceil(totalTickets / batchSize);
      let syncedCount = 0;

      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;
        
        try {
          console.log(`📦 Syncing batch ${i + 1}/${totalBatches} (${startAt}-${startAt + batchSize})...`);

          const response = await this.api.get('/jira/search', {
            params: {
              jql: 'ORDER BY created DESC',
              fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
              maxResults: batchSize,
              startAt: startAt
            }
          });

          const tickets = response.data.issues || [];
          
          if (tickets.length === 0) {
            console.log('✅ No more tickets to sync');
            break;
          }

          // Transform and upsert tickets
          const operations = tickets.map(ticket => {
            const transformedTicket = this.transformTicketForDB(ticket);
            return {
              updateOne: {
                filter: { key: transformedTicket.key },
                update: { $set: transformedTicket },
                upsert: true
              }
            };
          });

          // Bulk write to MongoDB
          const result = await TicketModel.bulkWrite(operations, { ordered: false });
          syncedCount += tickets.length;

          console.log(`✅ Batch ${i + 1}/${totalBatches}: ${tickets.length} tickets synced (Total: ${syncedCount.toLocaleString()})`);
          console.log(`   📝 Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

          // Small delay to prevent overwhelming the API
          await new Promise(resolve => setTimeout(resolve, 100));

        } catch (error) {
          console.error(`❌ Batch ${i + 1}/${totalBatches} failed:`, error.message);
          // Continue with next batch
        }
      }

      this.isInitialSyncComplete = true;
      this.lastSyncTime = new Date();
      
      console.log(`🎉 Initial sync complete! ${syncedCount.toLocaleString()} tickets synced to MongoDB`);
      
      // Get final count from storage
      const dbCount = await TicketModel.countDocuments({ isActive: true });
      console.log(`📊 Storage now contains ${dbCount.toLocaleString()} active tickets`);

    } catch (error) {
      console.error('❌ Initial sync failed:', error);
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Real-time sync - check for new/updated tickets
  async performRealTimeSync() {
    if (!this.isInitialSyncComplete) {
      console.log('⏳ Initial sync not complete, skipping real-time sync...');
      return;
    }

    if (this.syncInProgress) {
      console.log('⏳ Sync in progress, skipping real-time sync...');
      return;
    }

    try {
      console.log('🔄 Checking for new/updated tickets...');

      // Get storage service
      const { mongoService: mongo, Ticket: TicketModel } = await initializeMongoDB();

      // Get the most recent ticket date from storage
      const lastTicket = await TicketModel.findOne({}, {}, { sort: { updated: -1 } });
      const lastUpdateTime = lastTicket ? lastTicket.updated : new Date(Date.now() - 24 * 60 * 60 * 1000); // Last 24 hours if no tickets

      // Format date for Jira JQL
      const jiraDate = this.formatDateForJira(lastUpdateTime);
      
      const response = await this.api.get('/jira/search', {
        params: {
          jql: `updated >= "${jiraDate}" ORDER BY updated DESC`,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
          maxResults: 1000
        }
      });

      const tickets = response.data.issues || [];
      
      if (tickets.length === 0) {
        console.log('✅ No new/updated tickets found');
        return { newTickets: 0, updatedTickets: 0 };
      }

      // Transform and upsert tickets
      const operations = tickets.map(ticket => {
        const transformedTicket = this.transformTicketForDB(ticket);
        return {
          updateOne: {
            filter: { key: transformedTicket.key },
            update: { $set: transformedTicket },
            upsert: true
          }
        };
      });

      const result = await TicketModel.bulkWrite(operations, { ordered: false });
      
      console.log(`🆕 Real-time sync: ${result.upsertedCount} new, ${result.modifiedCount} updated tickets`);
      
      return {
        newTickets: result.upsertedCount,
        updatedTickets: result.modifiedCount,
        totalProcessed: tickets.length
      };

    } catch (error) {
      console.error('❌ Real-time sync failed:', error);
      return { newTickets: 0, updatedTickets: 0, error: error.message };
    }
  }

  // Format date for Jira JQL queries
  formatDateForJira(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // Get sync status
  getSyncStatus() {
    return {
      isInitialSyncComplete: this.isInitialSyncComplete,
      lastSyncTime: this.lastSyncTime,
      syncInProgress: this.syncInProgress,
      mongoStatus: mongoService.getConnectionStatus()
    };
  }

  async getDashboardData() {
    try {
      // Get storage service
      const { mongoService: mongo, Ticket: TicketModel } = await initializeMongoDB();
      await mongo.connect();

      // Get total count
      const totalTickets = await TicketModel.countDocuments({ isActive: true });

      // Get status counts
      const statusCounts = await TicketModel.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$status', count: { $sum: 1 } } }
      ]);

      // Get priority counts
      const priorityCounts = await TicketModel.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$priority', count: { $sum: 1 } } }
      ]);

      // Get organization counts
      const organizationCounts = await TicketModel.aggregate([
        { $match: { isActive: true } },
        { $group: { _id: '$organization', count: { $sum: 1 } } }
      ]);

      // Get recent tickets (last 1000)
      const recentTickets = await TicketModel.find({ isActive: true })
        .sort({ created: -1 })
        .limit(1000)
        .lean();

      // Get tickets created today
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const ticketsToday = await TicketModel.countDocuments({
        isActive: true,
        created: { $gte: today }
      });

      // Transform aggregation results to match expected format
      const statusData = {};
      statusCounts.forEach(item => {
        statusData[item._id] = item.count;
      });

      const priorityData = {};
      priorityCounts.forEach(item => {
        priorityData[item._id] = item.count;
      });

      const organizationData = {};
      organizationCounts.forEach(item => {
        organizationData[item._id] = item.count;
      });

      return {
        status: statusData,
        priority: priorityData,
        organizations: organizationData,
        allTickets: recentTickets,
        recentTickets: recentTickets.slice(0, 100),
        stats: {
          totalTickets,
          ticketsToday,
          recentTickets: recentTickets.length
        },
        metadata: {
          totalTickets,
          actualTickets: totalTickets,
          lastUpdated: new Date().toLocaleString(),
          source: 'MongoDB',
          isRealTimeUpdate: false
        }
      };

    } catch (error) {
      console.error('❌ Error getting dashboard data from MongoDB:', error);
      throw error;
    }
  }

  // Get all tickets with pagination and filtering
  async getAllTickets(options = {}) {
    try {
      // Get storage service
      const { mongoService: mongo, Ticket: TicketModel } = await initializeMongoDB();
      await mongo.connect();

      const {
        page = 0,
        limit = 1000,
        status,
        priority,
        organization,
        project,
        assignee,
        search
      } = options;

      // Build filter
      const filter = { isActive: true };

      if (status) filter.status = status;
      if (priority) filter.priority = priority;
      if (organization) filter.organization = organization;
      if (project) filter.project = project;
      if (assignee) filter.assignee = assignee;
      if (search) {
        filter.$or = [
          { summary: { $regex: search, $options: 'i' } },
          { key: { $regex: search, $options: 'i' } },
          { description: { $regex: search, $options: 'i' } }
        ];
      }

      // Get total count for pagination
      const totalCount = await TicketModel.countDocuments(filter);

      // Get tickets
      const tickets = await TicketModel.find(filter)
        .sort({ created: -1 })
        .skip(page * limit)
        .limit(limit)
        .lean();

      return {
        tickets,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      };

    } catch (error) {
      console.error('❌ Error getting tickets from MongoDB:', error);
      throw error;
    }
  }
}

// Create singleton instance
const ticketSyncService = new TicketSyncService();

export default ticketSyncService;
