import React, { useState, useEffect } from 'react';
import {
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Box,
  Chip,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Link,
  Tooltip,
  CircularProgress
} from '@mui/material';
import { Refresh as RefreshIcon } from '@mui/icons-material';
import optimizedJiraApi from '../services/optimizedJiraApi';
import { loadDashboardConfig } from '../config/enhancedDashboardConfig';

const TicketsTable = ({ allTickets = [], initialFilters = {}, onClearFilters }) => {
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(25);
  const [filters, setFilters] = useState({
    project: initialFilters.project || '',
    status: initialFilters.status || '',
    priority: initialFilters.priority || '',
    assignee: initialFilters.assignee || ''
  });
  const [availableFilters, setAvailableFilters] = useState({
    projects: [],
    statuses: [],
    priorities: [],
    assignees: []
  });

  const processTickets = () => {
    if (!allTickets || allTickets.length === 0) {
      setFilteredTickets([]);
      setAvailableFilters({
        projects: [],
        statuses: [],
        priorities: [],
        assignees: []
      });
      return;
    }

    // Get current configuration for display fields
    const currentConfig = loadDashboardConfig();
    const displayFields = currentConfig.displayFields || [];

    // Transform Jira tickets to table format (support both full Jira issues and flattened Mongo docs)
    const transformedTickets = allTickets.map(issue => {
      const fields = issue.fields || {};

      // Helper: extract Organizations from customfield_10002 (can be array/object/string)
      const extractOrganization = () => {
        const orgField = fields.customfield_10002 ?? issue.customfield_10002;
        if (Array.isArray(orgField) && orgField.length > 0) {
          const first = orgField[0];
          if (typeof first === 'string') return first;
          return first.name || first.value || first.displayName || first.id || 'N/A';
        }
        if (typeof orgField === 'string' && orgField.trim()) return orgField.trim();
        if (orgField && typeof orgField === 'object') {
          return orgField.name || orgField.value || orgField.displayName || 'N/A';
        }
        return issue.organization || 'N/A';
      };

      // Normalize summary to plain text
      const rawSummary = issue.summary || fields.summary;
      let normalizedSummary = 'No summary';
      if (typeof rawSummary === 'string' && rawSummary.trim()) {
        normalizedSummary = rawSummary;
      } else if (rawSummary && typeof rawSummary === 'object') {
        if (typeof rawSummary.text === 'string') normalizedSummary = rawSummary.text;
        else if (Array.isArray(rawSummary.content) && rawSummary.content[0]?.content?.[0]?.text) {
          normalizedSummary = rawSummary.content[0].content[0].text;
        }
      }

      const baseTicket = {
        key: issue.key,
        summary: normalizedSummary,
        status: issue.status || (fields.status && fields.status.name) || 'Unknown',
        priority: issue.priority || (fields.priority && fields.priority.name) || 'None',
        assignee: issue.assignee || (fields.assignee && (fields.assignee.displayName || fields.assignee.name)) || 'Unassigned',
        project: issue.project || (fields.project && (fields.project.name || fields.project.key)) || 'Unknown',
        created: (issue.created || fields.created) ? new Date(issue.created || fields.created).toLocaleDateString() : 'Unknown',
        updated: (issue.updated || fields.updated) ? new Date(issue.updated || fields.updated).toLocaleDateString() : 'Unknown',
        description: (typeof (issue.description || fields.description) === 'string') ? (issue.description || fields.description) : '',
        organization: extractOrganization()
      };

      // Add additional fields based on configuration
      if (displayFields.includes('reporter')) {
        baseTicket.reporter = (fields.reporter && (fields.reporter.displayName || fields.reporter.name)) || 'Unknown';
      }
      if (displayFields.includes('components')) {
        baseTicket.components = (fields.components && fields.components.map(c => c.name).join(', ')) || 'None';
      }
      if (displayFields.includes('labels')) {
        baseTicket.labels = (fields.labels && fields.labels.join(', ')) || 'None';
      }
      if (displayFields.includes('customfield_10000')) {
        baseTicket.storyPoints = fields.customfield_10000 || 'None';
      }
      if (displayFields.includes('customfield_10001')) {
        baseTicket.epicLink = fields.customfield_10001 || 'None';
      }
      if (displayFields.includes('customfield_10002')) {
        // In this Jira, customfield_10002 is Organizations
        baseTicket.organization = baseTicket.organization;
      }

      return baseTicket;
    });

    // Apply filters
    let filtered = transformedTickets;

    if (filters.project) {
      filtered = filtered.filter(ticket =>
        ticket.project.toLowerCase().includes(filters.project.toLowerCase())
      );
    }

    if (filters.status) {
      filtered = filtered.filter(ticket =>
        ticket.status.toLowerCase().includes(filters.status.toLowerCase())
      );
    }

    if (filters.priority) {
      filtered = filtered.filter(ticket =>
        ticket.priority.toLowerCase().includes(filters.priority.toLowerCase())
      );
    }

    if (filters.assignee) {
      filtered = filtered.filter(ticket =>
        ticket.assignee.toLowerCase().includes(filters.assignee.toLowerCase())
      );
    }

    setFilteredTickets(filtered);

    // Update available filters from all tickets
    const projects = [...new Set(transformedTickets.map(t => t.project))].sort();
    const statuses = [...new Set(transformedTickets.map(t => t.status))].sort();
    const priorities = [...new Set(transformedTickets.map(t => t.priority))].sort();
    const assignees = [...new Set(transformedTickets.map(t => t.assignee))].sort();

    setAvailableFilters({
      projects,
      statuses,
      priorities,
      assignees
    });
  };

  // Process tickets when allTickets or filters change
  useEffect(() => {
    processTickets();
  }, [allTickets, filters]);

  // Update filters when initialFilters change
  useEffect(() => {
    setFilters({
      project: initialFilters.project || '',
      status: initialFilters.status || '',
      priority: initialFilters.priority || '',
      assignee: initialFilters.assignee || ''
    });
    setPage(0); // Reset to first page when filters change
  }, [initialFilters]);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setPageSize(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterChange = (filterName, value) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value
    }));
    setPage(0); // Reset to first page when filtering
  };

  const clearFilters = () => {
    setFilters({
      project: '',
      status: '',
      priority: '',
      assignee: ''
    });
    setPage(0);
    // Also call parent's clear function if provided
    if (onClearFilters) {
      onClearFilters();
    }
  };

  const getStatusColor = (status) => {
    const statusColors = {
      'Open': '#5e72e4',
      'In Progress': '#11cdef',
      'Waiting for customer': '#8965e0',
      'Resolved': '#2dce89',
      'Closed': '#2dce89',
      'En attente du fournissuer': '#fb6340',
      'Escalated': '#f5365c'
    };
    return statusColors[status] || '#6c757d';
  };

  const getPriorityColor = (priority) => {
    const priorityColors = {
      'Urgent': '#dc3545',
      'Moyen': '#fd7e14',
      'Faible': '#6c757d',
      'Très faible': '#6c757d'
    };
    return priorityColors[priority] || '#6c757d';
  };

  const getOrganizationColor = (organization) => {
    const organizationColors = {
      'STORE': '#28a745',
      'IT Support': '#007bff',
      'Operations': '#ffc107',
      'Maintenance': '#dc3545',
      'Other': '#6c757d'
    };
    return organizationColors[organization] || '#6c757d';
  };

  // Get current configuration for display fields
  const currentConfig = loadDashboardConfig();
  const displayFields = currentConfig.displayFields || [];

  // Define column headers based on configuration
  const getTableHeaders = () => {
    const baseHeaders = [
      { key: 'key', label: 'Key' },
      { key: 'summary', label: 'Summary' },
      { key: 'status', label: 'Status' },
      { key: 'priority', label: 'Priority' },
      { key: 'assignee', label: 'Assignee' },
      { key: 'project', label: 'Project' },
      { key: 'organization', label: 'Organization' },
      { key: 'created', label: 'Created' },
      { key: 'updated', label: 'Updated' }
    ];

    const additionalHeaders = [];
    if (displayFields.includes('reporter')) {
      additionalHeaders.push({ key: 'reporter', label: 'Reporter' });
    }
    if (displayFields.includes('components')) {
      additionalHeaders.push({ key: 'components', label: 'Components' });
    }
    if (displayFields.includes('labels')) {
      additionalHeaders.push({ key: 'labels', label: 'Labels' });
    }
    if (displayFields.includes('customfield_10000')) {
      additionalHeaders.push({ key: 'storyPoints', label: 'Story Points' });
    }
    if (displayFields.includes('customfield_10001')) {
      additionalHeaders.push({ key: 'epicLink', label: 'Epic Link' });
    }
    if (displayFields.includes('customfield_10002')) {
      additionalHeaders.push({ key: 'sprint', label: 'Sprint' });
    }

    return [...baseHeaders, ...additionalHeaders];
  };

  // Render cell content based on field type
  const renderCellContent = (ticket, fieldKey) => {
    switch (fieldKey) {
      case 'key':
        return (
          <Link
            href={`https://ma-mcd.atlassian.net/browse/${ticket.key}`}
            target="_blank"
            rel="noopener noreferrer"
            sx={{ fontWeight: 'bold', textDecoration: 'none' }}
          >
            {ticket.key}
          </Link>
        );
      case 'summary':
        return (
          <Tooltip title={ticket.description} arrow>
            <Typography variant="body2" sx={{
              maxWidth: 300,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}>
              {ticket.summary}
            </Typography>
          </Tooltip>
        );
      case 'status':
        return (
          <Chip
            label={ticket.status}
            size="small"
            sx={{
              backgroundColor: getStatusColor(ticket.status),
              color: 'white',
              fontWeight: 'bold'
            }}
          />
        );
      case 'priority':
        return (
          <Chip
            label={ticket.priority}
            size="small"
            sx={{
              backgroundColor: getPriorityColor(ticket.priority),
              color: 'white',
              fontWeight: 'bold'
            }}
          />
        );
      case 'project':
        return (
          <Chip
            label={ticket.project}
            size="small"
            variant="outlined"
          />
        );
      case 'organization':
        return (
          <Chip
            label={ticket.organization}
            size="small"
            sx={{
              backgroundColor: getOrganizationColor(ticket.organization),
              color: 'white',
              fontWeight: 'bold'
            }}
          />
        );
      case 'components':
        return ticket.components ? (
          <Chip
            label={ticket.components}
            size="small"
            variant="outlined"
            color="primary"
          />
        ) : 'None';
      case 'labels':
        return ticket.labels ? (
          <Chip
            label={ticket.labels}
            size="small"
            variant="outlined"
            color="secondary"
          />
        ) : 'None';
      case 'storyPoints':
        return ticket.storyPoints || 'None';
      default:
        return ticket[fieldKey] || 'N/A';
    }
  };

  return (
    <Paper sx={{ p: 3, mt: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h5" fontWeight="bold">
          All Tickets ({filteredTickets.length.toLocaleString()})
        </Typography>
      </Box>

      {/* Filters */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Project</InputLabel>
            <Select
              value={filters.project}
              onChange={(e) => handleFilterChange('project', e.target.value)}
              label="Project"
            >
              <MenuItem value="">All Projects</MenuItem>
              {availableFilters.projects.map(project => (
                <MenuItem key={project} value={project}>{project}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              label="Status"
            >
              <MenuItem value="">All Statuses</MenuItem>
              {availableFilters.statuses.map(status => (
                <MenuItem key={status} value={status}>{status}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Priority</InputLabel>
            <Select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              label="Priority"
            >
              <MenuItem value="">All Priorities</MenuItem>
              {availableFilters.priorities.map(priority => (
                <MenuItem key={priority} value={priority}>{priority}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <FormControl fullWidth size="small">
            <InputLabel>Assignee</InputLabel>
            <Select
              value={filters.assignee}
              onChange={(e) => handleFilterChange('assignee', e.target.value)}
              label="Assignee"
            >
              <MenuItem value="">All Assignees</MenuItem>
              {availableFilters.assignees.map(assignee => (
                <MenuItem key={assignee} value={assignee}>{assignee}</MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <Button
            variant="outlined"
            onClick={clearFilters}
            fullWidth
            sx={{ height: '40px' }}
          >
            Clear Filters
          </Button>
        </Grid>
      </Grid>

      {/* Enhanced Table with better navigation */}
      <TableContainer component={Paper} sx={{ maxHeight: 800, overflow: 'auto' }}>
        <Table stickyHeader size="small">
          <TableHead>
            <TableRow>
              {getTableHeaders().map(header => (
                <TableCell key={header.key}>
                  <strong>{header.label}</strong>
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={getTableHeaders().length} align="center" sx={{ py: 4 }}>
                  <CircularProgress />
                </TableCell>
              </TableRow>
            ) : filteredTickets.length === 0 ? (
              <TableRow>
                <TableCell colSpan={getTableHeaders().length} align="center" sx={{ py: 4 }}>
                  No tickets found
                </TableCell>
              </TableRow>
            ) : (
              filteredTickets
                .slice(page * pageSize, page * pageSize + pageSize)
                .map((ticket) => (
                <TableRow key={ticket.key} hover>
                  {getTableHeaders().map(header => (
                    <TableCell key={header.key}>
                      {renderCellContent(ticket, header.key)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Enhanced Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2, p: 2, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          Showing {page * pageSize + 1}-{Math.min((page + 1) * pageSize, filteredTickets.length)} of {filteredTickets.length.toLocaleString()} tickets
        </Typography>
        <TablePagination
          component="div"
          count={filteredTickets.length}
          page={page}
          onPageChange={handleChangePage}
          rowsPerPage={pageSize}
          onRowsPerPageChange={handleChangeRowsPerPage}
          rowsPerPageOptions={[10, 25, 50, 100, 200]}
          sx={{
            '& .MuiTablePagination-toolbar': { minHeight: 'auto' },
            '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
              fontSize: '0.875rem'
            }
          }}
          labelRowsPerPage="Rows:"
          labelDisplayedRows={({ from, to, count }) =>
            `${from}-${to} of ${count !== -1 ? count.toLocaleString() : `more than ${to}`}`
          }
        />
      </Box>
    </Paper>
  );
};

export default TicketsTable;
