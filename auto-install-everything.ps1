# Jira Dashboard Complete Auto-Installer
# This script automatically installs and configures everything needed

param(
    [switch]$InstallNodeJS,
    [switch]$Force
)

# Require administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInR<PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as Administrator." -ForegroundColor Red
    Write-Host "Right-click on the script and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Jira Dashboard Complete Auto-Installer" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""
Write-Host "This will automatically install and configure:" -ForegroundColor Yellow
Write-Host "  ✅ Node.js (if needed)" -ForegroundColor Gray
Write-Host "  ✅ MongoDB (local installation)" -ForegroundColor Gray
Write-Host "  ✅ Application dependencies" -ForegroundColor Gray
Write-Host "  ✅ Production build" -ForegroundColor Gray
Write-Host "  ✅ Windows Service auto-start" -ForegroundColor Gray
Write-Host "  ✅ Full screen dashboard" -ForegroundColor Gray
Write-Host ""

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = $scriptDir
$logFile = Join-Path $projectDir "logs\auto-install.log"
$logDir = Split-Path -Parent $logFile

# Create logs directory
if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

function Write-Log {
    param($Message, $Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host $logMessage -ForegroundColor $Color
    Add-Content -Path $logFile -Value $logMessage
}

function Test-InternetConnection {
    Write-Log "🌐 Testing internet connection..." "Yellow"
    try {
        $response = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet
        if ($response) {
            Write-Log "✅ Internet connection available" "Green"
            return $true
        }
    }
    catch {
        Write-Log "❌ No internet connection available" "Red"
        return $false
    }
    return $false
}

function Install-NodeJS {
    Write-Log "📦 Installing Node.js..." "Yellow"
    
    # Check if Node.js is already installed
    try {
        $nodeVersion = & node --version 2>$null
        if ($nodeVersion) {
            Write-Log "✅ Node.js already installed: $nodeVersion" "Green"
            return $true
        }
    }
    catch {
        # Continue with installation
    }
    
    # Download and install Node.js
    $nodeUrl = "https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi"
    $nodeInstaller = Join-Path $env:TEMP "nodejs-installer.msi"
    
    try {
        Write-Log "⬇️ Downloading Node.js..." "Yellow"
        Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
        
        Write-Log "🔧 Installing Node.js..." "Yellow"
        Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", "`"$nodeInstaller`"", "/quiet", "/norestart" -Wait
        
        # Refresh environment variables
        $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
        
        # Verify installation
        Start-Sleep -Seconds 5
        $nodeVersion = & node --version 2>$null
        if ($nodeVersion) {
            Write-Log "✅ Node.js installed successfully: $nodeVersion" "Green"
            Remove-Item $nodeInstaller -Force -ErrorAction SilentlyContinue
            return $true
        } else {
            Write-Log "❌ Node.js installation verification failed" "Red"
            return $false
        }
    }
    catch {
        Write-Log "❌ Failed to install Node.js: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Install-Dependencies {
    Write-Log "📦 Installing application dependencies..." "Yellow"
    
    Set-Location $projectDir
    
    try {
        # Clean install
        if (Test-Path "node_modules") {
            Write-Log "🧹 Cleaning existing node_modules..." "Yellow"
            Remove-Item "node_modules" -Recurse -Force
        }
        
        if (Test-Path "package-lock.json") {
            Remove-Item "package-lock.json" -Force
        }
        
        # Install dependencies
        Write-Log "⬇️ Installing dependencies..." "Yellow"
        $installProcess = Start-Process -FilePath "npm" -ArgumentList "install" -Wait -PassThru -WindowStyle Hidden
        
        if ($installProcess.ExitCode -eq 0) {
            Write-Log "✅ Dependencies installed successfully" "Green"
            return $true
        } else {
            Write-Log "❌ Failed to install dependencies (Exit code: $($installProcess.ExitCode))" "Red"
            return $false
        }
    }
    catch {
        Write-Log "❌ Failed to install dependencies: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Build-Production {
    Write-Log "🔨 Building production version..." "Yellow"
    
    try {
        $buildProcess = Start-Process -FilePath "npm" -ArgumentList "run", "build" -Wait -PassThru -WindowStyle Hidden
        
        if ($buildProcess.ExitCode -eq 0) {
            Write-Log "✅ Production build completed successfully" "Green"
            return $true
        } else {
            Write-Log "❌ Production build failed (Exit code: $($buildProcess.ExitCode))" "Red"
            return $false
        }
    }
    catch {
        Write-Log "❌ Failed to build production version: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Install-MongoDB {
    Write-Log "🍃 Installing and configuring MongoDB..." "Yellow"
    
    try {
        $mongoScript = Join-Path $projectDir "scripts\auto-install-mongodb.ps1"
        & powershell.exe -ExecutionPolicy Bypass -File $mongoScript
        Write-Log "✅ MongoDB installation completed" "Green"
        return $true
    }
    catch {
        Write-Log "❌ MongoDB installation failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Install-WindowsService {
    Write-Log "🔧 Installing Windows Service..." "Yellow"
    
    try {
        $serviceScript = Join-Path $projectDir "scripts\install-windows-service.ps1"
        & powershell.exe -ExecutionPolicy Bypass -File $serviceScript -Install
        Write-Log "✅ Windows Service installed successfully" "Green"
        return $true
    }
    catch {
        Write-Log "❌ Windows Service installation failed: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Test-Installation {
    Write-Log "🧪 Testing installation..." "Yellow"
    
    # Test Node.js
    try {
        $nodeVersion = & node --version
        Write-Log "✅ Node.js: $nodeVersion" "Green"
    }
    catch {
        Write-Log "❌ Node.js test failed" "Red"
        return $false
    }
    
    # Test npm
    try {
        $npmVersion = & npm --version
        Write-Log "✅ npm: $npmVersion" "Green"
    }
    catch {
        Write-Log "❌ npm test failed" "Red"
        return $false
    }
    
    # Test MongoDB connection
    try {
        $mongoTest = Test-NetConnection -ComputerName "localhost" -Port 27017 -InformationLevel Quiet
        if ($mongoTest) {
            Write-Log "✅ MongoDB: Running on port 27017" "Green"
        } else {
            Write-Log "⚠️ MongoDB: Not responding (may start later)" "Yellow"
        }
    }
    catch {
        Write-Log "⚠️ MongoDB: Connection test failed" "Yellow"
    }
    
    # Test application files
    if (Test-Path "dist") {
        Write-Log "✅ Production build: Available" "Green"
    } else {
        Write-Log "❌ Production build: Missing" "Red"
        return $false
    }
    
    return $true
}

# Main installation process
Write-Log "🚀 Starting complete auto-installation..." "Green"

# Step 1: Check internet connection
if (-not (Test-InternetConnection)) {
    Write-Log "❌ Internet connection required for installation" "Red"
    pause
    exit 1
}

# Step 2: Install Node.js if needed
if ($InstallNodeJS -or -not (Get-Command node -ErrorAction SilentlyContinue)) {
    if (-not (Install-NodeJS)) {
        Write-Log "❌ Node.js installation failed" "Red"
        pause
        exit 1
    }
}

# Step 3: Install dependencies
if (-not (Install-Dependencies)) {
    Write-Log "❌ Dependencies installation failed" "Red"
    pause
    exit 1
}

# Step 4: Install MongoDB
if (-not (Install-MongoDB)) {
    Write-Log "❌ MongoDB installation failed" "Red"
    pause
    exit 1
}

# Step 5: Build production version
if (-not (Build-Production)) {
    Write-Log "❌ Production build failed" "Red"
    pause
    exit 1
}

# Step 6: Install Windows Service
if (-not (Install-WindowsService)) {
    Write-Log "❌ Windows Service installation failed" "Red"
    pause
    exit 1
}

# Step 7: Test everything
if (-not (Test-Installation)) {
    Write-Log "⚠️ Some tests failed, but installation may still work" "Yellow"
}

# Final success message
Write-Log "🎉 Complete auto-installation finished successfully!" "Green"
Write-Log "" "White"
Write-Log "✅ Your Jira Dashboard is now ready for production!" "Green"
Write-Log "✅ It will start automatically when Windows boots" "Green"
Write-Log "✅ Dashboard URL: http://localhost:3000" "Cyan"
Write-Log "✅ Full screen mode enabled" "Green"
Write-Log "✅ Auto-restart on crashes" "Green"
Write-Log "" "White"
Write-Log "📝 Installation log: $logFile" "Gray"
Write-Log "🔧 Manage service: setup-auto-start.bat" "Gray"
Write-Log "" "White"

# Ask if user wants to start the dashboard now
$startNow = Read-Host "Would you like to start the dashboard now? (y/n)"
if ($startNow -eq "y" -or $startNow -eq "Y") {
    Write-Log "🚀 Starting dashboard..." "Green"
    try {
        Start-Process "http://localhost:3000"
        Write-Log "✅ Dashboard opened in browser" "Green"
    }
    catch {
        Write-Log "⚠️ Please open http://localhost:3000 manually" "Yellow"
    }
}

Write-Log "🎯 Installation complete! Your dashboard is production-ready." "Green"
pause
