import React, { useState, useCallback } from 'react';
import AssigneeGroupManager from './AssigneeGroupManager';

const ConfigurationManager = ({ config, onConfigChange, theme = 'light', dashboardData, getAllAssignees }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activePreset, setActivePreset] = useState(null);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [importData, setImportData] = useState('');

  // Export configuration to JSON
  const exportConfig = useCallback(() => {
    const configData = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      config: config
    };
    
    const blob = new Blob([JSON.stringify(configData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `jira-dashboard-config-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [config]);

  // Import configuration from JSON
  const importConfig = useCallback(() => {
    try {
      const parsed = JSON.parse(importData);
      if (parsed.config) {
        onConfigChange(parsed.config);
        setShowImportDialog(false);
        setImportData('');
        alert('Configuration imported successfully!');
      } else {
        alert('Invalid configuration format');
      }
    } catch (error) {
      alert('Error parsing configuration: ' + error.message);
    }
  }, [importData, onConfigChange]);

  // Apply preset configuration
  const applyPreset = useCallback((presetId) => {
    const preset = config.presets?.find(p => p.id === presetId);
    if (preset && preset.config) {
      onConfigChange({ ...config, ...preset.config });
      setActivePreset(presetId);
    }
  }, [config, onConfigChange]);

  // Create assignee group
  const createAssigneeGroup = useCallback((name, assignees, description = '') => {
    const newGroup = {
      id: `group-${Date.now()}`,
      name,
      assignees,
      description
    };
    
    const updatedConfig = {
      ...config,
      assigneeGroups: [...(config.assigneeGroups || []), newGroup]
    };
    
    onConfigChange(updatedConfig);
  }, [config, onConfigChange]);

  // Apply assignee group to all cards
  const applyAssigneeGroup = useCallback((groupId) => {
    const group = config.assigneeGroups?.find(g => g.id === groupId);
    if (!group) return;

    const updatedConfig = { ...config };
    
    // Apply to status cards
    if (updatedConfig.statusCards) {
      updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: group.assignees
      }));
    }
    
    // Apply to priority cards
    if (updatedConfig.priorityCards) {
      updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: group.assignees
      }));
    }
    
    // Apply to organization cards
    if (updatedConfig.organizationCards) {
      updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: group.assignees
      }));
    }
    
    // Apply to charts
    if (updatedConfig.charts) {
      Object.keys(updatedConfig.charts).forEach(chartId => {
        updatedConfig.charts[chartId] = {
          ...updatedConfig.charts[chartId],
          assigneeFilter: 'specific',
          selectedAssignees: group.assignees
        };
      });
    }
    
    onConfigChange(updatedConfig);
  }, [config, onConfigChange]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    if (confirm('Are you sure you want to reset all configurations to defaults? This cannot be undone.')) {
      // This would reset to the default config structure
      const defaultConfig = {
        charts: {
          organizationChart: {
            id: 'organizationChart',
            title: 'Tickets by Restaurant',
            type: 'horizontal-bar',
            visible: true,
            position: { row: 1, col: 1 },
            size: { width: 1, height: 1 },
            dataSource: 'organizations',
            limit: 10,
            colorScheme: 'blue',
            showValues: true,
            dateFilter: 'all',
            assigneeFilter: 'all',
            selectedAssignees: []
          },
          weeklyChart: {
            id: 'weeklyChart',
            title: 'Tickets by Day (Last 7 Days)',
            type: 'line',
            visible: true,
            position: { row: 1, col: 2 },
            size: { width: 1, height: 1 },
            dataSource: 'created',
            period: 7,
            colorScheme: 'green',
            showFill: true,
            dateFilter: 'all',
            assigneeFilter: 'all',
            selectedAssignees: []
          }
        },
        assigneeGroups: [],
        presets: config.presets || [],
        statusCards: config.statusCards || [],
        priorityCards: config.priorityCards || [],
        organizationCards: config.organizationCards || []
      };
      
      onConfigChange(defaultConfig);
      setActivePreset(null);
    }
  }, [config, onConfigChange]);

  const filteredStatuses = (config.statusCards || []).filter(card =>
    card.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    card.jiraStatuses?.some(status => status.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredPriorities = (config.priorityCards || []).filter(card =>
    card.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    card.jiraPriorities?.some(priority => priority.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div style={{
      padding: '20px',
      backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',
      borderRadius: '12px',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`
    }}>
      <h3 style={{
        margin: '0 0 20px 0',
        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        🚀 Intelligent Configuration Manager
      </h3>

      {/* Quick Actions */}
      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }}>
        <button
          onClick={exportConfig}
          style={{
            padding: '8px 16px',
            backgroundColor: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          📤 Export Config
        </button>
        
        <button
          onClick={() => setShowImportDialog(true)}
          style={{
            padding: '8px 16px',
            backgroundColor: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          📥 Import Config
        </button>
        
        <button
          onClick={resetToDefaults}
          style={{
            padding: '8px 16px',
            backgroundColor: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          🔄 Reset to Defaults
        </button>
      </div>

      {/* Search */}
      <div style={{ marginBottom: '20px' }}>
        <input
          type="text"
          placeholder="🔍 Search statuses, priorities, organizations..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '10px 12px',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '6px',
            backgroundColor: theme === 'dark' ? '#374151' : 'white',
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Presets */}
      <div style={{ marginBottom: '24px' }}>
        <h4 style={{
          margin: '0 0 12px 0',
          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
          fontSize: '16px'
        }}>
          📋 Quick Presets
        </h4>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          {(config.presets || []).map(preset => (
            <button
              key={preset.id}
              onClick={() => applyPreset(preset.id)}
              style={{
                padding: '6px 12px',
                backgroundColor: activePreset === preset.id ? '#3b82f6' : (theme === 'dark' ? '#4b5563' : '#f3f4f6'),
                color: activePreset === preset.id ? 'white' : (theme === 'dark' ? '#e5e7eb' : '#1f2937'),
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {preset.name}
            </button>
          ))}
        </div>
      </div>

      {/* Enhanced Assignee Groups Management */}
      <AssigneeGroupManager
        config={config}
        onConfigChange={onConfigChange}
        theme={theme}
        dashboardData={dashboardData}
        getAllAssignees={getAllAssignees}
      />

      {/* Import Dialog */}
      {showImportDialog && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }}>
          <div style={{
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            padding: '24px',
            borderRadius: '12px',
            maxWidth: '500px',
            width: '90%'
          }}>
            <h3 style={{
              margin: '0 0 16px 0',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
            }}>
              Import Configuration
            </h3>
            <textarea
              value={importData}
              onChange={(e) => setImportData(e.target.value)}
              placeholder="Paste your configuration JSON here..."
              style={{
                width: '100%',
                height: '200px',
                padding: '12px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '6px',
                backgroundColor: theme === 'dark' ? '#374151' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px',
                fontFamily: 'monospace',
                resize: 'vertical'
              }}
            />
            <div style={{
              display: 'flex',
              gap: '12px',
              marginTop: '16px',
              justifyContent: 'flex-end'
            }}>
              <button
                onClick={() => setShowImportDialog(false)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: theme === 'dark' ? '#4b5563' : '#f3f4f6',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={importConfig}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer'
                }}
              >
                Import
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ConfigurationManager;
