import React, { useState, useCallback, useMemo, memo } from 'react';
import AssigneeGroupManager from './AssigneeGroupManager';

// Memoized card configuration component to prevent unnecessary re-renders
const CardConfigItem = memo(({ 
  card, 
  cardType, 
  theme, 
  updateCardConfig, 
  getAllAssignees,
  availableStatuses,
  availablePriorities,
  availableOrganizations 
}) => {
  const handleUpdate = useCallback((updates) => {
    updateCardConfig(cardType, card.id, updates);
  }, [updateCardConfig, cardType, card.id]);

  return (
    <div style={{
      padding: '16px',
      backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
      borderRadius: '8px',
      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`,
      marginBottom: '12px'
    }}>
      {/* Card Header */}
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
        <input
          type="checkbox"
          checked={card.visible !== false}
          onChange={(e) => handleUpdate({ visible: e.target.checked })}
          style={{ transform: 'scale(1.2)' }}
        />
        <input
          type="text"
          placeholder="Card Title"
          value={card.title || ''}
          onChange={(e) => handleUpdate({ title: e.target.value })}
          style={{
            flex: 1,
            padding: '8px 12px',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '6px',
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontSize: '14px'
          }}
        />
        <input
          type="color"
          value={card.color || '#3b82f6'}
          onChange={(e) => handleUpdate({ color: e.target.value })}
          style={{
            width: '40px',
            height: '32px',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        />
      </div>

      {/* Card Configuration Grid */}
      <div style={{ display: 'grid', gap: '12px', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' }}>
        {/* Status/Priority/Organization Selection */}
        {cardType === 'status' && (
          <div>
            <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
              Jira Statuses
            </label>
            <select
              multiple
              value={card.jiraStatuses || []}
              onChange={(e) => handleUpdate({ 
                jiraStatuses: Array.from(e.target.selectedOptions, option => option.value) 
              })}
              style={{
                width: '100%',
                minHeight: '80px',
                padding: '6px 8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '4px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px'
              }}
            >
              {availableStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        )}

        {cardType === 'priority' && (
          <div>
            <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
              Jira Priorities
            </label>
            <select
              multiple
              value={card.jiraPriorities || []}
              onChange={(e) => handleUpdate({ 
                jiraPriorities: Array.from(e.target.selectedOptions, option => option.value) 
              })}
              style={{
                width: '100%',
                minHeight: '80px',
                padding: '6px 8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '4px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px'
              }}
            >
              {availablePriorities.map(priority => (
                <option key={priority} value={priority}>{priority}</option>
              ))}
            </select>
          </div>
        )}

        {cardType === 'organization' && (
          <div>
            <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
              Organizations
            </label>
            <select
              multiple
              value={card.organizationNames || []}
              onChange={(e) => handleUpdate({ 
                organizationNames: Array.from(e.target.selectedOptions, option => option.value) 
              })}
              style={{
                width: '100%',
                minHeight: '80px',
                padding: '6px 8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '4px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px'
              }}
            >
              {availableOrganizations.map(org => (
                <option key={org} value={org}>{org}</option>
              ))}
            </select>
          </div>
        )}

        {/* Assignee Filter */}
        <div>
          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
            Assignee Filter
          </label>
          <select
            value={card.assigneeFilter || 'all'}
            onChange={(e) => handleUpdate({
              assigneeFilter: e.target.value,
              selectedAssignees: e.target.value !== 'specific' ? [] : card.selectedAssignees
            })}
            style={{
              width: '100%',
              padding: '6px 8px',
              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
              borderRadius: '4px',
              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
              fontSize: '12px'
            }}
          >
            <option value="all">All Assignees</option>
            <option value="unassigned">Unassigned Only</option>
            <option value="specific">Specific Assignees</option>
          </select>
        </div>

        {/* Date Filter */}
        <div>
          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
            Date Filter
          </label>
          <select
            value={card.dateFilter || 'all'}
            onChange={(e) => handleUpdate({ dateFilter: e.target.value })}
            style={{
              width: '100%',
              padding: '6px 8px',
              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
              borderRadius: '4px',
              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
              fontSize: '12px'
            }}
          >
            <option value="all">All Time</option>
            <option value="last7days">Last 7 Days</option>
            <option value="last30days">Last 30 Days</option>
            <option value="last90days">Last 90 Days</option>
            <option value="thisyear">This Year</option>
            <option value="lastyear">Last Year</option>
            <option value="custom">Custom Range</option>
          </select>
        </div>
      </div>

      {/* Specific Assignees Selection */}
      {card.assigneeFilter === 'specific' && (
        <div style={{ marginTop: '12px' }}>
          <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
            Select Assignees
          </label>
          <div style={{
            maxHeight: '120px',
            overflowY: 'auto',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '4px',
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            padding: '4px'
          }}>
            {getAllAssignees().map(assignee => (
              <label key={assignee.key} style={{
                display: 'flex',
                alignItems: 'center',
                padding: '4px 8px',
                cursor: 'pointer',
                fontSize: '11px',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                borderRadius: '2px'
              }}>
                <input
                  type="checkbox"
                  checked={(card.selectedAssignees || []).includes(assignee.displayName)}
                  onChange={(e) => {
                    const currentAssignees = card.selectedAssignees || [];
                    const newAssignees = e.target.checked
                      ? [...currentAssignees, assignee.displayName]
                      : currentAssignees.filter(name => name !== assignee.displayName);
                    handleUpdate({ selectedAssignees: newAssignees });
                  }}
                  style={{ marginRight: '8px', transform: 'scale(0.9)' }}
                />
                <div>
                  <div style={{ fontWeight: '500' }}>{assignee.displayName}</div>
                  {assignee.emailAddress && (
                    <div style={{ fontSize: '10px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                      {assignee.emailAddress}
                    </div>
                  )}
                </div>
              </label>
            ))}
          </div>
        </div>
      )}

      {/* Custom Date Range */}
      {card.dateFilter === 'custom' && (
        <div style={{ marginTop: '12px', display: 'grid', gap: '8px', gridTemplateColumns: '1fr 1fr' }}>
          <div>
            <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
              From Date
            </label>
            <input
              type="date"
              value={card.customDateFrom || ''}
              onChange={(e) => handleUpdate({ customDateFrom: e.target.value })}
              style={{
                width: '100%',
                padding: '6px 8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '4px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px'
              }}
            />
          </div>
          <div>
            <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
              To Date
            </label>
            <input
              type="date"
              value={card.customDateTo || ''}
              onChange={(e) => handleUpdate({ customDateTo: e.target.value })}
              style={{
                width: '100%',
                padding: '6px 8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '4px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '12px'
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
});

const OptimizedConfigurationManager = ({ 
  config, 
  onConfigChange, 
  theme = 'light', 
  dashboardData, 
  activeConfigTab,
  setActiveConfigTab,
  updateCardConfig,
  addNewCard,
  getAllAssignees 
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  // Memoized data extraction to prevent recalculation
  const { availableStatuses, availablePriorities, availableOrganizations } = useMemo(() => {
    const tickets = dashboardData.tickets || [];
    
    const statuses = [...new Set(tickets.map(ticket => 
      ticket.status || ticket.fields?.status?.name || ''
    ).filter(Boolean))].sort();
    
    const priorities = [...new Set(tickets.map(ticket => 
      ticket.priority || ticket.fields?.priority?.name || ''
    ).filter(Boolean))].sort();
    
    const organizations = [...new Set(tickets.map(ticket => {
      const org = ticket.fields?.customfield_10002?.value || 
                  ticket.fields?.customfield_10002 || 
                  ticket.organization || 
                  'Unknown';
      return typeof org === 'string' ? org : org?.value || 'Unknown';
    }).filter(org => org && org !== 'Unknown'))].sort();

    return { 
      availableStatuses: statuses, 
      availablePriorities: priorities, 
      availableOrganizations: organizations 
    };
  }, [dashboardData.tickets]);

  // Filtered cards based on search
  const filteredStatusCards = useMemo(() => 
    (config.statusCards || []).filter(card =>
      card.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.jiraStatuses?.some(status => status.toLowerCase().includes(searchTerm.toLowerCase()))
    ), [config.statusCards, searchTerm]
  );

  const filteredPriorityCards = useMemo(() => 
    (config.priorityCards || []).filter(card =>
      card.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.jiraPriorities?.some(priority => priority.toLowerCase().includes(searchTerm.toLowerCase()))
    ), [config.priorityCards, searchTerm]
  );

  const filteredOrganizationCards = useMemo(() => 
    (config.organizationCards || []).filter(card =>
      card.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      card.organizationNames?.some(org => org.toLowerCase().includes(searchTerm.toLowerCase()))
    ), [config.organizationCards, searchTerm]
  );

  return (
    <div style={{
      padding: '20px',
      backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',
      borderRadius: '12px',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`
    }}>
      <h3 style={{
        margin: '0 0 20px 0',
        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
        fontSize: '18px',
        fontWeight: '600'
      }}>
        ⚙️ Optimized Configuration Manager
      </h3>

      {/* Search */}
      <div style={{ marginBottom: '20px' }}>
        <input
          type="text"
          placeholder="🔍 Search cards..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '10px 12px',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '6px',
            backgroundColor: theme === 'dark' ? '#374151' : 'white',
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Configuration Tabs */}
      <div style={{
        display: 'flex',
        gap: '4px',
        marginBottom: '20px',
        borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`
      }}>
        {[
          { id: 'status', label: '📊 Status Cards', icon: '📊' },
          { id: 'priority', label: '⚡ Priority Cards', icon: '⚡' },
          { id: 'organization', label: '🏢 Organization Cards', icon: '🏢' },
          { id: 'general', label: '⚙️ General Settings', icon: '⚙️' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveConfigTab(tab.id)}
            style={{
              background: 'none',
              border: 'none',
              padding: '12px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: activeConfigTab === tab.id ? '600' : '400',
              color: activeConfigTab === tab.id
                ? (theme === 'dark' ? '#60a5fa' : '#2563eb')
                : (theme === 'dark' ? '#9ca3af' : '#64748b'),
              borderBottom: activeConfigTab === tab.id
                ? `2px solid ${theme === 'dark' ? '#60a5fa' : '#2563eb'}`
                : '2px solid transparent',
              transition: 'all 0.2s ease'
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div style={{ maxHeight: '60vh', overflowY: 'auto', paddingRight: '8px' }}>
        {/* Status Cards */}
        {activeConfigTab === 'status' && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '500', color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>
                Status Cards ({filteredStatusCards.length})
              </h4>
              <button
                onClick={() => addNewCard('status')}
                style={{
                  backgroundColor: '#10b981',
                  color: 'white',
                  padding: '6px 12px',
                  fontSize: '12px',
                  borderRadius: '4px',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                + Add Card
              </button>
            </div>
            
            {filteredStatusCards.map(card => (
              <CardConfigItem
                key={card.id}
                card={card}
                cardType="status"
                theme={theme}
                updateCardConfig={updateCardConfig}
                getAllAssignees={getAllAssignees}
                availableStatuses={availableStatuses}
              />
            ))}
          </div>
        )}

        {/* Priority Cards */}
        {activeConfigTab === 'priority' && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '500', color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>
                Priority Cards ({filteredPriorityCards.length})
              </h4>
              <button
                onClick={() => addNewCard('priority')}
                style={{
                  backgroundColor: '#10b981',
                  color: 'white',
                  padding: '6px 12px',
                  fontSize: '12px',
                  borderRadius: '4px',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                + Add Card
              </button>
            </div>
            
            {filteredPriorityCards.map(card => (
              <CardConfigItem
                key={card.id}
                card={card}
                cardType="priority"
                theme={theme}
                updateCardConfig={updateCardConfig}
                getAllAssignees={getAllAssignees}
                availablePriorities={availablePriorities}
              />
            ))}
          </div>
        )}

        {/* Organization Cards */}
        {activeConfigTab === 'organization' && (
          <div>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
              <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '500', color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>
                Organization Cards ({filteredOrganizationCards.length})
              </h4>
              <button
                onClick={() => addNewCard('organization')}
                style={{
                  backgroundColor: '#10b981',
                  color: 'white',
                  padding: '6px 12px',
                  fontSize: '12px',
                  borderRadius: '4px',
                  border: 'none',
                  cursor: 'pointer'
                }}
              >
                + Add Card
              </button>
            </div>
            
            {filteredOrganizationCards.map(card => (
              <CardConfigItem
                key={card.id}
                card={card}
                cardType="organization"
                theme={theme}
                updateCardConfig={updateCardConfig}
                getAllAssignees={getAllAssignees}
                availableOrganizations={availableOrganizations}
              />
            ))}
          </div>
        )}

        {/* General Settings */}
        {activeConfigTab === 'general' && (
          <AssigneeGroupManager
            config={config}
            onConfigChange={onConfigChange}
            theme={theme}
            dashboardData={dashboardData}
            getAllAssignees={getAllAssignees}
          />
        )}
      </div>
    </div>
  );
};

export default OptimizedConfigurationManager;
