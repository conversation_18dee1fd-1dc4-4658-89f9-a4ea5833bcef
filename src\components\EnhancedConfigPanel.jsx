import React, { useState, useEffect } from 'react';
import optimizedJiraApi from '../services/optimizedJiraApi.js';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Grid,
  Card,
  CardContent,
  IconButton,
  Divider,
  Chip,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  ColorLens as ColorIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon
} from '@mui/icons-material';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`config-tabpanel-${index}`}
    aria-labelledby={`config-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const ColorPicker = ({ color, onChange, label }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
    <Typography variant="body2">{label}:</Typography>
    <Box
      sx={{
        width: 30,
        height: 30,
        backgroundColor: color,
        border: '1px solid #ccc',
        borderRadius: 1,
        cursor: 'pointer'
      }}
      onClick={() => {
        const input = document.createElement('input');
        input.type = 'color';
        input.value = color;
        input.onchange = (e) => onChange(e.target.value);
        input.click();
      }}
    />
    <Typography variant="caption" color="text.secondary">{color}</Typography>
  </Box>
);

const EnhancedConfigPanel = ({ open, onClose, config, onConfigChange }) => {
  const [currentTab, setCurrentTab] = useState(0);
  const [localConfig, setLocalConfig] = useState(config);
  const [availableAssignees, setAvailableAssignees] = useState([]);
  const [loadingAssignees, setLoadingAssignees] = useState(false);

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  // Load available assignees when component opens
  useEffect(() => {
    if (open && availableAssignees.length === 0) {
      setLoadingAssignees(true);
      optimizedJiraApi.getAllAssignees()
        .then((assignees) => {
          setAvailableAssignees(assignees);
          console.log('📋 Loaded ' + assignees.length + ' assignees for filtering');
        })
        .catch((error) => {
          console.error('Error loading assignees:', error);
          setAvailableAssignees(['John Doe', 'Jane Smith', 'Bob Johnson']); // Fallback
        })
        .finally(() => {
          setLoadingAssignees(false);
        });
    }
  }, [open]);

  const handleSave = () => {
    onConfigChange(localConfig);
    onClose();
  };

  const handleReset = () => {
    setLocalConfig(config);
  };

  const addStatusCard = () => {
    const newCard = {
      id: `status_${Date.now()}`,
      title: 'New Status',
      color: '#2196f3',
      textColor: 'white',
      jiraStatuses: [],
      visible: true
    };
    setLocalConfig({
      ...localConfig,
      statusCards: [...localConfig.statusCards, newCard]
    });
  };

  const removeStatusCard = (index) => {
    const newCards = localConfig.statusCards.filter((_, i) => i !== index);
    setLocalConfig({
      ...localConfig,
      statusCards: newCards
    });
  };

  const updateStatusCard = (index, field, value) => {
    const newCards = [...localConfig.statusCards];
    newCards[index] = { ...newCards[index], [field]: value };
    setLocalConfig({
      ...localConfig,
      statusCards: newCards
    });
  };

  const addPriorityCard = () => {
    const newCard = {
      id: `priority_${Date.now()}`,
      title: 'New Priority',
      color: '#ff9800',
      textColor: 'white',
      jiraPriorities: [],
      visible: true
    };
    setLocalConfig({
      ...localConfig,
      priorityCards: [...localConfig.priorityCards, newCard]
    });
  };

  const removePriorityCard = (index) => {
    const newCards = localConfig.priorityCards.filter((_, i) => i !== index);
    setLocalConfig({
      ...localConfig,
      priorityCards: newCards
    });
  };

  const updatePriorityCard = (index, field, value) => {
    const newCards = [...localConfig.priorityCards];
    newCards[index] = { ...newCards[index], [field]: value };
    setLocalConfig({
      ...localConfig,
      priorityCards: newCards
    });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Typography variant="h5" fontWeight="bold">
          🎛️ Enhanced Dashboard Configuration
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Customize every aspect of your dashboard
        </Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange}>
            <Tab label="📊 Status Cards" />
            <Tab label="🎯 Priority Cards" />
            <Tab label="📈 Charts" />
            <Tab label="🎨 Appearance" />
            <Tab label="⚙️ General" />
          </Tabs>
        </Box>

        {/* Status Cards Configuration */}
        <TabPanel value={currentTab} index={0}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Status Cards Configuration</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addStatusCard}
              size="small"
            >
              Add Status Card
            </Button>
          </Box>
          
          <Grid container spacing={2}>
            {(localConfig.statusCards || []).map((card, index) => (
              <Grid item xs={12} md={6} key={card.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        Card {index + 1}
                      </Typography>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => updateStatusCard(index, 'visible', !card.visible)}
                          color={card.visible ? 'primary' : 'default'}
                        >
                          {card.visible ? <VisibilityIcon /> : <VisibilityOffIcon />}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => removeStatusCard(index)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                    
                    <TextField
                      fullWidth
                      label="Card Title"
                      value={card.title}
                      onChange={(e) => updateStatusCard(index, 'title', e.target.value)}
                      sx={{ mb: 2 }}
                      size="small"
                    />
                    
                    <ColorPicker
                      color={card.color}
                      onChange={(color) => updateStatusCard(index, 'color', color)}
                      label="Background Color"
                    />
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}>Jira Statuses to Include:</Typography>
                      <TextField
                        fullWidth
                        placeholder="e.g., Open, In Progress, Done"
                        value={(card.jiraStatuses && card.jiraStatuses.join(', ')) || ''}
                        onChange={(e) => updateStatusCard(index, 'jiraStatuses', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                        size="small"
                        helperText="Comma-separated list of Jira status names"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Priority Cards Configuration */}
        <TabPanel value={currentTab} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">Priority Cards Configuration</Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={addPriorityCard}
              size="small"
            >
              Add Priority Card
            </Button>
          </Box>
          
          <Grid container spacing={2}>
            {(localConfig.priorityCards || []).map((card, index) => (
              <Grid item xs={12} md={6} key={card.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1" fontWeight="bold">
                        Card {index + 1}
                      </Typography>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => updatePriorityCard(index, 'visible', !card.visible)}
                          color={card.visible ? 'primary' : 'default'}
                        >
                          {card.visible ? <VisibilityIcon /> : <VisibilityOffIcon />}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => removePriorityCard(index)}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </Box>
                    
                    <TextField
                      fullWidth
                      label="Card Title"
                      value={card.title}
                      onChange={(e) => updatePriorityCard(index, 'title', e.target.value)}
                      sx={{ mb: 2 }}
                      size="small"
                    />
                    
                    <ColorPicker
                      color={card.color}
                      onChange={(color) => updatePriorityCard(index, 'color', color)}
                      label="Background Color"
                    />
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}>Jira Priorities to Include:</Typography>
                      <TextField
                        fullWidth
                        placeholder="e.g., Urgent, High, Medium, Low"
                        value={(card.jiraPriorities && card.jiraPriorities.join(', ')) || ''}
                        onChange={(e) => updatePriorityCard(index, 'jiraPriorities', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                        size="small"
                        helperText="Comma-separated list of Jira priority names"
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        {/* Charts Configuration */}
        <TabPanel value={currentTab} index={2}>
          <Typography variant="h6" sx={{ mb: 3 }}>Charts Configuration</Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    📊 Project Distribution Chart
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={(localConfig.charts && localConfig.charts.projectChart && localConfig.charts.projectChart.enabled !== undefined) ? localConfig.charts.projectChart.enabled : true}
                        onChange={(e) => setLocalConfig({
                          ...localConfig,
                          charts: {
                            ...localConfig.charts,
                            projectChart: {
                              ...(localConfig.charts && localConfig.charts.projectChart ? localConfig.charts.projectChart : {}),
                              enabled: e.target.checked
                            }
                          }
                        })}
                      />
                    }
                    label="Show Project Chart"
                  />
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    📈 Monthly Trends Chart
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={(localConfig.charts && localConfig.charts.trendsChart && localConfig.charts.trendsChart.enabled !== undefined) ? localConfig.charts.trendsChart.enabled : true}
                        onChange={(e) => setLocalConfig({
                          ...localConfig,
                          charts: {
                            ...localConfig.charts,
                            trendsChart: {
                              ...(localConfig.charts && localConfig.charts.trendsChart ? localConfig.charts.trendsChart : {}),
                              enabled: e.target.checked
                            }
                          }
                        })}
                      />
                    }
                    label="Show Trends Chart"
                  />
                </CardContent>
              </Card>
            </Grid>

            {/* Year Filter Configuration */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    📅 Year Filter
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Filter by Year</InputLabel>
                    <Select
                      value={(localConfig.filters && localConfig.filters.year) || 'all'}
                      onChange={(e) => setLocalConfig({
                        ...localConfig,
                        filters: {
                          ...localConfig.filters,
                          year: e.target.value
                        }
                      })}
                      label="Filter by Year"
                    >
                      <MenuItem value="all">All Years</MenuItem>
                      <MenuItem value="2024">2024</MenuItem>
                      <MenuItem value="2023">2023</MenuItem>
                      <MenuItem value="2022">2022</MenuItem>
                      <MenuItem value="2021">2021</MenuItem>
                      <MenuItem value="2020">2020</MenuItem>
                    </Select>
                  </FormControl>
                  <Typography variant="body2" color="text.secondary">
                    Filter tickets by creation year
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Assignee Filter Configuration */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    👤 Assignee Filter
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Filter by Assignees</InputLabel>
                    <Select
                      multiple
                      value={(localConfig.filters && localConfig.filters.assignees) || []}
                      onChange={(e) => setLocalConfig({
                        ...localConfig,
                        filters: {
                          ...localConfig.filters,
                          assignees: e.target.value
                        }
                      })}
                      label="Filter by Assignees"
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {selected.map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      {loadingAssignees ? (
                        <MenuItem disabled>
                          <em>Loading assignees...</em>
                        </MenuItem>
                      ) : (
                        availableAssignees.map((assignee) => (
                          <MenuItem key={assignee} value={assignee}>
                            {assignee}
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                  <Typography variant="body2" color="text.secondary">
                    Show tickets only for selected assignees (empty = show all)
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Field Selection Configuration */}
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    🔧 Display Fields
                  </Typography>
                  <FormControl fullWidth sx={{ mb: 2 }}>
                    <InputLabel>Additional Fields to Show</InputLabel>
                    <Select
                      multiple
                      value={localConfig.displayFields || []}
                      onChange={(e) => setLocalConfig({
                        ...localConfig,
                        displayFields: e.target.value
                      })}
                      label="Additional Fields to Show"
                      renderValue={(selected) => selected.join(', ')}
                    >
                      <MenuItem value="assignee">Assignee</MenuItem>
                      <MenuItem value="reporter">Reporter</MenuItem>
                      <MenuItem value="components">Components</MenuItem>
                      <MenuItem value="labels">Labels</MenuItem>
                      <MenuItem value="description">Description</MenuItem>
                      <MenuItem value="customfield_10000">Story Points</MenuItem>
                      <MenuItem value="customfield_10001">Epic Link</MenuItem>
                      <MenuItem value="customfield_10002">Sprint</MenuItem>
                    </Select>
                  </FormControl>
                  <Typography variant="body2" color="text.secondary">
                    Select additional fields to display in the tickets table
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Appearance Configuration */}
        <TabPanel value={currentTab} index={3}>
          <Typography variant="h6" sx={{ mb: 3 }}>Appearance Settings</Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Dashboard Title"
                value={localConfig.title || ''}
                onChange={(e) => setLocalConfig({ ...localConfig, title: e.target.value })}
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Dashboard Subtitle"
                value={localConfig.subtitle || ''}
                onChange={(e) => setLocalConfig({ ...localConfig, subtitle: e.target.value })}
                sx={{ mb: 2 }}
              />
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Theme</InputLabel>
                <Select
                  value={localConfig.theme || 'light'}
                  onChange={(e) => setLocalConfig({ ...localConfig, theme: e.target.value })}
                  label="Theme"
                >
                  <MenuItem value="light">Light</MenuItem>
                  <MenuItem value="dark">Dark</MenuItem>
                  <MenuItem value="auto">Auto</MenuItem>
                </Select>
              </FormControl>
              
              <ColorPicker
                color={localConfig.primaryColor || '#f39c12'}
                onChange={(color) => setLocalConfig({ ...localConfig, primaryColor: color })}
                label="Primary Color"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* General Configuration */}
        <TabPanel value={currentTab} index={4}>
          <Typography variant="h6" sx={{ mb: 3 }}>General Settings</Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Auto Refresh Interval</InputLabel>
                <Select
                  value={localConfig.refreshInterval || 30}
                  onChange={(e) => setLocalConfig({ ...localConfig, refreshInterval: e.target.value })}
                  label="Auto Refresh Interval"
                >
                  <MenuItem value={10}>10 seconds</MenuItem>
                  <MenuItem value={30}>30 seconds</MenuItem>
                  <MenuItem value={60}>1 minute</MenuItem>
                  <MenuItem value={300}>5 minutes</MenuItem>
                  <MenuItem value={0}>Disabled</MenuItem>
                </Select>
              </FormControl>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Default Page Size</InputLabel>
                <Select
                  value={localConfig.defaultPageSize || 25}
                  onChange={(e) => setLocalConfig({ ...localConfig, defaultPageSize: e.target.value })}
                  label="Default Page Size"
                >
                  <MenuItem value={10}>10 rows</MenuItem>
                  <MenuItem value={25}>25 rows</MenuItem>
                  <MenuItem value={50}>50 rows</MenuItem>
                  <MenuItem value={100}>100 rows</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <FormControlLabel
                control={
                  <Switch
                    checked={localConfig.showTicketsTable ?? true}
                    onChange={(e) => setLocalConfig({ ...localConfig, showTicketsTable: e.target.checked })}
                  />
                }
                label="Show Tickets Table"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={localConfig.enableRealTimeUpdates ?? true}
                    onChange={(e) => setLocalConfig({ ...localConfig, enableRealTimeUpdates: e.target.checked })}
                  />
                }
                label="Enable Real-time Updates"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={localConfig.showMetadata ?? false}
                    onChange={(e) => setLocalConfig({ ...localConfig, showMetadata: e.target.checked })}
                  />
                }
                label="Show Debug Metadata"
              />
            </Grid>
          </Grid>
        </TabPanel>
      </DialogContent>
      
      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button onClick={handleReset} color="secondary">
          Reset to Default
        </Button>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained">
          Save Configuration
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedConfigPanel;
