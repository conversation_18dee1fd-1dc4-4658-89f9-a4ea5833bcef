var Iy=Object.defineProperty;var t0=(t,e,i)=>e in t?Iy(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i;var Z=(t,e,i)=>t0(t,typeof e!="symbol"?e+"":e,i);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const s of a)if(s.type==="childList")for(const l of s.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&n(l)}).observe(document,{childList:!0,subtree:!0});function i(a){const s={};return a.integrity&&(s.integrity=a.integrity),a.referrerPolicy&&(s.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?s.credentials="include":a.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function n(a){if(a.ep)return;a.ep=!0;const s=i(a);fetch(a.href,s)}})();function e0(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Vg={exports:{}},ur={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i0=Symbol.for("react.transitional.element"),n0=Symbol.for("react.fragment");function Gg(t,e,i){var n=null;if(i!==void 0&&(n=""+i),e.key!==void 0&&(n=""+e.key),"key"in e){i={};for(var a in e)a!=="key"&&(i[a]=e[a])}else i=e;return e=i.ref,{$$typeof:i0,type:t,key:n,ref:e!==void 0?e:null,props:i}}ur.Fragment=n0;ur.jsx=Gg;ur.jsxs=Gg;Vg.exports=ur;var u=Vg.exports,qg={exports:{}},K={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ku=Symbol.for("react.transitional.element"),a0=Symbol.for("react.portal"),s0=Symbol.for("react.fragment"),l0=Symbol.for("react.strict_mode"),o0=Symbol.for("react.profiler"),r0=Symbol.for("react.consumer"),c0=Symbol.for("react.context"),u0=Symbol.for("react.forward_ref"),d0=Symbol.for("react.suspense"),f0=Symbol.for("react.memo"),Xg=Symbol.for("react.lazy"),$f=Symbol.iterator;function h0(t){return t===null||typeof t!="object"?null:(t=$f&&t[$f]||t["@@iterator"],typeof t=="function"?t:null)}var Qg={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Zg=Object.assign,Wg={};function Ua(t,e,i){this.props=t,this.context=e,this.refs=Wg,this.updater=i||Qg}Ua.prototype.isReactComponent={};Ua.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Ua.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function Kg(){}Kg.prototype=Ua.prototype;function $u(t,e,i){this.props=t,this.context=e,this.refs=Wg,this.updater=i||Qg}var Pu=$u.prototype=new Kg;Pu.constructor=$u;Zg(Pu,Ua.prototype);Pu.isPureReactComponent=!0;var Pf=Array.isArray,Ct={H:null,A:null,T:null,S:null,V:null},$g=Object.prototype.hasOwnProperty;function Ju(t,e,i,n,a,s){return i=s.ref,{$$typeof:Ku,type:t,key:e,ref:i!==void 0?i:null,props:s}}function p0(t,e){return Ju(t.type,e,void 0,void 0,void 0,t.props)}function Iu(t){return typeof t=="object"&&t!==null&&t.$$typeof===Ku}function g0(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(i){return e[i]})}var Jf=/\/+/g;function Wr(t,e){return typeof t=="object"&&t!==null&&t.key!=null?g0(""+t.key):e.toString(36)}function If(){}function b0(t){switch(t.status){case"fulfilled":return t.value;case"rejected":throw t.reason;default:switch(typeof t.status=="string"?t.then(If,If):(t.status="pending",t.then(function(e){t.status==="pending"&&(t.status="fulfilled",t.value=e)},function(e){t.status==="pending"&&(t.status="rejected",t.reason=e)})),t.status){case"fulfilled":return t.value;case"rejected":throw t.reason}}throw t}function Jn(t,e,i,n,a){var s=typeof t;(s==="undefined"||s==="boolean")&&(t=null);var l=!1;if(t===null)l=!0;else switch(s){case"bigint":case"string":case"number":l=!0;break;case"object":switch(t.$$typeof){case Ku:case a0:l=!0;break;case Xg:return l=t._init,Jn(l(t._payload),e,i,n,a)}}if(l)return a=a(t),l=n===""?"."+Wr(t,0):n,Pf(a)?(i="",l!=null&&(i=l.replace(Jf,"$&/")+"/"),Jn(a,e,i,"",function(c){return c})):a!=null&&(Iu(a)&&(a=p0(a,i+(a.key==null||t&&t.key===a.key?"":(""+a.key).replace(Jf,"$&/")+"/")+l)),e.push(a)),1;l=0;var o=n===""?".":n+":";if(Pf(t))for(var r=0;r<t.length;r++)n=t[r],s=o+Wr(n,r),l+=Jn(n,e,i,s,a);else if(r=h0(t),typeof r=="function")for(t=r.call(t),r=0;!(n=t.next()).done;)n=n.value,s=o+Wr(n,r++),l+=Jn(n,e,i,s,a);else if(s==="object"){if(typeof t.then=="function")return Jn(b0(t),e,i,n,a);throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.")}return l}function Al(t,e,i){if(t==null)return t;var n=[],a=0;return Jn(t,n,"","",function(s){return e.call(i,s,a++)}),n}function x0(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(i){(t._status===0||t._status===-1)&&(t._status=1,t._result=i)},function(i){(t._status===0||t._status===-1)&&(t._status=2,t._result=i)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var th=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function m0(){}K.Children={map:Al,forEach:function(t,e,i){Al(t,function(){e.apply(this,arguments)},i)},count:function(t){var e=0;return Al(t,function(){e++}),e},toArray:function(t){return Al(t,function(e){return e})||[]},only:function(t){if(!Iu(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};K.Component=Ua;K.Fragment=s0;K.Profiler=o0;K.PureComponent=$u;K.StrictMode=l0;K.Suspense=d0;K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Ct;K.__COMPILER_RUNTIME={__proto__:null,c:function(t){return Ct.H.useMemoCache(t)}};K.cache=function(t){return function(){return t.apply(null,arguments)}};K.cloneElement=function(t,e,i){if(t==null)throw Error("The argument must be a React element, but you passed "+t+".");var n=Zg({},t.props),a=t.key,s=void 0;if(e!=null)for(l in e.ref!==void 0&&(s=void 0),e.key!==void 0&&(a=""+e.key),e)!$g.call(e,l)||l==="key"||l==="__self"||l==="__source"||l==="ref"&&e.ref===void 0||(n[l]=e[l]);var l=arguments.length-2;if(l===1)n.children=i;else if(1<l){for(var o=Array(l),r=0;r<l;r++)o[r]=arguments[r+2];n.children=o}return Ju(t.type,a,void 0,void 0,s,n)};K.createContext=function(t){return t={$$typeof:c0,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null},t.Provider=t,t.Consumer={$$typeof:r0,_context:t},t};K.createElement=function(t,e,i){var n,a={},s=null;if(e!=null)for(n in e.key!==void 0&&(s=""+e.key),e)$g.call(e,n)&&n!=="key"&&n!=="__self"&&n!=="__source"&&(a[n]=e[n]);var l=arguments.length-2;if(l===1)a.children=i;else if(1<l){for(var o=Array(l),r=0;r<l;r++)o[r]=arguments[r+2];a.children=o}if(t&&t.defaultProps)for(n in l=t.defaultProps,l)a[n]===void 0&&(a[n]=l[n]);return Ju(t,s,void 0,void 0,null,a)};K.createRef=function(){return{current:null}};K.forwardRef=function(t){return{$$typeof:u0,render:t}};K.isValidElement=Iu;K.lazy=function(t){return{$$typeof:Xg,_payload:{_status:-1,_result:t},_init:x0}};K.memo=function(t,e){return{$$typeof:f0,type:t,compare:e===void 0?null:e}};K.startTransition=function(t){var e=Ct.T,i={};Ct.T=i;try{var n=t(),a=Ct.S;a!==null&&a(i,n),typeof n=="object"&&n!==null&&typeof n.then=="function"&&n.then(m0,th)}catch(s){th(s)}finally{Ct.T=e}};K.unstable_useCacheRefresh=function(){return Ct.H.useCacheRefresh()};K.use=function(t){return Ct.H.use(t)};K.useActionState=function(t,e,i){return Ct.H.useActionState(t,e,i)};K.useCallback=function(t,e){return Ct.H.useCallback(t,e)};K.useContext=function(t){return Ct.H.useContext(t)};K.useDebugValue=function(){};K.useDeferredValue=function(t,e){return Ct.H.useDeferredValue(t,e)};K.useEffect=function(t,e,i){var n=Ct.H;if(typeof i=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return n.useEffect(t,e)};K.useId=function(){return Ct.H.useId()};K.useImperativeHandle=function(t,e,i){return Ct.H.useImperativeHandle(t,e,i)};K.useInsertionEffect=function(t,e){return Ct.H.useInsertionEffect(t,e)};K.useLayoutEffect=function(t,e){return Ct.H.useLayoutEffect(t,e)};K.useMemo=function(t,e){return Ct.H.useMemo(t,e)};K.useOptimistic=function(t,e){return Ct.H.useOptimistic(t,e)};K.useReducer=function(t,e,i){return Ct.H.useReducer(t,e,i)};K.useRef=function(t){return Ct.H.useRef(t)};K.useState=function(t){return Ct.H.useState(t)};K.useSyncExternalStore=function(t,e,i){return Ct.H.useSyncExternalStore(t,e,i)};K.useTransition=function(){return Ct.H.useTransition()};K.version="19.1.1";qg.exports=K;var z=qg.exports;const Dn=e0(z);var Pg={exports:{}},dr={},Jg={exports:{}},Ig={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(t){function e(H,V){var U=H.length;H.push(V);t:for(;0<U;){var rt=U-1>>>1,at=H[rt];if(0<a(at,V))H[rt]=V,H[U]=at,U=rt;else break t}}function i(H){return H.length===0?null:H[0]}function n(H){if(H.length===0)return null;var V=H[0],U=H.pop();if(U!==V){H[0]=U;t:for(var rt=0,at=H.length,At=at>>>1;rt<At;){var Mt=2*(rt+1)-1,le=H[Mt],R=Mt+1,N=H[R];if(0>a(le,U))R<at&&0>a(N,le)?(H[rt]=N,H[R]=U,rt=R):(H[rt]=le,H[Mt]=U,rt=Mt);else if(R<at&&0>a(N,U))H[rt]=N,H[R]=U,rt=R;else break t}}return V}function a(H,V){var U=H.sortIndex-V.sortIndex;return U!==0?U:H.id-V.id}if(t.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var s=performance;t.unstable_now=function(){return s.now()}}else{var l=Date,o=l.now();t.unstable_now=function(){return l.now()-o}}var r=[],c=[],d=1,f=null,h=3,g=!1,m=!1,v=!1,S=!1,b=typeof setTimeout=="function"?setTimeout:null,x=typeof clearTimeout=="function"?clearTimeout:null,y=typeof setImmediate<"u"?setImmediate:null;function _(H){for(var V=i(c);V!==null;){if(V.callback===null)n(c);else if(V.startTime<=H)n(c),V.sortIndex=V.expirationTime,e(r,V);else break;V=i(c)}}function D(H){if(v=!1,_(H),!m)if(i(r)!==null)m=!0,A||(A=!0,G());else{var V=i(c);V!==null&&ot(D,V.startTime-H)}}var A=!1,j=-1,T=5,M=-1;function L(){return S?!0:!(t.unstable_now()-M<T)}function Y(){if(S=!1,A){var H=t.unstable_now();M=H;var V=!0;try{t:{m=!1,v&&(v=!1,x(j),j=-1),g=!0;var U=h;try{e:{for(_(H),f=i(r);f!==null&&!(f.expirationTime>H&&L());){var rt=f.callback;if(typeof rt=="function"){f.callback=null,h=f.priorityLevel;var at=rt(f.expirationTime<=H);if(H=t.unstable_now(),typeof at=="function"){f.callback=at,_(H),V=!0;break e}f===i(r)&&n(r),_(H)}else n(r);f=i(r)}if(f!==null)V=!0;else{var At=i(c);At!==null&&ot(D,At.startTime-H),V=!1}}break t}finally{f=null,h=U,g=!1}V=void 0}}finally{V?G():A=!1}}}var G;if(typeof y=="function")G=function(){y(Y)};else if(typeof MessageChannel<"u"){var mt=new MessageChannel,wt=mt.port2;mt.port1.onmessage=Y,G=function(){wt.postMessage(null)}}else G=function(){b(Y,0)};function ot(H,V){j=b(function(){H(t.unstable_now())},V)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(H){H.callback=null},t.unstable_forceFrameRate=function(H){0>H||125<H?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<H?Math.floor(1e3/H):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_next=function(H){switch(h){case 1:case 2:case 3:var V=3;break;default:V=h}var U=h;h=V;try{return H()}finally{h=U}},t.unstable_requestPaint=function(){S=!0},t.unstable_runWithPriority=function(H,V){switch(H){case 1:case 2:case 3:case 4:case 5:break;default:H=3}var U=h;h=H;try{return V()}finally{h=U}},t.unstable_scheduleCallback=function(H,V,U){var rt=t.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?rt+U:rt):U=rt,H){case 1:var at=-1;break;case 2:at=250;break;case 5:at=**********;break;case 4:at=1e4;break;default:at=5e3}return at=U+at,H={id:d++,callback:V,priorityLevel:H,startTime:U,expirationTime:at,sortIndex:-1},U>rt?(H.sortIndex=U,e(c,H),i(r)===null&&H===i(c)&&(v?(x(j),j=-1):v=!0,ot(D,U-rt))):(H.sortIndex=at,e(r,H),m||g||(m=!0,A||(A=!0,G()))),H},t.unstable_shouldYield=L,t.unstable_wrapCallback=function(H){var V=h;return function(){var U=h;h=V;try{return H.apply(this,arguments)}finally{h=U}}}})(Ig);Jg.exports=Ig;var y0=Jg.exports,tb={exports:{}},se={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var v0=z;function eb(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Oi(){}var ae={d:{f:Oi,r:function(){throw Error(eb(522))},D:Oi,C:Oi,L:Oi,m:Oi,X:Oi,S:Oi,M:Oi},p:0,findDOMNode:null},S0=Symbol.for("react.portal");function _0(t,e,i){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:S0,key:n==null?null:""+n,children:t,containerInfo:e,implementation:i}}var xs=v0.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function fr(t,e){if(t==="font")return"";if(typeof e=="string")return e==="use-credentials"?e:""}se.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ae;se.createPortal=function(t,e){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)throw Error(eb(299));return _0(t,e,null,i)};se.flushSync=function(t){var e=xs.T,i=ae.p;try{if(xs.T=null,ae.p=2,t)return t()}finally{xs.T=e,ae.p=i,ae.d.f()}};se.preconnect=function(t,e){typeof t=="string"&&(e?(e=e.crossOrigin,e=typeof e=="string"?e==="use-credentials"?e:"":void 0):e=null,ae.d.C(t,e))};se.prefetchDNS=function(t){typeof t=="string"&&ae.d.D(t)};se.preinit=function(t,e){if(typeof t=="string"&&e&&typeof e.as=="string"){var i=e.as,n=fr(i,e.crossOrigin),a=typeof e.integrity=="string"?e.integrity:void 0,s=typeof e.fetchPriority=="string"?e.fetchPriority:void 0;i==="style"?ae.d.S(t,typeof e.precedence=="string"?e.precedence:void 0,{crossOrigin:n,integrity:a,fetchPriority:s}):i==="script"&&ae.d.X(t,{crossOrigin:n,integrity:a,fetchPriority:s,nonce:typeof e.nonce=="string"?e.nonce:void 0})}};se.preinitModule=function(t,e){if(typeof t=="string")if(typeof e=="object"&&e!==null){if(e.as==null||e.as==="script"){var i=fr(e.as,e.crossOrigin);ae.d.M(t,{crossOrigin:i,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0})}}else e==null&&ae.d.M(t)};se.preload=function(t,e){if(typeof t=="string"&&typeof e=="object"&&e!==null&&typeof e.as=="string"){var i=e.as,n=fr(i,e.crossOrigin);ae.d.L(t,i,{crossOrigin:n,integrity:typeof e.integrity=="string"?e.integrity:void 0,nonce:typeof e.nonce=="string"?e.nonce:void 0,type:typeof e.type=="string"?e.type:void 0,fetchPriority:typeof e.fetchPriority=="string"?e.fetchPriority:void 0,referrerPolicy:typeof e.referrerPolicy=="string"?e.referrerPolicy:void 0,imageSrcSet:typeof e.imageSrcSet=="string"?e.imageSrcSet:void 0,imageSizes:typeof e.imageSizes=="string"?e.imageSizes:void 0,media:typeof e.media=="string"?e.media:void 0})}};se.preloadModule=function(t,e){if(typeof t=="string")if(e){var i=fr(e.as,e.crossOrigin);ae.d.m(t,{as:typeof e.as=="string"&&e.as!=="script"?e.as:void 0,crossOrigin:i,integrity:typeof e.integrity=="string"?e.integrity:void 0})}else ae.d.m(t)};se.requestFormReset=function(t){ae.d.r(t)};se.unstable_batchedUpdates=function(t,e){return t(e)};se.useFormState=function(t,e,i){return xs.H.useFormState(t,e,i)};se.useFormStatus=function(){return xs.H.useHostTransitionStatus()};se.version="19.1.1";function ib(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(ib)}catch(t){console.error(t)}}ib(),tb.exports=se;var k0=tb.exports;/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ft=y0,nb=z,C0=k0;function O(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var i=2;i<arguments.length;i++)e+="&args[]="+encodeURIComponent(arguments[i])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function ab(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function nl(t){var e=t,i=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(i=e.return),t=e.return;while(t)}return e.tag===3?i:null}function sb(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function eh(t){if(nl(t)!==t)throw Error(O(188))}function D0(t){var e=t.alternate;if(!e){if(e=nl(t),e===null)throw Error(O(188));return e!==t?null:t}for(var i=t,n=e;;){var a=i.return;if(a===null)break;var s=a.alternate;if(s===null){if(n=a.return,n!==null){i=n;continue}break}if(a.child===s.child){for(s=a.child;s;){if(s===i)return eh(a),t;if(s===n)return eh(a),e;s=s.sibling}throw Error(O(188))}if(i.return!==n.return)i=a,n=s;else{for(var l=!1,o=a.child;o;){if(o===i){l=!0,i=a,n=s;break}if(o===n){l=!0,n=a,i=s;break}o=o.sibling}if(!l){for(o=s.child;o;){if(o===i){l=!0,i=s,n=a;break}if(o===n){l=!0,n=s,i=a;break}o=o.sibling}if(!l)throw Error(O(189))}}if(i.alternate!==n)throw Error(O(190))}if(i.tag!==3)throw Error(O(188));return i.stateNode.current===i?t:e}function lb(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=lb(t),e!==null)return e;t=t.sibling}return null}var St=Object.assign,w0=Symbol.for("react.element"),Ml=Symbol.for("react.transitional.element"),us=Symbol.for("react.portal"),ia=Symbol.for("react.fragment"),ob=Symbol.for("react.strict_mode"),Xc=Symbol.for("react.profiler"),j0=Symbol.for("react.provider"),rb=Symbol.for("react.consumer"),yi=Symbol.for("react.context"),td=Symbol.for("react.forward_ref"),Qc=Symbol.for("react.suspense"),Zc=Symbol.for("react.suspense_list"),ed=Symbol.for("react.memo"),Li=Symbol.for("react.lazy"),Wc=Symbol.for("react.activity"),z0=Symbol.for("react.memo_cache_sentinel"),ih=Symbol.iterator;function Pa(t){return t===null||typeof t!="object"?null:(t=ih&&t[ih]||t["@@iterator"],typeof t=="function"?t:null)}var T0=Symbol.for("react.client.reference");function Kc(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===T0?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case ia:return"Fragment";case Xc:return"Profiler";case ob:return"StrictMode";case Qc:return"Suspense";case Zc:return"SuspenseList";case Wc:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case us:return"Portal";case yi:return(t.displayName||"Context")+".Provider";case rb:return(t._context.displayName||"Context")+".Consumer";case td:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ed:return e=t.displayName||null,e!==null?e:Kc(t.type)||"Memo";case Li:e=t._payload,t=t._init;try{return Kc(t(e))}catch{}}return null}var ds=Array.isArray,X=nb.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,lt=C0.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,wn={pending:!1,data:null,method:null,action:null},$c=[],na=-1;function si(t){return{current:t}}function Qt(t){0>na||(t.current=$c[na],$c[na]=null,na--)}function Dt(t,e){na++,$c[na]=t.current,t.current=e}var ti=si(null),Ls=si(null),$i=si(null),Do=si(null);function wo(t,e){switch(Dt($i,e),Dt(Ls,t),Dt(ti,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?op(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=op(e),t=Dm(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}Qt(ti),Dt(ti,t)}function Da(){Qt(ti),Qt(Ls),Qt($i)}function Pc(t){t.memoizedState!==null&&Dt(Do,t);var e=ti.current,i=Dm(e,t.type);e!==i&&(Dt(Ls,t),Dt(ti,i))}function jo(t){Ls.current===t&&(Qt(ti),Qt(Ls)),Do.current===t&&(Qt(Do),Qs._currentValue=wn)}var Jc=Object.prototype.hasOwnProperty,id=Ft.unstable_scheduleCallback,Kr=Ft.unstable_cancelCallback,A0=Ft.unstable_shouldYield,M0=Ft.unstable_requestPaint,ei=Ft.unstable_now,O0=Ft.unstable_getCurrentPriorityLevel,cb=Ft.unstable_ImmediatePriority,ub=Ft.unstable_UserBlockingPriority,zo=Ft.unstable_NormalPriority,R0=Ft.unstable_LowPriority,db=Ft.unstable_IdlePriority,E0=Ft.log,B0=Ft.unstable_setDisableYieldValue,al=null,ke=null;function Vi(t){if(typeof E0=="function"&&B0(t),ke&&typeof ke.setStrictMode=="function")try{ke.setStrictMode(al,t)}catch{}}var Ce=Math.clz32?Math.clz32:H0,L0=Math.log,N0=Math.LN2;function H0(t){return t>>>=0,t===0?32:31-(L0(t)/N0|0)|0}var Ol=256,Rl=4194304;function mn(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function hr(t,e,i){var n=t.pendingLanes;if(n===0)return 0;var a=0,s=t.suspendedLanes,l=t.pingedLanes;t=t.warmLanes;var o=n&134217727;return o!==0?(n=o&~s,n!==0?a=mn(n):(l&=o,l!==0?a=mn(l):i||(i=o&~t,i!==0&&(a=mn(i))))):(o=n&~s,o!==0?a=mn(o):l!==0?a=mn(l):i||(i=n&~t,i!==0&&(a=mn(i)))),a===0?0:e!==0&&e!==a&&!(e&s)&&(s=a&-a,i=e&-e,s>=i||s===32&&(i&4194048)!==0)?e:a}function sl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function U0(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function fb(){var t=Ol;return Ol<<=1,!(Ol&4194048)&&(Ol=256),t}function hb(){var t=Rl;return Rl<<=1,!(Rl&62914560)&&(Rl=4194304),t}function $r(t){for(var e=[],i=0;31>i;i++)e.push(t);return e}function ll(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Y0(t,e,i,n,a,s){var l=t.pendingLanes;t.pendingLanes=i,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=i,t.entangledLanes&=i,t.errorRecoveryDisabledLanes&=i,t.shellSuspendCounter=0;var o=t.entanglements,r=t.expirationTimes,c=t.hiddenUpdates;for(i=l&~i;0<i;){var d=31-Ce(i),f=1<<d;o[d]=0,r[d]=-1;var h=c[d];if(h!==null)for(c[d]=null,d=0;d<h.length;d++){var g=h[d];g!==null&&(g.lane&=-536870913)}i&=~f}n!==0&&pb(t,n,0),s!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=s&~(l&~e))}function pb(t,e,i){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-Ce(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|i&4194090}function gb(t,e){var i=t.entangledLanes|=e;for(t=t.entanglements;i;){var n=31-Ce(i),a=1<<n;a&e|t[n]&e&&(t[n]|=e),i&=~a}}function nd(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function ad(t){return t&=-t,2<t?8<t?t&134217727?32:268435456:8:2}function bb(){var t=lt.p;return t!==0?t:(t=window.event,t===void 0?32:Bm(t.type))}function F0(t,e){var i=lt.p;try{return lt.p=t,e()}finally{lt.p=i}}var un=Math.random().toString(36).slice(2),Jt="__reactFiber$"+un,pe="__reactProps$"+un,Ya="__reactContainer$"+un,Ic="__reactEvents$"+un,V0="__reactListeners$"+un,G0="__reactHandles$"+un,nh="__reactResources$"+un,ol="__reactMarker$"+un;function sd(t){delete t[Jt],delete t[pe],delete t[Ic],delete t[V0],delete t[G0]}function aa(t){var e=t[Jt];if(e)return e;for(var i=t.parentNode;i;){if(e=i[Ya]||i[Jt]){if(i=e.alternate,e.child!==null||i!==null&&i.child!==null)for(t=up(t);t!==null;){if(i=t[Jt])return i;t=up(t)}return e}t=i,i=t.parentNode}return null}function Fa(t){if(t=t[Jt]||t[Ya]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function fs(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(O(33))}function pa(t){var e=t[nh];return e||(e=t[nh]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function qt(t){t[ol]=!0}var xb=new Set,mb={};function Un(t,e){wa(t,e),wa(t+"Capture",e)}function wa(t,e){for(mb[t]=e,t=0;t<e.length;t++)xb.add(e[t])}var q0=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ah={},sh={};function X0(t){return Jc.call(sh,t)?!0:Jc.call(ah,t)?!1:q0.test(t)?sh[t]=!0:(ah[t]=!0,!1)}function so(t,e,i){if(X0(e))if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+i)}}function El(t,e,i){if(i===null)t.removeAttribute(e);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+i)}}function di(t,e,i,n){if(n===null)t.removeAttribute(i);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(i);return}t.setAttributeNS(e,i,""+n)}}var Pr,lh;function In(t){if(Pr===void 0)try{throw Error()}catch(i){var e=i.stack.trim().match(/\n( *(at )?)/);Pr=e&&e[1]||"",lh=-1<i.stack.indexOf(`
    at`)?" (<anonymous>)":-1<i.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Pr+t+lh}var Jr=!1;function Ir(t,e){if(!t||Jr)return"";Jr=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var f=function(){throw Error()};if(Object.defineProperty(f.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(f,[])}catch(g){var h=g}Reflect.construct(t,[],f)}else{try{f.call()}catch(g){h=g}t.call(f.prototype)}}else{try{throw Error()}catch(g){h=g}(f=t())&&typeof f.catch=="function"&&f.catch(function(){})}}catch(g){if(g&&h&&typeof g.stack=="string")return[g.stack,h.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=n.DetermineComponentFrameRoot(),l=s[0],o=s[1];if(l&&o){var r=l.split(`
`),c=o.split(`
`);for(a=n=0;n<r.length&&!r[n].includes("DetermineComponentFrameRoot");)n++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(n===r.length||a===c.length)for(n=r.length-1,a=c.length-1;1<=n&&0<=a&&r[n]!==c[a];)a--;for(;1<=n&&0<=a;n--,a--)if(r[n]!==c[a]){if(n!==1||a!==1)do if(n--,a--,0>a||r[n]!==c[a]){var d=`
`+r[n].replace(" at new "," at ");return t.displayName&&d.includes("<anonymous>")&&(d=d.replace("<anonymous>",t.displayName)),d}while(1<=n&&0<=a);break}}}finally{Jr=!1,Error.prepareStackTrace=i}return(i=t?t.displayName||t.name:"")?In(i):""}function Q0(t){switch(t.tag){case 26:case 27:case 5:return In(t.type);case 16:return In("Lazy");case 13:return In("Suspense");case 19:return In("SuspenseList");case 0:case 15:return Ir(t.type,!1);case 11:return Ir(t.type.render,!1);case 1:return Ir(t.type,!0);case 31:return In("Activity");default:return""}}function oh(t){try{var e="";do e+=Q0(t),t=t.return;while(t);return e}catch(i){return`
Error generating stack: `+i.message+`
`+i.stack}}function Ae(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function yb(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Z0(t){var e=yb(t)?"checked":"value",i=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),n=""+t[e];if(!t.hasOwnProperty(e)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var a=i.get,s=i.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(l){n=""+l,s.call(this,l)}}),Object.defineProperty(t,e,{enumerable:i.enumerable}),{getValue:function(){return n},setValue:function(l){n=""+l},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function To(t){t._valueTracker||(t._valueTracker=Z0(t))}function vb(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var i=e.getValue(),n="";return t&&(n=yb(t)?t.checked?"true":"false":t.value),t=n,t!==i?(e.setValue(t),!0):!1}function Ao(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var W0=/[\n"\\]/g;function Re(t){return t.replace(W0,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function tu(t,e,i,n,a,s,l,o){t.name="",l!=null&&typeof l!="function"&&typeof l!="symbol"&&typeof l!="boolean"?t.type=l:t.removeAttribute("type"),e!=null?l==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ae(e)):t.value!==""+Ae(e)&&(t.value=""+Ae(e)):l!=="submit"&&l!=="reset"||t.removeAttribute("value"),e!=null?eu(t,l,Ae(e)):i!=null?eu(t,l,Ae(i)):n!=null&&t.removeAttribute("value"),a==null&&s!=null&&(t.defaultChecked=!!s),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?t.name=""+Ae(o):t.removeAttribute("name")}function Sb(t,e,i,n,a,s,l,o){if(s!=null&&typeof s!="function"&&typeof s!="symbol"&&typeof s!="boolean"&&(t.type=s),e!=null||i!=null){if(!(s!=="submit"&&s!=="reset"||e!=null))return;i=i!=null?""+Ae(i):"",e=e!=null?""+Ae(e):i,o||e===t.value||(t.value=e),t.defaultValue=e}n=n??a,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=o?t.checked:!!n,t.defaultChecked=!!n,l!=null&&typeof l!="function"&&typeof l!="symbol"&&typeof l!="boolean"&&(t.name=l)}function eu(t,e,i){e==="number"&&Ao(t.ownerDocument)===t||t.defaultValue===""+i||(t.defaultValue=""+i)}function ga(t,e,i,n){if(t=t.options,e){e={};for(var a=0;a<i.length;a++)e["$"+i[a]]=!0;for(i=0;i<t.length;i++)a=e.hasOwnProperty("$"+t[i].value),t[i].selected!==a&&(t[i].selected=a),a&&n&&(t[i].defaultSelected=!0)}else{for(i=""+Ae(i),e=null,a=0;a<t.length;a++){if(t[a].value===i){t[a].selected=!0,n&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function _b(t,e,i){if(e!=null&&(e=""+Ae(e),e!==t.value&&(t.value=e),i==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=i!=null?""+Ae(i):""}function kb(t,e,i,n){if(e==null){if(n!=null){if(i!=null)throw Error(O(92));if(ds(n)){if(1<n.length)throw Error(O(93));n=n[0]}i=n}i==null&&(i=""),e=i}i=Ae(e),t.defaultValue=i,n=t.textContent,n===i&&n!==""&&n!==null&&(t.value=n)}function ja(t,e){if(e){var i=t.firstChild;if(i&&i===t.lastChild&&i.nodeType===3){i.nodeValue=e;return}}t.textContent=e}var K0=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function rh(t,e,i){var n=e.indexOf("--")===0;i==null||typeof i=="boolean"||i===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,i):typeof i!="number"||i===0||K0.has(e)?e==="float"?t.cssFloat=i:t[e]=(""+i).trim():t[e]=i+"px"}function Cb(t,e,i){if(e!=null&&typeof e!="object")throw Error(O(62));if(t=t.style,i!=null){for(var n in i)!i.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var a in e)n=e[a],e.hasOwnProperty(a)&&i[a]!==n&&rh(t,a,n)}else for(var s in e)e.hasOwnProperty(s)&&rh(t,s,e[s])}function ld(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $0=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),P0=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function lo(t){return P0.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var iu=null;function od(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var sa=null,ba=null;function ch(t){var e=Fa(t);if(e&&(t=e.stateNode)){var i=t[pe]||null;t:switch(t=e.stateNode,e.type){case"input":if(tu(t,i.value,i.defaultValue,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name),e=i.name,i.type==="radio"&&e!=null){for(i=t;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll('input[name="'+Re(""+e)+'"][type="radio"]'),e=0;e<i.length;e++){var n=i[e];if(n!==t&&n.form===t.form){var a=n[pe]||null;if(!a)throw Error(O(90));tu(n,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<i.length;e++)n=i[e],n.form===t.form&&vb(n)}break t;case"textarea":_b(t,i.value,i.defaultValue);break t;case"select":e=i.value,e!=null&&ga(t,!!i.multiple,e,!1)}}}var tc=!1;function Db(t,e,i){if(tc)return t(e,i);tc=!0;try{var n=t(e);return n}finally{if(tc=!1,(sa!==null||ba!==null)&&(kr(),sa&&(e=sa,t=ba,ba=sa=null,ch(e),t)))for(e=0;e<t.length;e++)ch(t[e])}}function Ns(t,e){var i=t.stateNode;if(i===null)return null;var n=i[pe]||null;if(n===null)return null;i=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(i&&typeof i!="function")throw Error(O(231,e,typeof i));return i}var wi=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),nu=!1;if(wi)try{var Ja={};Object.defineProperty(Ja,"passive",{get:function(){nu=!0}}),window.addEventListener("test",Ja,Ja),window.removeEventListener("test",Ja,Ja)}catch{nu=!1}var Gi=null,rd=null,oo=null;function wb(){if(oo)return oo;var t,e=rd,i=e.length,n,a="value"in Gi?Gi.value:Gi.textContent,s=a.length;for(t=0;t<i&&e[t]===a[t];t++);var l=i-t;for(n=1;n<=l&&e[i-n]===a[s-n];n++);return oo=a.slice(t,1<n?1-n:void 0)}function ro(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Bl(){return!0}function uh(){return!1}function ge(t){function e(i,n,a,s,l){this._reactName=i,this._targetInst=a,this.type=n,this.nativeEvent=s,this.target=l,this.currentTarget=null;for(var o in t)t.hasOwnProperty(o)&&(i=t[o],this[o]=i?i(s):s[o]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?Bl:uh,this.isPropagationStopped=uh,this}return St(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=Bl)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=Bl)},persist:function(){},isPersistent:Bl}),e}var Yn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},pr=ge(Yn),rl=St({},Yn,{view:0,detail:0}),J0=ge(rl),ec,ic,Ia,gr=St({},rl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:cd,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ia&&(Ia&&t.type==="mousemove"?(ec=t.screenX-Ia.screenX,ic=t.screenY-Ia.screenY):ic=ec=0,Ia=t),ec)},movementY:function(t){return"movementY"in t?t.movementY:ic}}),dh=ge(gr),I0=St({},gr,{dataTransfer:0}),t1=ge(I0),e1=St({},rl,{relatedTarget:0}),nc=ge(e1),i1=St({},Yn,{animationName:0,elapsedTime:0,pseudoElement:0}),n1=ge(i1),a1=St({},Yn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),s1=ge(a1),l1=St({},Yn,{data:0}),fh=ge(l1),o1={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},r1={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},c1={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function u1(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=c1[t])?!!e[t]:!1}function cd(){return u1}var d1=St({},rl,{key:function(t){if(t.key){var e=o1[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=ro(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?r1[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:cd,charCode:function(t){return t.type==="keypress"?ro(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?ro(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),f1=ge(d1),h1=St({},gr,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),hh=ge(h1),p1=St({},rl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:cd}),g1=ge(p1),b1=St({},Yn,{propertyName:0,elapsedTime:0,pseudoElement:0}),x1=ge(b1),m1=St({},gr,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),y1=ge(m1),v1=St({},Yn,{newState:0,oldState:0}),S1=ge(v1),_1=[9,13,27,32],ud=wi&&"CompositionEvent"in window,ms=null;wi&&"documentMode"in document&&(ms=document.documentMode);var k1=wi&&"TextEvent"in window&&!ms,jb=wi&&(!ud||ms&&8<ms&&11>=ms),ph=" ",gh=!1;function zb(t,e){switch(t){case"keyup":return _1.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tb(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var la=!1;function C1(t,e){switch(t){case"compositionend":return Tb(e);case"keypress":return e.which!==32?null:(gh=!0,ph);case"textInput":return t=e.data,t===ph&&gh?null:t;default:return null}}function D1(t,e){if(la)return t==="compositionend"||!ud&&zb(t,e)?(t=wb(),oo=rd=Gi=null,la=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return jb&&e.locale!=="ko"?null:e.data;default:return null}}var w1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bh(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!w1[t.type]:e==="textarea"}function Ab(t,e,i,n){sa?ba?ba.push(n):ba=[n]:sa=n,e=Wo(e,"onChange"),0<e.length&&(i=new pr("onChange","change",null,i,n),t.push({event:i,listeners:e}))}var ys=null,Hs=null;function j1(t){_m(t,0)}function br(t){var e=fs(t);if(vb(e))return t}function xh(t,e){if(t==="change")return e}var Mb=!1;if(wi){var ac;if(wi){var sc="oninput"in document;if(!sc){var mh=document.createElement("div");mh.setAttribute("oninput","return;"),sc=typeof mh.oninput=="function"}ac=sc}else ac=!1;Mb=ac&&(!document.documentMode||9<document.documentMode)}function yh(){ys&&(ys.detachEvent("onpropertychange",Ob),Hs=ys=null)}function Ob(t){if(t.propertyName==="value"&&br(Hs)){var e=[];Ab(e,Hs,t,od(t)),Db(j1,e)}}function z1(t,e,i){t==="focusin"?(yh(),ys=e,Hs=i,ys.attachEvent("onpropertychange",Ob)):t==="focusout"&&yh()}function T1(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return br(Hs)}function A1(t,e){if(t==="click")return br(e)}function M1(t,e){if(t==="input"||t==="change")return br(e)}function O1(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var je=typeof Object.is=="function"?Object.is:O1;function Us(t,e){if(je(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var i=Object.keys(t),n=Object.keys(e);if(i.length!==n.length)return!1;for(n=0;n<i.length;n++){var a=i[n];if(!Jc.call(e,a)||!je(t[a],e[a]))return!1}return!0}function vh(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Sh(t,e){var i=vh(t);t=0;for(var n;i;){if(i.nodeType===3){if(n=t+i.textContent.length,t<=e&&n>=e)return{node:i,offset:e-t};t=n}t:{for(;i;){if(i.nextSibling){i=i.nextSibling;break t}i=i.parentNode}i=void 0}i=vh(i)}}function Rb(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Rb(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Eb(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=Ao(t.document);e instanceof t.HTMLIFrameElement;){try{var i=typeof e.contentWindow.location.href=="string"}catch{i=!1}if(i)t=e.contentWindow;else break;e=Ao(t.document)}return e}function dd(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var R1=wi&&"documentMode"in document&&11>=document.documentMode,oa=null,au=null,vs=null,su=!1;function _h(t,e,i){var n=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;su||oa==null||oa!==Ao(n)||(n=oa,"selectionStart"in n&&dd(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),vs&&Us(vs,n)||(vs=n,n=Wo(au,"onSelect"),0<n.length&&(e=new pr("onSelect","select",null,e,i),t.push({event:e,listeners:n}),e.target=oa)))}function pn(t,e){var i={};return i[t.toLowerCase()]=e.toLowerCase(),i["Webkit"+t]="webkit"+e,i["Moz"+t]="moz"+e,i}var ra={animationend:pn("Animation","AnimationEnd"),animationiteration:pn("Animation","AnimationIteration"),animationstart:pn("Animation","AnimationStart"),transitionrun:pn("Transition","TransitionRun"),transitionstart:pn("Transition","TransitionStart"),transitioncancel:pn("Transition","TransitionCancel"),transitionend:pn("Transition","TransitionEnd")},lc={},Bb={};wi&&(Bb=document.createElement("div").style,"AnimationEvent"in window||(delete ra.animationend.animation,delete ra.animationiteration.animation,delete ra.animationstart.animation),"TransitionEvent"in window||delete ra.transitionend.transition);function Fn(t){if(lc[t])return lc[t];if(!ra[t])return t;var e=ra[t],i;for(i in e)if(e.hasOwnProperty(i)&&i in Bb)return lc[t]=e[i];return t}var Lb=Fn("animationend"),Nb=Fn("animationiteration"),Hb=Fn("animationstart"),E1=Fn("transitionrun"),B1=Fn("transitionstart"),L1=Fn("transitioncancel"),Ub=Fn("transitionend"),Yb=new Map,lu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");lu.push("scrollEnd");function Xe(t,e){Yb.set(t,e),Un(e,[t])}var kh=new WeakMap;function Ee(t,e){if(typeof t=="object"&&t!==null){var i=kh.get(t);return i!==void 0?i:(e={value:t,source:e,stack:oh(e)},kh.set(t,e),e)}return{value:t,source:e,stack:oh(e)}}var Te=[],ca=0,fd=0;function xr(){for(var t=ca,e=fd=ca=0;e<t;){var i=Te[e];Te[e++]=null;var n=Te[e];Te[e++]=null;var a=Te[e];Te[e++]=null;var s=Te[e];if(Te[e++]=null,n!==null&&a!==null){var l=n.pending;l===null?a.next=a:(a.next=l.next,l.next=a),n.pending=a}s!==0&&Fb(i,a,s)}}function mr(t,e,i,n){Te[ca++]=t,Te[ca++]=e,Te[ca++]=i,Te[ca++]=n,fd|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function hd(t,e,i,n){return mr(t,e,i,n),Mo(t)}function Va(t,e){return mr(t,null,null,e),Mo(t)}function Fb(t,e,i){t.lanes|=i;var n=t.alternate;n!==null&&(n.lanes|=i);for(var a=!1,s=t.return;s!==null;)s.childLanes|=i,n=s.alternate,n!==null&&(n.childLanes|=i),s.tag===22&&(t=s.stateNode,t===null||t._visibility&1||(a=!0)),t=s,s=s.return;return t.tag===3?(s=t.stateNode,a&&e!==null&&(a=31-Ce(i),t=s.hiddenUpdates,n=t[a],n===null?t[a]=[e]:n.push(e),e.lane=i|536870912),s):null}function Mo(t){if(50<As)throw As=0,ju=null,Error(O(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var ua={};function N1(t,e,i,n){this.tag=t,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Se(t,e,i,n){return new N1(t,e,i,n)}function pd(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Ci(t,e){var i=t.alternate;return i===null?(i=Se(t.tag,e,t.key,t.mode),i.elementType=t.elementType,i.type=t.type,i.stateNode=t.stateNode,i.alternate=t,t.alternate=i):(i.pendingProps=e,i.type=t.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=t.flags&65011712,i.childLanes=t.childLanes,i.lanes=t.lanes,i.child=t.child,i.memoizedProps=t.memoizedProps,i.memoizedState=t.memoizedState,i.updateQueue=t.updateQueue,e=t.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},i.sibling=t.sibling,i.index=t.index,i.ref=t.ref,i.refCleanup=t.refCleanup,i}function Vb(t,e){t.flags&=65011714;var i=t.alternate;return i===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=i.childLanes,t.lanes=i.lanes,t.child=i.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=i.memoizedProps,t.memoizedState=i.memoizedState,t.updateQueue=i.updateQueue,t.type=i.type,e=i.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function co(t,e,i,n,a,s){var l=0;if(n=t,typeof t=="function")pd(t)&&(l=1);else if(typeof t=="string")l=Uv(t,i,ti.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Wc:return t=Se(31,i,e,a),t.elementType=Wc,t.lanes=s,t;case ia:return jn(i.children,a,s,e);case ob:l=8,a|=24;break;case Xc:return t=Se(12,i,e,a|2),t.elementType=Xc,t.lanes=s,t;case Qc:return t=Se(13,i,e,a),t.elementType=Qc,t.lanes=s,t;case Zc:return t=Se(19,i,e,a),t.elementType=Zc,t.lanes=s,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case j0:case yi:l=10;break t;case rb:l=9;break t;case td:l=11;break t;case ed:l=14;break t;case Li:l=16,n=null;break t}l=29,i=Error(O(130,t===null?"null":typeof t,"")),n=null}return e=Se(l,i,e,a),e.elementType=t,e.type=n,e.lanes=s,e}function jn(t,e,i,n){return t=Se(7,t,n,e),t.lanes=i,t}function oc(t,e,i){return t=Se(6,t,null,e),t.lanes=i,t}function rc(t,e,i){return e=Se(4,t.children!==null?t.children:[],t.key,e),e.lanes=i,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var da=[],fa=0,Oo=null,Ro=0,Me=[],Oe=0,zn=null,vi=1,Si="";function yn(t,e){da[fa++]=Ro,da[fa++]=Oo,Oo=t,Ro=e}function Gb(t,e,i){Me[Oe++]=vi,Me[Oe++]=Si,Me[Oe++]=zn,zn=t;var n=vi;t=Si;var a=32-Ce(n)-1;n&=~(1<<a),i+=1;var s=32-Ce(e)+a;if(30<s){var l=a-a%5;s=(n&(1<<l)-1).toString(32),n>>=l,a-=l,vi=1<<32-Ce(e)+a|i<<a|n,Si=s+t}else vi=1<<s|i<<a|n,Si=t}function gd(t){t.return!==null&&(yn(t,1),Gb(t,1,0))}function bd(t){for(;t===Oo;)Oo=da[--fa],da[fa]=null,Ro=da[--fa],da[fa]=null;for(;t===zn;)zn=Me[--Oe],Me[Oe]=null,Si=Me[--Oe],Me[Oe]=null,vi=Me[--Oe],Me[Oe]=null}var ne=null,zt=null,st=!1,Tn=null,Je=!1,ou=Error(O(519));function En(t){var e=Error(O(418,""));throw Ys(Ee(e,t)),ou}function Ch(t){var e=t.stateNode,i=t.type,n=t.memoizedProps;switch(e[Jt]=t,e[pe]=n,i){case"dialog":J("cancel",e),J("close",e);break;case"iframe":case"object":case"embed":J("load",e);break;case"video":case"audio":for(i=0;i<Gs.length;i++)J(Gs[i],e);break;case"source":J("error",e);break;case"img":case"image":case"link":J("error",e),J("load",e);break;case"details":J("toggle",e);break;case"input":J("invalid",e),Sb(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),To(e);break;case"select":J("invalid",e);break;case"textarea":J("invalid",e),kb(e,n.value,n.defaultValue,n.children),To(e)}i=n.children,typeof i!="string"&&typeof i!="number"&&typeof i!="bigint"||e.textContent===""+i||n.suppressHydrationWarning===!0||Cm(e.textContent,i)?(n.popover!=null&&(J("beforetoggle",e),J("toggle",e)),n.onScroll!=null&&J("scroll",e),n.onScrollEnd!=null&&J("scrollend",e),n.onClick!=null&&(e.onclick=wr),e=!0):e=!1,e||En(t)}function Dh(t){for(ne=t.return;ne;)switch(ne.tag){case 5:case 13:Je=!1;return;case 27:case 3:Je=!0;return;default:ne=ne.return}}function ts(t){if(t!==ne)return!1;if(!st)return Dh(t),st=!0,!1;var e=t.tag,i;if((i=e!==3&&e!==27)&&((i=e===5)&&(i=t.type,i=!(i!=="form"&&i!=="button")||Ru(t.type,t.memoizedProps)),i=!i),i&&zt&&En(t),Dh(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(O(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(i=t.data,i==="/$"){if(e===0){zt=Ge(t.nextSibling);break t}e--}else i!=="$"&&i!=="$!"&&i!=="$?"||e++;t=t.nextSibling}zt=null}}else e===27?(e=zt,dn(t.type)?(t=Lu,Lu=null,zt=t):zt=e):zt=ne?Ge(t.stateNode.nextSibling):null;return!0}function cl(){zt=ne=null,st=!1}function wh(){var t=Tn;return t!==null&&(fe===null?fe=t:fe.push.apply(fe,t),Tn=null),t}function Ys(t){Tn===null?Tn=[t]:Tn.push(t)}var ru=si(null),Vn=null,_i=null;function Hi(t,e,i){Dt(ru,e._currentValue),e._currentValue=i}function Di(t){t._currentValue=ru.current,Qt(ru)}function cu(t,e,i){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===i)break;t=t.return}}function uu(t,e,i,n){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var s=a.dependencies;if(s!==null){var l=a.child;s=s.firstContext;t:for(;s!==null;){var o=s;s=a;for(var r=0;r<e.length;r++)if(o.context===e[r]){s.lanes|=i,o=s.alternate,o!==null&&(o.lanes|=i),cu(s.return,i,t),n||(l=null);break t}s=o.next}}else if(a.tag===18){if(l=a.return,l===null)throw Error(O(341));l.lanes|=i,s=l.alternate,s!==null&&(s.lanes|=i),cu(l,i,t),l=null}else l=a.child;if(l!==null)l.return=a;else for(l=a;l!==null;){if(l===t){l=null;break}if(a=l.sibling,a!==null){a.return=l.return,l=a;break}l=l.return}a=l}}function ul(t,e,i,n){t=null;for(var a=e,s=!1;a!==null;){if(!s){if(a.flags&524288)s=!0;else if(a.flags&262144)break}if(a.tag===10){var l=a.alternate;if(l===null)throw Error(O(387));if(l=l.memoizedProps,l!==null){var o=a.type;je(a.pendingProps.value,l.value)||(t!==null?t.push(o):t=[o])}}else if(a===Do.current){if(l=a.alternate,l===null)throw Error(O(387));l.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(Qs):t=[Qs])}a=a.return}t!==null&&uu(e,t,i,n),e.flags|=262144}function Eo(t){for(t=t.firstContext;t!==null;){if(!je(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Bn(t){Vn=t,_i=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function It(t){return qb(Vn,t)}function Ll(t,e){return Vn===null&&Bn(t),qb(t,e)}function qb(t,e){var i=e._currentValue;if(e={context:e,memoizedValue:i,next:null},_i===null){if(t===null)throw Error(O(308));_i=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else _i=_i.next=e;return i}var H1=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(i,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(i){return i()})}},U1=Ft.unstable_scheduleCallback,Y1=Ft.unstable_NormalPriority,Ut={$$typeof:yi,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function xd(){return{controller:new H1,data:new Map,refCount:0}}function dl(t){t.refCount--,t.refCount===0&&U1(Y1,function(){t.controller.abort()})}var Ss=null,du=0,za=0,xa=null;function F1(t,e){if(Ss===null){var i=Ss=[];du=0,za=Yd(),xa={status:"pending",value:void 0,then:function(n){i.push(n)}}}return du++,e.then(jh,jh),e}function jh(){if(--du===0&&Ss!==null){xa!==null&&(xa.status="fulfilled");var t=Ss;Ss=null,za=0,xa=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function V1(t,e){var i=[],n={status:"pending",value:null,reason:null,then:function(a){i.push(a)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var a=0;a<i.length;a++)(0,i[a])(e)},function(a){for(n.status="rejected",n.reason=a,a=0;a<i.length;a++)(0,i[a])(void 0)}),n}var zh=X.S;X.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&F1(t,e),zh!==null&&zh(t,e)};var An=si(null);function md(){var t=An.current;return t!==null?t:yt.pooledCache}function uo(t,e){e===null?Dt(An,An.current):Dt(An,e.pool)}function Xb(){var t=md();return t===null?null:{parent:Ut._currentValue,pool:t}}var fl=Error(O(460)),Qb=Error(O(474)),yr=Error(O(542)),fu={then:function(){}};function Th(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Nl(){}function Zb(t,e,i){switch(i=t[i],i===void 0?t.push(e):i!==e&&(e.then(Nl,Nl),e=i),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Mh(t),t;default:if(typeof e.status=="string")e.then(Nl,Nl);else{if(t=yt,t!==null&&100<t.shellSuspendCounter)throw Error(O(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=n}},function(n){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Mh(t),t}throw _s=e,fl}}var _s=null;function Ah(){if(_s===null)throw Error(O(459));var t=_s;return _s=null,t}function Mh(t){if(t===fl||t===yr)throw Error(O(483))}var Ni=!1;function yd(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function hu(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Pi(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Ji(t,e,i){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,ft&2){var a=n.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),n.pending=e,e=Mo(t),Fb(t,null,i),e}return mr(t,n,e,i),Mo(t)}function ks(t,e,i){if(e=e.updateQueue,e!==null&&(e=e.shared,(i&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,i|=n,e.lanes=i,gb(t,i)}}function cc(t,e){var i=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,i===n)){var a=null,s=null;if(i=i.firstBaseUpdate,i!==null){do{var l={lane:i.lane,tag:i.tag,payload:i.payload,callback:null,next:null};s===null?a=s=l:s=s.next=l,i=i.next}while(i!==null);s===null?a=s=e:s=s.next=e}else a=s=e;i={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:s,shared:n.shared,callbacks:n.callbacks},t.updateQueue=i;return}t=i.lastBaseUpdate,t===null?i.firstBaseUpdate=e:t.next=e,i.lastBaseUpdate=e}var pu=!1;function Cs(){if(pu){var t=xa;if(t!==null)throw t}}function Ds(t,e,i,n){pu=!1;var a=t.updateQueue;Ni=!1;var s=a.firstBaseUpdate,l=a.lastBaseUpdate,o=a.shared.pending;if(o!==null){a.shared.pending=null;var r=o,c=r.next;r.next=null,l===null?s=c:l.next=c,l=r;var d=t.alternate;d!==null&&(d=d.updateQueue,o=d.lastBaseUpdate,o!==l&&(o===null?d.firstBaseUpdate=c:o.next=c,d.lastBaseUpdate=r))}if(s!==null){var f=a.baseState;l=0,d=c=r=null,o=s;do{var h=o.lane&-536870913,g=h!==o.lane;if(g?(it&h)===h:(n&h)===h){h!==0&&h===za&&(pu=!0),d!==null&&(d=d.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});t:{var m=t,v=o;h=e;var S=i;switch(v.tag){case 1:if(m=v.payload,typeof m=="function"){f=m.call(S,f,h);break t}f=m;break t;case 3:m.flags=m.flags&-65537|128;case 0:if(m=v.payload,h=typeof m=="function"?m.call(S,f,h):m,h==null)break t;f=St({},f,h);break t;case 2:Ni=!0}}h=o.callback,h!==null&&(t.flags|=64,g&&(t.flags|=8192),g=a.callbacks,g===null?a.callbacks=[h]:g.push(h))}else g={lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},d===null?(c=d=g,r=f):d=d.next=g,l|=h;if(o=o.next,o===null){if(o=a.shared.pending,o===null)break;g=o,o=g.next,g.next=null,a.lastBaseUpdate=g,a.shared.pending=null}}while(!0);d===null&&(r=f),a.baseState=r,a.firstBaseUpdate=c,a.lastBaseUpdate=d,s===null&&(a.shared.lanes=0),rn|=l,t.lanes=l,t.memoizedState=f}}function Wb(t,e){if(typeof t!="function")throw Error(O(191,t));t.call(e)}function Kb(t,e){var i=t.callbacks;if(i!==null)for(t.callbacks=null,t=0;t<i.length;t++)Wb(i[t],e)}var Ta=si(null),Bo=si(0);function Oh(t,e){t=Ti,Dt(Bo,t),Dt(Ta,e),Ti=t|e.baseLanes}function gu(){Dt(Bo,Ti),Dt(Ta,Ta.current)}function vd(){Ti=Bo.current,Qt(Ta),Qt(Bo)}var ln=0,$=null,bt=null,Bt=null,Lo=!1,ma=!1,Ln=!1,No=0,Fs=0,ya=null,G1=0;function Ot(){throw Error(O(321))}function Sd(t,e){if(e===null)return!1;for(var i=0;i<e.length&&i<t.length;i++)if(!je(t[i],e[i]))return!1;return!0}function _d(t,e,i,n,a,s){return ln=s,$=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,X.H=t===null||t.memoizedState===null?wx:jx,Ln=!1,s=i(n,a),Ln=!1,ma&&(s=Pb(e,i,n,a)),$b(t),s}function $b(t){X.H=Ho;var e=bt!==null&&bt.next!==null;if(ln=0,Bt=bt=$=null,Lo=!1,Fs=0,ya=null,e)throw Error(O(300));t===null||Xt||(t=t.dependencies,t!==null&&Eo(t)&&(Xt=!0))}function Pb(t,e,i,n){$=t;var a=0;do{if(ma&&(ya=null),Fs=0,ma=!1,25<=a)throw Error(O(301));if(a+=1,Bt=bt=null,t.updateQueue!=null){var s=t.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,s.memoCache!=null&&(s.memoCache.index=0)}X.H=$1,s=e(i,n)}while(ma);return s}function q1(){var t=X.H,e=t.useState()[0];return e=typeof e.then=="function"?hl(e):e,t=t.useState()[0],(bt!==null?bt.memoizedState:null)!==t&&($.flags|=1024),e}function kd(){var t=No!==0;return No=0,t}function Cd(t,e,i){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~i}function Dd(t){if(Lo){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Lo=!1}ln=0,Bt=bt=$=null,ma=!1,Fs=No=0,ya=null}function ue(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Bt===null?$.memoizedState=Bt=t:Bt=Bt.next=t,Bt}function Nt(){if(bt===null){var t=$.alternate;t=t!==null?t.memoizedState:null}else t=bt.next;var e=Bt===null?$.memoizedState:Bt.next;if(e!==null)Bt=e,bt=t;else{if(t===null)throw $.alternate===null?Error(O(467)):Error(O(310));bt=t,t={memoizedState:bt.memoizedState,baseState:bt.baseState,baseQueue:bt.baseQueue,queue:bt.queue,next:null},Bt===null?$.memoizedState=Bt=t:Bt=Bt.next=t}return Bt}function wd(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function hl(t){var e=Fs;return Fs+=1,ya===null&&(ya=[]),t=Zb(ya,t,e),e=$,(Bt===null?e.memoizedState:Bt.next)===null&&(e=e.alternate,X.H=e===null||e.memoizedState===null?wx:jx),t}function vr(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return hl(t);if(t.$$typeof===yi)return It(t)}throw Error(O(438,String(t)))}function jd(t){var e=null,i=$.updateQueue;if(i!==null&&(e=i.memoCache),e==null){var n=$.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),i===null&&(i=wd(),$.updateQueue=i),i.memoCache=e,i=e.data[e.index],i===void 0)for(i=e.data[e.index]=Array(t),n=0;n<t;n++)i[n]=z0;return e.index++,i}function ji(t,e){return typeof e=="function"?e(t):e}function fo(t){var e=Nt();return zd(e,bt,t)}function zd(t,e,i){var n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=i;var a=t.baseQueue,s=n.pending;if(s!==null){if(a!==null){var l=a.next;a.next=s.next,s.next=l}e.baseQueue=a=s,n.pending=null}if(s=t.baseState,a===null)t.memoizedState=s;else{e=a.next;var o=l=null,r=null,c=e,d=!1;do{var f=c.lane&-536870913;if(f!==c.lane?(it&f)===f:(ln&f)===f){var h=c.revertLane;if(h===0)r!==null&&(r=r.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===za&&(d=!0);else if((ln&h)===h){c=c.next,h===za&&(d=!0);continue}else f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},r===null?(o=r=f,l=s):r=r.next=f,$.lanes|=h,rn|=h;f=c.action,Ln&&i(s,f),s=c.hasEagerState?c.eagerState:i(s,f)}else h={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},r===null?(o=r=h,l=s):r=r.next=h,$.lanes|=f,rn|=f;c=c.next}while(c!==null&&c!==e);if(r===null?l=s:r.next=o,!je(s,t.memoizedState)&&(Xt=!0,d&&(i=xa,i!==null)))throw i;t.memoizedState=s,t.baseState=l,t.baseQueue=r,n.lastRenderedState=s}return a===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function uc(t){var e=Nt(),i=e.queue;if(i===null)throw Error(O(311));i.lastRenderedReducer=t;var n=i.dispatch,a=i.pending,s=e.memoizedState;if(a!==null){i.pending=null;var l=a=a.next;do s=t(s,l.action),l=l.next;while(l!==a);je(s,e.memoizedState)||(Xt=!0),e.memoizedState=s,e.baseQueue===null&&(e.baseState=s),i.lastRenderedState=s}return[s,n]}function Jb(t,e,i){var n=$,a=Nt(),s=st;if(s){if(i===void 0)throw Error(O(407));i=i()}else i=e();var l=!je((bt||a).memoizedState,i);l&&(a.memoizedState=i,Xt=!0),a=a.queue;var o=ex.bind(null,n,a,t);if(pl(2048,8,o,[t]),a.getSnapshot!==e||l||Bt!==null&&Bt.memoizedState.tag&1){if(n.flags|=2048,Aa(9,Sr(),tx.bind(null,n,a,i,e),null),yt===null)throw Error(O(349));s||ln&124||Ib(n,e,i)}return i}function Ib(t,e,i){t.flags|=16384,t={getSnapshot:e,value:i},e=$.updateQueue,e===null?(e=wd(),$.updateQueue=e,e.stores=[t]):(i=e.stores,i===null?e.stores=[t]:i.push(t))}function tx(t,e,i,n){e.value=i,e.getSnapshot=n,ix(e)&&nx(t)}function ex(t,e,i){return i(function(){ix(e)&&nx(t)})}function ix(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!je(t,i)}catch{return!0}}function nx(t){var e=Va(t,2);e!==null&&we(e,t,2)}function bu(t){var e=ue();if(typeof t=="function"){var i=t;if(t=i(),Ln){Vi(!0);try{i()}finally{Vi(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ji,lastRenderedState:t},e}function ax(t,e,i,n){return t.baseState=i,zd(t,bt,typeof n=="function"?n:ji)}function X1(t,e,i,n,a){if(_r(t))throw Error(O(485));if(t=e.action,t!==null){var s={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(l){s.listeners.push(l)}};X.T!==null?i(!0):s.isTransition=!1,n(s),i=e.pending,i===null?(s.next=e.pending=s,sx(e,s)):(s.next=i.next,e.pending=i.next=s)}}function sx(t,e){var i=e.action,n=e.payload,a=t.state;if(e.isTransition){var s=X.T,l={};X.T=l;try{var o=i(a,n),r=X.S;r!==null&&r(l,o),Rh(t,e,o)}catch(c){xu(t,e,c)}finally{X.T=s}}else try{s=i(a,n),Rh(t,e,s)}catch(c){xu(t,e,c)}}function Rh(t,e,i){i!==null&&typeof i=="object"&&typeof i.then=="function"?i.then(function(n){Eh(t,e,n)},function(n){return xu(t,e,n)}):Eh(t,e,i)}function Eh(t,e,i){e.status="fulfilled",e.value=i,lx(e),t.state=i,e=t.pending,e!==null&&(i=e.next,i===e?t.pending=null:(i=i.next,e.next=i,sx(t,i)))}function xu(t,e,i){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=i,lx(e),e=e.next;while(e!==n)}t.action=null}function lx(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function ox(t,e){return e}function Bh(t,e){if(st){var i=yt.formState;if(i!==null){t:{var n=$;if(st){if(zt){e:{for(var a=zt,s=Je;a.nodeType!==8;){if(!s){a=null;break e}if(a=Ge(a.nextSibling),a===null){a=null;break e}}s=a.data,a=s==="F!"||s==="F"?a:null}if(a){zt=Ge(a.nextSibling),n=a.data==="F!";break t}}En(n)}n=!1}n&&(e=i[0])}}return i=ue(),i.memoizedState=i.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ox,lastRenderedState:e},i.queue=n,i=kx.bind(null,$,n),n.dispatch=i,n=bu(!1),s=Od.bind(null,$,!1,n.queue),n=ue(),a={state:e,dispatch:null,action:t,pending:null},n.queue=a,i=X1.bind(null,$,a,s,i),a.dispatch=i,n.memoizedState=t,[e,i,!1]}function Lh(t){var e=Nt();return rx(e,bt,t)}function rx(t,e,i){if(e=zd(t,e,ox)[0],t=fo(ji)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=hl(e)}catch(l){throw l===fl?yr:l}else n=e;e=Nt();var a=e.queue,s=a.dispatch;return i!==e.memoizedState&&($.flags|=2048,Aa(9,Sr(),Q1.bind(null,a,i),null)),[n,s,t]}function Q1(t,e){t.action=e}function Nh(t){var e=Nt(),i=bt;if(i!==null)return rx(e,i,t);Nt(),e=e.memoizedState,i=Nt();var n=i.queue.dispatch;return i.memoizedState=t,[e,n,!1]}function Aa(t,e,i,n){return t={tag:t,create:i,deps:n,inst:e,next:null},e=$.updateQueue,e===null&&(e=wd(),$.updateQueue=e),i=e.lastEffect,i===null?e.lastEffect=t.next=t:(n=i.next,i.next=t,t.next=n,e.lastEffect=t),t}function Sr(){return{destroy:void 0,resource:void 0}}function cx(){return Nt().memoizedState}function ho(t,e,i,n){var a=ue();n=n===void 0?null:n,$.flags|=t,a.memoizedState=Aa(1|e,Sr(),i,n)}function pl(t,e,i,n){var a=Nt();n=n===void 0?null:n;var s=a.memoizedState.inst;bt!==null&&n!==null&&Sd(n,bt.memoizedState.deps)?a.memoizedState=Aa(e,s,i,n):($.flags|=t,a.memoizedState=Aa(1|e,s,i,n))}function Hh(t,e){ho(8390656,8,t,e)}function ux(t,e){pl(2048,8,t,e)}function dx(t,e){return pl(4,2,t,e)}function fx(t,e){return pl(4,4,t,e)}function hx(t,e){if(typeof e=="function"){t=t();var i=e(t);return function(){typeof i=="function"?i():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function px(t,e,i){i=i!=null?i.concat([t]):null,pl(4,4,hx.bind(null,e,t),i)}function Td(){}function gx(t,e){var i=Nt();e=e===void 0?null:e;var n=i.memoizedState;return e!==null&&Sd(e,n[1])?n[0]:(i.memoizedState=[t,e],t)}function bx(t,e){var i=Nt();e=e===void 0?null:e;var n=i.memoizedState;if(e!==null&&Sd(e,n[1]))return n[0];if(n=t(),Ln){Vi(!0);try{t()}finally{Vi(!1)}}return i.memoizedState=[n,e],n}function Ad(t,e,i){return i===void 0||ln&1073741824?t.memoizedState=e:(t.memoizedState=i,t=lm(),$.lanes|=t,rn|=t,i)}function xx(t,e,i,n){return je(i,e)?i:Ta.current!==null?(t=Ad(t,i,n),je(t,e)||(Xt=!0),t):ln&42?(t=lm(),$.lanes|=t,rn|=t,e):(Xt=!0,t.memoizedState=i)}function mx(t,e,i,n,a){var s=lt.p;lt.p=s!==0&&8>s?s:8;var l=X.T,o={};X.T=o,Od(t,!1,e,i);try{var r=a(),c=X.S;if(c!==null&&c(o,r),r!==null&&typeof r=="object"&&typeof r.then=="function"){var d=V1(r,n);ws(t,e,d,De(t))}else ws(t,e,n,De(t))}catch(f){ws(t,e,{then:function(){},status:"rejected",reason:f},De())}finally{lt.p=s,X.T=l}}function Z1(){}function mu(t,e,i,n){if(t.tag!==5)throw Error(O(476));var a=yx(t).queue;mx(t,a,e,wn,i===null?Z1:function(){return vx(t),i(n)})}function yx(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:wn,baseState:wn,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ji,lastRenderedState:wn},next:null};var i={};return e.next={memoizedState:i,baseState:i,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ji,lastRenderedState:i},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function vx(t){var e=yx(t).next.queue;ws(t,e,{},De())}function Md(){return It(Qs)}function Sx(){return Nt().memoizedState}function _x(){return Nt().memoizedState}function W1(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var i=De();t=Pi(i);var n=Ji(e,t,i);n!==null&&(we(n,e,i),ks(n,e,i)),e={cache:xd()},t.payload=e;return}e=e.return}}function K1(t,e,i){var n=De();i={lane:n,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null},_r(t)?Cx(e,i):(i=hd(t,e,i,n),i!==null&&(we(i,t,n),Dx(i,e,n)))}function kx(t,e,i){var n=De();ws(t,e,i,n)}function ws(t,e,i,n){var a={lane:n,revertLane:0,action:i,hasEagerState:!1,eagerState:null,next:null};if(_r(t))Cx(e,a);else{var s=t.alternate;if(t.lanes===0&&(s===null||s.lanes===0)&&(s=e.lastRenderedReducer,s!==null))try{var l=e.lastRenderedState,o=s(l,i);if(a.hasEagerState=!0,a.eagerState=o,je(o,l))return mr(t,e,a,0),yt===null&&xr(),!1}catch{}finally{}if(i=hd(t,e,a,n),i!==null)return we(i,t,n),Dx(i,e,n),!0}return!1}function Od(t,e,i,n){if(n={lane:2,revertLane:Yd(),action:n,hasEagerState:!1,eagerState:null,next:null},_r(t)){if(e)throw Error(O(479))}else e=hd(t,i,n,2),e!==null&&we(e,t,2)}function _r(t){var e=t.alternate;return t===$||e!==null&&e===$}function Cx(t,e){ma=Lo=!0;var i=t.pending;i===null?e.next=e:(e.next=i.next,i.next=e),t.pending=e}function Dx(t,e,i){if(i&4194048){var n=e.lanes;n&=t.pendingLanes,i|=n,e.lanes=i,gb(t,i)}}var Ho={readContext:It,use:vr,useCallback:Ot,useContext:Ot,useEffect:Ot,useImperativeHandle:Ot,useLayoutEffect:Ot,useInsertionEffect:Ot,useMemo:Ot,useReducer:Ot,useRef:Ot,useState:Ot,useDebugValue:Ot,useDeferredValue:Ot,useTransition:Ot,useSyncExternalStore:Ot,useId:Ot,useHostTransitionStatus:Ot,useFormState:Ot,useActionState:Ot,useOptimistic:Ot,useMemoCache:Ot,useCacheRefresh:Ot},wx={readContext:It,use:vr,useCallback:function(t,e){return ue().memoizedState=[t,e===void 0?null:e],t},useContext:It,useEffect:Hh,useImperativeHandle:function(t,e,i){i=i!=null?i.concat([t]):null,ho(4194308,4,hx.bind(null,e,t),i)},useLayoutEffect:function(t,e){return ho(4194308,4,t,e)},useInsertionEffect:function(t,e){ho(4,2,t,e)},useMemo:function(t,e){var i=ue();e=e===void 0?null:e;var n=t();if(Ln){Vi(!0);try{t()}finally{Vi(!1)}}return i.memoizedState=[n,e],n},useReducer:function(t,e,i){var n=ue();if(i!==void 0){var a=i(e);if(Ln){Vi(!0);try{i(e)}finally{Vi(!1)}}}else a=e;return n.memoizedState=n.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},n.queue=t,t=t.dispatch=K1.bind(null,$,t),[n.memoizedState,t]},useRef:function(t){var e=ue();return t={current:t},e.memoizedState=t},useState:function(t){t=bu(t);var e=t.queue,i=kx.bind(null,$,e);return e.dispatch=i,[t.memoizedState,i]},useDebugValue:Td,useDeferredValue:function(t,e){var i=ue();return Ad(i,t,e)},useTransition:function(){var t=bu(!1);return t=mx.bind(null,$,t.queue,!0,!1),ue().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,i){var n=$,a=ue();if(st){if(i===void 0)throw Error(O(407));i=i()}else{if(i=e(),yt===null)throw Error(O(349));it&124||Ib(n,e,i)}a.memoizedState=i;var s={value:i,getSnapshot:e};return a.queue=s,Hh(ex.bind(null,n,s,t),[t]),n.flags|=2048,Aa(9,Sr(),tx.bind(null,n,s,i,e),null),i},useId:function(){var t=ue(),e=yt.identifierPrefix;if(st){var i=Si,n=vi;i=(n&~(1<<32-Ce(n)-1)).toString(32)+i,e="«"+e+"R"+i,i=No++,0<i&&(e+="H"+i.toString(32)),e+="»"}else i=G1++,e="«"+e+"r"+i.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:Md,useFormState:Bh,useActionState:Bh,useOptimistic:function(t){var e=ue();e.memoizedState=e.baseState=t;var i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=i,e=Od.bind(null,$,!0,i),i.dispatch=e,[t,e]},useMemoCache:jd,useCacheRefresh:function(){return ue().memoizedState=W1.bind(null,$)}},jx={readContext:It,use:vr,useCallback:gx,useContext:It,useEffect:ux,useImperativeHandle:px,useInsertionEffect:dx,useLayoutEffect:fx,useMemo:bx,useReducer:fo,useRef:cx,useState:function(){return fo(ji)},useDebugValue:Td,useDeferredValue:function(t,e){var i=Nt();return xx(i,bt.memoizedState,t,e)},useTransition:function(){var t=fo(ji)[0],e=Nt().memoizedState;return[typeof t=="boolean"?t:hl(t),e]},useSyncExternalStore:Jb,useId:Sx,useHostTransitionStatus:Md,useFormState:Lh,useActionState:Lh,useOptimistic:function(t,e){var i=Nt();return ax(i,bt,t,e)},useMemoCache:jd,useCacheRefresh:_x},$1={readContext:It,use:vr,useCallback:gx,useContext:It,useEffect:ux,useImperativeHandle:px,useInsertionEffect:dx,useLayoutEffect:fx,useMemo:bx,useReducer:uc,useRef:cx,useState:function(){return uc(ji)},useDebugValue:Td,useDeferredValue:function(t,e){var i=Nt();return bt===null?Ad(i,t,e):xx(i,bt.memoizedState,t,e)},useTransition:function(){var t=uc(ji)[0],e=Nt().memoizedState;return[typeof t=="boolean"?t:hl(t),e]},useSyncExternalStore:Jb,useId:Sx,useHostTransitionStatus:Md,useFormState:Nh,useActionState:Nh,useOptimistic:function(t,e){var i=Nt();return bt!==null?ax(i,bt,t,e):(i.baseState=t,[t,i.queue.dispatch])},useMemoCache:jd,useCacheRefresh:_x},va=null,Vs=0;function Hl(t){var e=Vs;return Vs+=1,va===null&&(va=[]),Zb(va,t,e)}function es(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Ul(t,e){throw e.$$typeof===w0?Error(O(525)):(t=Object.prototype.toString.call(e),Error(O(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Uh(t){var e=t._init;return e(t._payload)}function zx(t){function e(b,x){if(t){var y=b.deletions;y===null?(b.deletions=[x],b.flags|=16):y.push(x)}}function i(b,x){if(!t)return null;for(;x!==null;)e(b,x),x=x.sibling;return null}function n(b){for(var x=new Map;b!==null;)b.key!==null?x.set(b.key,b):x.set(b.index,b),b=b.sibling;return x}function a(b,x){return b=Ci(b,x),b.index=0,b.sibling=null,b}function s(b,x,y){return b.index=y,t?(y=b.alternate,y!==null?(y=y.index,y<x?(b.flags|=67108866,x):y):(b.flags|=67108866,x)):(b.flags|=1048576,x)}function l(b){return t&&b.alternate===null&&(b.flags|=67108866),b}function o(b,x,y,_){return x===null||x.tag!==6?(x=oc(y,b.mode,_),x.return=b,x):(x=a(x,y),x.return=b,x)}function r(b,x,y,_){var D=y.type;return D===ia?d(b,x,y.props.children,_,y.key):x!==null&&(x.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===Li&&Uh(D)===x.type)?(x=a(x,y.props),es(x,y),x.return=b,x):(x=co(y.type,y.key,y.props,null,b.mode,_),es(x,y),x.return=b,x)}function c(b,x,y,_){return x===null||x.tag!==4||x.stateNode.containerInfo!==y.containerInfo||x.stateNode.implementation!==y.implementation?(x=rc(y,b.mode,_),x.return=b,x):(x=a(x,y.children||[]),x.return=b,x)}function d(b,x,y,_,D){return x===null||x.tag!==7?(x=jn(y,b.mode,_,D),x.return=b,x):(x=a(x,y),x.return=b,x)}function f(b,x,y){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return x=oc(""+x,b.mode,y),x.return=b,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Ml:return y=co(x.type,x.key,x.props,null,b.mode,y),es(y,x),y.return=b,y;case us:return x=rc(x,b.mode,y),x.return=b,x;case Li:var _=x._init;return x=_(x._payload),f(b,x,y)}if(ds(x)||Pa(x))return x=jn(x,b.mode,y,null),x.return=b,x;if(typeof x.then=="function")return f(b,Hl(x),y);if(x.$$typeof===yi)return f(b,Ll(b,x),y);Ul(b,x)}return null}function h(b,x,y,_){var D=x!==null?x.key:null;if(typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint")return D!==null?null:o(b,x,""+y,_);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case Ml:return y.key===D?r(b,x,y,_):null;case us:return y.key===D?c(b,x,y,_):null;case Li:return D=y._init,y=D(y._payload),h(b,x,y,_)}if(ds(y)||Pa(y))return D!==null?null:d(b,x,y,_,null);if(typeof y.then=="function")return h(b,x,Hl(y),_);if(y.$$typeof===yi)return h(b,x,Ll(b,y),_);Ul(b,y)}return null}function g(b,x,y,_,D){if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return b=b.get(y)||null,o(x,b,""+_,D);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case Ml:return b=b.get(_.key===null?y:_.key)||null,r(x,b,_,D);case us:return b=b.get(_.key===null?y:_.key)||null,c(x,b,_,D);case Li:var A=_._init;return _=A(_._payload),g(b,x,y,_,D)}if(ds(_)||Pa(_))return b=b.get(y)||null,d(x,b,_,D,null);if(typeof _.then=="function")return g(b,x,y,Hl(_),D);if(_.$$typeof===yi)return g(b,x,y,Ll(x,_),D);Ul(x,_)}return null}function m(b,x,y,_){for(var D=null,A=null,j=x,T=x=0,M=null;j!==null&&T<y.length;T++){j.index>T?(M=j,j=null):M=j.sibling;var L=h(b,j,y[T],_);if(L===null){j===null&&(j=M);break}t&&j&&L.alternate===null&&e(b,j),x=s(L,x,T),A===null?D=L:A.sibling=L,A=L,j=M}if(T===y.length)return i(b,j),st&&yn(b,T),D;if(j===null){for(;T<y.length;T++)j=f(b,y[T],_),j!==null&&(x=s(j,x,T),A===null?D=j:A.sibling=j,A=j);return st&&yn(b,T),D}for(j=n(j);T<y.length;T++)M=g(j,b,T,y[T],_),M!==null&&(t&&M.alternate!==null&&j.delete(M.key===null?T:M.key),x=s(M,x,T),A===null?D=M:A.sibling=M,A=M);return t&&j.forEach(function(Y){return e(b,Y)}),st&&yn(b,T),D}function v(b,x,y,_){if(y==null)throw Error(O(151));for(var D=null,A=null,j=x,T=x=0,M=null,L=y.next();j!==null&&!L.done;T++,L=y.next()){j.index>T?(M=j,j=null):M=j.sibling;var Y=h(b,j,L.value,_);if(Y===null){j===null&&(j=M);break}t&&j&&Y.alternate===null&&e(b,j),x=s(Y,x,T),A===null?D=Y:A.sibling=Y,A=Y,j=M}if(L.done)return i(b,j),st&&yn(b,T),D;if(j===null){for(;!L.done;T++,L=y.next())L=f(b,L.value,_),L!==null&&(x=s(L,x,T),A===null?D=L:A.sibling=L,A=L);return st&&yn(b,T),D}for(j=n(j);!L.done;T++,L=y.next())L=g(j,b,T,L.value,_),L!==null&&(t&&L.alternate!==null&&j.delete(L.key===null?T:L.key),x=s(L,x,T),A===null?D=L:A.sibling=L,A=L);return t&&j.forEach(function(G){return e(b,G)}),st&&yn(b,T),D}function S(b,x,y,_){if(typeof y=="object"&&y!==null&&y.type===ia&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case Ml:t:{for(var D=y.key;x!==null;){if(x.key===D){if(D=y.type,D===ia){if(x.tag===7){i(b,x.sibling),_=a(x,y.props.children),_.return=b,b=_;break t}}else if(x.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===Li&&Uh(D)===x.type){i(b,x.sibling),_=a(x,y.props),es(_,y),_.return=b,b=_;break t}i(b,x);break}else e(b,x);x=x.sibling}y.type===ia?(_=jn(y.props.children,b.mode,_,y.key),_.return=b,b=_):(_=co(y.type,y.key,y.props,null,b.mode,_),es(_,y),_.return=b,b=_)}return l(b);case us:t:{for(D=y.key;x!==null;){if(x.key===D)if(x.tag===4&&x.stateNode.containerInfo===y.containerInfo&&x.stateNode.implementation===y.implementation){i(b,x.sibling),_=a(x,y.children||[]),_.return=b,b=_;break t}else{i(b,x);break}else e(b,x);x=x.sibling}_=rc(y,b.mode,_),_.return=b,b=_}return l(b);case Li:return D=y._init,y=D(y._payload),S(b,x,y,_)}if(ds(y))return m(b,x,y,_);if(Pa(y)){if(D=Pa(y),typeof D!="function")throw Error(O(150));return y=D.call(y),v(b,x,y,_)}if(typeof y.then=="function")return S(b,x,Hl(y),_);if(y.$$typeof===yi)return S(b,x,Ll(b,y),_);Ul(b,y)}return typeof y=="string"&&y!==""||typeof y=="number"||typeof y=="bigint"?(y=""+y,x!==null&&x.tag===6?(i(b,x.sibling),_=a(x,y),_.return=b,b=_):(i(b,x),_=oc(y,b.mode,_),_.return=b,b=_),l(b)):i(b,x)}return function(b,x,y,_){try{Vs=0;var D=S(b,x,y,_);return va=null,D}catch(j){if(j===fl||j===yr)throw j;var A=Se(29,j,null,b.mode);return A.lanes=_,A.return=b,A}finally{}}}var Ma=zx(!0),Tx=zx(!1),Ne=si(null),ii=null;function Ui(t){var e=t.alternate;Dt(Yt,Yt.current&1),Dt(Ne,t),ii===null&&(e===null||Ta.current!==null||e.memoizedState!==null)&&(ii=t)}function Ax(t){if(t.tag===22){if(Dt(Yt,Yt.current),Dt(Ne,t),ii===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(ii=t)}}else Yi()}function Yi(){Dt(Yt,Yt.current),Dt(Ne,Ne.current)}function ki(t){Qt(Ne),ii===t&&(ii=null),Qt(Yt)}var Yt=si(0);function Uo(t){for(var e=t;e!==null;){if(e.tag===13){var i=e.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||Bu(i)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function dc(t,e,i,n){e=t.memoizedState,i=i(n,e),i=i==null?e:St({},e,i),t.memoizedState=i,t.lanes===0&&(t.updateQueue.baseState=i)}var yu={enqueueSetState:function(t,e,i){t=t._reactInternals;var n=De(),a=Pi(n);a.payload=e,i!=null&&(a.callback=i),e=Ji(t,a,n),e!==null&&(we(e,t,n),ks(e,t,n))},enqueueReplaceState:function(t,e,i){t=t._reactInternals;var n=De(),a=Pi(n);a.tag=1,a.payload=e,i!=null&&(a.callback=i),e=Ji(t,a,n),e!==null&&(we(e,t,n),ks(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var i=De(),n=Pi(i);n.tag=2,e!=null&&(n.callback=e),e=Ji(t,n,i),e!==null&&(we(e,t,i),ks(e,t,i))}};function Yh(t,e,i,n,a,s,l){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,s,l):e.prototype&&e.prototype.isPureReactComponent?!Us(i,n)||!Us(a,s):!0}function Fh(t,e,i,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(i,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(i,n),e.state!==t&&yu.enqueueReplaceState(e,e.state,null)}function Nn(t,e){var i=e;if("ref"in e){i={};for(var n in e)n!=="ref"&&(i[n]=e[n])}if(t=t.defaultProps){i===e&&(i=St({},i));for(var a in t)i[a]===void 0&&(i[a]=t[a])}return i}var Yo=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Mx(t){Yo(t)}function Ox(t){console.error(t)}function Rx(t){Yo(t)}function Fo(t,e){try{var i=t.onUncaughtError;i(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function Vh(t,e,i){try{var n=t.onCaughtError;n(i.value,{componentStack:i.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function vu(t,e,i){return i=Pi(i),i.tag=3,i.payload={element:null},i.callback=function(){Fo(t,e)},i}function Ex(t){return t=Pi(t),t.tag=3,t}function Bx(t,e,i,n){var a=i.type.getDerivedStateFromError;if(typeof a=="function"){var s=n.value;t.payload=function(){return a(s)},t.callback=function(){Vh(e,i,n)}}var l=i.stateNode;l!==null&&typeof l.componentDidCatch=="function"&&(t.callback=function(){Vh(e,i,n),typeof a!="function"&&(Ii===null?Ii=new Set([this]):Ii.add(this));var o=n.stack;this.componentDidCatch(n.value,{componentStack:o!==null?o:""})})}function P1(t,e,i,n,a){if(i.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=i.alternate,e!==null&&ul(e,i,a,!0),i=Ne.current,i!==null){switch(i.tag){case 13:return ii===null?zu():i.alternate===null&&Tt===0&&(Tt=3),i.flags&=-257,i.flags|=65536,i.lanes=a,n===fu?i.flags|=16384:(e=i.updateQueue,e===null?i.updateQueue=new Set([n]):e.add(n),_c(t,n,a)),!1;case 22:return i.flags|=65536,n===fu?i.flags|=16384:(e=i.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},i.updateQueue=e):(i=e.retryQueue,i===null?e.retryQueue=new Set([n]):i.add(n)),_c(t,n,a)),!1}throw Error(O(435,i.tag))}return _c(t,n,a),zu(),!1}if(st)return e=Ne.current,e!==null?(!(e.flags&65536)&&(e.flags|=256),e.flags|=65536,e.lanes=a,n!==ou&&(t=Error(O(422),{cause:n}),Ys(Ee(t,i)))):(n!==ou&&(e=Error(O(423),{cause:n}),Ys(Ee(e,i))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,n=Ee(n,i),a=vu(t.stateNode,n,a),cc(t,a),Tt!==4&&(Tt=2)),!1;var s=Error(O(520),{cause:n});if(s=Ee(s,i),Ts===null?Ts=[s]:Ts.push(s),Tt!==4&&(Tt=2),e===null)return!0;n=Ee(n,i),i=e;do{switch(i.tag){case 3:return i.flags|=65536,t=a&-a,i.lanes|=t,t=vu(i.stateNode,n,t),cc(i,t),!1;case 1:if(e=i.type,s=i.stateNode,(i.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||s!==null&&typeof s.componentDidCatch=="function"&&(Ii===null||!Ii.has(s))))return i.flags|=65536,a&=-a,i.lanes|=a,a=Ex(a),Bx(a,t,i,n),cc(i,a),!1}i=i.return}while(i!==null);return!1}var Lx=Error(O(461)),Xt=!1;function Wt(t,e,i,n){e.child=t===null?Tx(e,null,i,n):Ma(e,t.child,i,n)}function Gh(t,e,i,n,a){i=i.render;var s=e.ref;if("ref"in n){var l={};for(var o in n)o!=="ref"&&(l[o]=n[o])}else l=n;return Bn(e),n=_d(t,e,i,l,s,a),o=kd(),t!==null&&!Xt?(Cd(t,e,a),zi(t,e,a)):(st&&o&&gd(e),e.flags|=1,Wt(t,e,n,a),e.child)}function qh(t,e,i,n,a){if(t===null){var s=i.type;return typeof s=="function"&&!pd(s)&&s.defaultProps===void 0&&i.compare===null?(e.tag=15,e.type=s,Nx(t,e,s,n,a)):(t=co(i.type,null,n,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(s=t.child,!Rd(t,a)){var l=s.memoizedProps;if(i=i.compare,i=i!==null?i:Us,i(l,n)&&t.ref===e.ref)return zi(t,e,a)}return e.flags|=1,t=Ci(s,n),t.ref=e.ref,t.return=e,e.child=t}function Nx(t,e,i,n,a){if(t!==null){var s=t.memoizedProps;if(Us(s,n)&&t.ref===e.ref)if(Xt=!1,e.pendingProps=n=s,Rd(t,a))t.flags&131072&&(Xt=!0);else return e.lanes=t.lanes,zi(t,e,a)}return Su(t,e,i,n,a)}function Hx(t,e,i){var n=e.pendingProps,a=n.children,s=t!==null?t.memoizedState:null;if(n.mode==="hidden"){if(e.flags&128){if(n=s!==null?s.baseLanes|i:i,t!==null){for(a=e.child=t.child,s=0;a!==null;)s=s|a.lanes|a.childLanes,a=a.sibling;e.childLanes=s&~n}else e.childLanes=0,e.child=null;return Xh(t,e,n,i)}if(i&536870912)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&uo(e,s!==null?s.cachePool:null),s!==null?Oh(e,s):gu(),Ax(e);else return e.lanes=e.childLanes=536870912,Xh(t,e,s!==null?s.baseLanes|i:i,i)}else s!==null?(uo(e,s.cachePool),Oh(e,s),Yi(),e.memoizedState=null):(t!==null&&uo(e,null),gu(),Yi());return Wt(t,e,a,i),e.child}function Xh(t,e,i,n){var a=md();return a=a===null?null:{parent:Ut._currentValue,pool:a},e.memoizedState={baseLanes:i,cachePool:a},t!==null&&uo(e,null),gu(),Ax(e),t!==null&&ul(t,e,n,!0),null}function po(t,e){var i=e.ref;if(i===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof i!="function"&&typeof i!="object")throw Error(O(284));(t===null||t.ref!==i)&&(e.flags|=4194816)}}function Su(t,e,i,n,a){return Bn(e),i=_d(t,e,i,n,void 0,a),n=kd(),t!==null&&!Xt?(Cd(t,e,a),zi(t,e,a)):(st&&n&&gd(e),e.flags|=1,Wt(t,e,i,a),e.child)}function Qh(t,e,i,n,a,s){return Bn(e),e.updateQueue=null,i=Pb(e,n,i,a),$b(t),n=kd(),t!==null&&!Xt?(Cd(t,e,s),zi(t,e,s)):(st&&n&&gd(e),e.flags|=1,Wt(t,e,i,s),e.child)}function Zh(t,e,i,n,a){if(Bn(e),e.stateNode===null){var s=ua,l=i.contextType;typeof l=="object"&&l!==null&&(s=It(l)),s=new i(n,s),e.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,s.updater=yu,e.stateNode=s,s._reactInternals=e,s=e.stateNode,s.props=n,s.state=e.memoizedState,s.refs={},yd(e),l=i.contextType,s.context=typeof l=="object"&&l!==null?It(l):ua,s.state=e.memoizedState,l=i.getDerivedStateFromProps,typeof l=="function"&&(dc(e,i,l,n),s.state=e.memoizedState),typeof i.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(l=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),l!==s.state&&yu.enqueueReplaceState(s,s.state,null),Ds(e,n,s,a),Cs(),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){s=e.stateNode;var o=e.memoizedProps,r=Nn(i,o);s.props=r;var c=s.context,d=i.contextType;l=ua,typeof d=="object"&&d!==null&&(l=It(d));var f=i.getDerivedStateFromProps;d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function",o=e.pendingProps!==o,d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(o||c!==l)&&Fh(e,s,n,l),Ni=!1;var h=e.memoizedState;s.state=h,Ds(e,n,s,a),Cs(),c=e.memoizedState,o||h!==c||Ni?(typeof f=="function"&&(dc(e,i,f,n),c=e.memoizedState),(r=Ni||Yh(e,i,r,n,h,c,l))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(e.flags|=4194308)):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=c),s.props=n,s.state=c,s.context=l,n=r):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{s=e.stateNode,hu(t,e),l=e.memoizedProps,d=Nn(i,l),s.props=d,f=e.pendingProps,h=s.context,c=i.contextType,r=ua,typeof c=="object"&&c!==null&&(r=It(c)),o=i.getDerivedStateFromProps,(c=typeof o=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==f||h!==r)&&Fh(e,s,n,r),Ni=!1,h=e.memoizedState,s.state=h,Ds(e,n,s,a),Cs();var g=e.memoizedState;l!==f||h!==g||Ni||t!==null&&t.dependencies!==null&&Eo(t.dependencies)?(typeof o=="function"&&(dc(e,i,o,n),g=e.memoizedState),(d=Ni||Yh(e,i,d,n,h,g,r)||t!==null&&t.dependencies!==null&&Eo(t.dependencies))?(c||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(n,g,r),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(n,g,r)),typeof s.componentDidUpdate=="function"&&(e.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===t.memoizedProps&&h===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===t.memoizedProps&&h===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=g),s.props=n,s.state=g,s.context=r,n=d):(typeof s.componentDidUpdate!="function"||l===t.memoizedProps&&h===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===t.memoizedProps&&h===t.memoizedState||(e.flags|=1024),n=!1)}return s=n,po(t,e),n=(e.flags&128)!==0,s||n?(s=e.stateNode,i=n&&typeof i.getDerivedStateFromError!="function"?null:s.render(),e.flags|=1,t!==null&&n?(e.child=Ma(e,t.child,null,a),e.child=Ma(e,null,i,a)):Wt(t,e,i,a),e.memoizedState=s.state,t=e.child):t=zi(t,e,a),t}function Wh(t,e,i,n){return cl(),e.flags|=256,Wt(t,e,i,n),e.child}var fc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function hc(t){return{baseLanes:t,cachePool:Xb()}}function pc(t,e,i){return t=t!==null?t.childLanes&~i:0,e&&(t|=Be),t}function Ux(t,e,i){var n=e.pendingProps,a=!1,s=(e.flags&128)!==0,l;if((l=s)||(l=t!==null&&t.memoizedState===null?!1:(Yt.current&2)!==0),l&&(a=!0,e.flags&=-129),l=(e.flags&32)!==0,e.flags&=-33,t===null){if(st){if(a?Ui(e):Yi(),st){var o=zt,r;if(r=o){t:{for(r=o,o=Je;r.nodeType!==8;){if(!o){o=null;break t}if(r=Ge(r.nextSibling),r===null){o=null;break t}}o=r}o!==null?(e.memoizedState={dehydrated:o,treeContext:zn!==null?{id:vi,overflow:Si}:null,retryLane:536870912,hydrationErrors:null},r=Se(18,null,null,0),r.stateNode=o,r.return=e,e.child=r,ne=e,zt=null,r=!0):r=!1}r||En(e)}if(o=e.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return Bu(o)?e.lanes=32:e.lanes=536870912,null;ki(e)}return o=n.children,n=n.fallback,a?(Yi(),a=e.mode,o=Vo({mode:"hidden",children:o},a),n=jn(n,a,i,null),o.return=e,n.return=e,o.sibling=n,e.child=o,a=e.child,a.memoizedState=hc(i),a.childLanes=pc(t,l,i),e.memoizedState=fc,n):(Ui(e),_u(e,o))}if(r=t.memoizedState,r!==null&&(o=r.dehydrated,o!==null)){if(s)e.flags&256?(Ui(e),e.flags&=-257,e=gc(t,e,i)):e.memoizedState!==null?(Yi(),e.child=t.child,e.flags|=128,e=null):(Yi(),a=n.fallback,o=e.mode,n=Vo({mode:"visible",children:n.children},o),a=jn(a,o,i,null),a.flags|=2,n.return=e,a.return=e,n.sibling=a,e.child=n,Ma(e,t.child,null,i),n=e.child,n.memoizedState=hc(i),n.childLanes=pc(t,l,i),e.memoizedState=fc,e=a);else if(Ui(e),Bu(o)){if(l=o.nextSibling&&o.nextSibling.dataset,l)var c=l.dgst;l=c,n=Error(O(419)),n.stack="",n.digest=l,Ys({value:n,source:null,stack:null}),e=gc(t,e,i)}else if(Xt||ul(t,e,i,!1),l=(i&t.childLanes)!==0,Xt||l){if(l=yt,l!==null&&(n=i&-i,n=n&42?1:nd(n),n=n&(l.suspendedLanes|i)?0:n,n!==0&&n!==r.retryLane))throw r.retryLane=n,Va(t,n),we(l,t,n),Lx;o.data==="$?"||zu(),e=gc(t,e,i)}else o.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=r.treeContext,zt=Ge(o.nextSibling),ne=e,st=!0,Tn=null,Je=!1,t!==null&&(Me[Oe++]=vi,Me[Oe++]=Si,Me[Oe++]=zn,vi=t.id,Si=t.overflow,zn=e),e=_u(e,n.children),e.flags|=4096);return e}return a?(Yi(),a=n.fallback,o=e.mode,r=t.child,c=r.sibling,n=Ci(r,{mode:"hidden",children:n.children}),n.subtreeFlags=r.subtreeFlags&65011712,c!==null?a=Ci(c,a):(a=jn(a,o,i,null),a.flags|=2),a.return=e,n.return=e,n.sibling=a,e.child=n,n=a,a=e.child,o=t.child.memoizedState,o===null?o=hc(i):(r=o.cachePool,r!==null?(c=Ut._currentValue,r=r.parent!==c?{parent:c,pool:c}:r):r=Xb(),o={baseLanes:o.baseLanes|i,cachePool:r}),a.memoizedState=o,a.childLanes=pc(t,l,i),e.memoizedState=fc,n):(Ui(e),i=t.child,t=i.sibling,i=Ci(i,{mode:"visible",children:n.children}),i.return=e,i.sibling=null,t!==null&&(l=e.deletions,l===null?(e.deletions=[t],e.flags|=16):l.push(t)),e.child=i,e.memoizedState=null,i)}function _u(t,e){return e=Vo({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Vo(t,e){return t=Se(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function gc(t,e,i){return Ma(e,t.child,null,i),t=_u(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Kh(t,e,i){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),cu(t.return,e,i)}function bc(t,e,i,n,a){var s=t.memoizedState;s===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:i,tailMode:a}:(s.isBackwards=e,s.rendering=null,s.renderingStartTime=0,s.last=n,s.tail=i,s.tailMode=a)}function Yx(t,e,i){var n=e.pendingProps,a=n.revealOrder,s=n.tail;if(Wt(t,e,n.children,i),n=Yt.current,n&2)n=n&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Kh(t,i,e);else if(t.tag===19)Kh(t,i,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}n&=1}switch(Dt(Yt,n),a){case"forwards":for(i=e.child,a=null;i!==null;)t=i.alternate,t!==null&&Uo(t)===null&&(a=i),i=i.sibling;i=a,i===null?(a=e.child,e.child=null):(a=i.sibling,i.sibling=null),bc(e,!1,a,i,s);break;case"backwards":for(i=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&Uo(t)===null){e.child=a;break}t=a.sibling,a.sibling=i,i=a,a=t}bc(e,!0,i,null,s);break;case"together":bc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function zi(t,e,i){if(t!==null&&(e.dependencies=t.dependencies),rn|=e.lanes,!(i&e.childLanes))if(t!==null){if(ul(t,e,i,!1),(i&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(O(153));if(e.child!==null){for(t=e.child,i=Ci(t,t.pendingProps),e.child=i,i.return=e;t.sibling!==null;)t=t.sibling,i=i.sibling=Ci(t,t.pendingProps),i.return=e;i.sibling=null}return e.child}function Rd(t,e){return t.lanes&e?!0:(t=t.dependencies,!!(t!==null&&Eo(t)))}function J1(t,e,i){switch(e.tag){case 3:wo(e,e.stateNode.containerInfo),Hi(e,Ut,t.memoizedState.cache),cl();break;case 27:case 5:Pc(e);break;case 4:wo(e,e.stateNode.containerInfo);break;case 10:Hi(e,e.type,e.memoizedProps.value);break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(Ui(e),e.flags|=128,null):i&e.child.childLanes?Ux(t,e,i):(Ui(e),t=zi(t,e,i),t!==null?t.sibling:null);Ui(e);break;case 19:var a=(t.flags&128)!==0;if(n=(i&e.childLanes)!==0,n||(ul(t,e,i,!1),n=(i&e.childLanes)!==0),a){if(n)return Yx(t,e,i);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),Dt(Yt,Yt.current),n)break;return null;case 22:case 23:return e.lanes=0,Hx(t,e,i);case 24:Hi(e,Ut,t.memoizedState.cache)}return zi(t,e,i)}function Fx(t,e,i){if(t!==null)if(t.memoizedProps!==e.pendingProps)Xt=!0;else{if(!Rd(t,i)&&!(e.flags&128))return Xt=!1,J1(t,e,i);Xt=!!(t.flags&131072)}else Xt=!1,st&&e.flags&1048576&&Gb(e,Ro,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var n=e.elementType,a=n._init;if(n=a(n._payload),e.type=n,typeof n=="function")pd(n)?(t=Nn(n,t),e.tag=1,e=Zh(null,e,n,t,i)):(e.tag=0,e=Su(null,e,n,t,i));else{if(n!=null){if(a=n.$$typeof,a===td){e.tag=11,e=Gh(null,e,n,t,i);break t}else if(a===ed){e.tag=14,e=qh(null,e,n,t,i);break t}}throw e=Kc(n)||n,Error(O(306,e,""))}}return e;case 0:return Su(t,e,e.type,e.pendingProps,i);case 1:return n=e.type,a=Nn(n,e.pendingProps),Zh(t,e,n,a,i);case 3:t:{if(wo(e,e.stateNode.containerInfo),t===null)throw Error(O(387));n=e.pendingProps;var s=e.memoizedState;a=s.element,hu(t,e),Ds(e,n,null,i);var l=e.memoizedState;if(n=l.cache,Hi(e,Ut,n),n!==s.cache&&uu(e,[Ut],i,!0),Cs(),n=l.element,s.isDehydrated)if(s={element:n,isDehydrated:!1,cache:l.cache},e.updateQueue.baseState=s,e.memoizedState=s,e.flags&256){e=Wh(t,e,n,i);break t}else if(n!==a){a=Ee(Error(O(424)),e),Ys(a),e=Wh(t,e,n,i);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(zt=Ge(t.firstChild),ne=e,st=!0,Tn=null,Je=!0,i=Tx(e,null,n,i),e.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling}else{if(cl(),n===a){e=zi(t,e,i);break t}Wt(t,e,n,i)}e=e.child}return e;case 26:return po(t,e),t===null?(i=fp(e.type,null,e.pendingProps,null))?e.memoizedState=i:st||(i=e.type,t=e.pendingProps,n=Ko($i.current).createElement(i),n[Jt]=e,n[pe]=t,$t(n,i,t),qt(n),e.stateNode=n):e.memoizedState=fp(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Pc(e),t===null&&st&&(n=e.stateNode=jm(e.type,e.pendingProps,$i.current),ne=e,Je=!0,a=zt,dn(e.type)?(Lu=a,zt=Ge(n.firstChild)):zt=a),Wt(t,e,e.pendingProps.children,i),po(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&st&&((a=n=zt)&&(n=wv(n,e.type,e.pendingProps,Je),n!==null?(e.stateNode=n,ne=e,zt=Ge(n.firstChild),Je=!1,a=!0):a=!1),a||En(e)),Pc(e),a=e.type,s=e.pendingProps,l=t!==null?t.memoizedProps:null,n=s.children,Ru(a,s)?n=null:l!==null&&Ru(a,l)&&(e.flags|=32),e.memoizedState!==null&&(a=_d(t,e,q1,null,null,i),Qs._currentValue=a),po(t,e),Wt(t,e,n,i),e.child;case 6:return t===null&&st&&((t=i=zt)&&(i=jv(i,e.pendingProps,Je),i!==null?(e.stateNode=i,ne=e,zt=null,t=!0):t=!1),t||En(e)),null;case 13:return Ux(t,e,i);case 4:return wo(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=Ma(e,null,n,i):Wt(t,e,n,i),e.child;case 11:return Gh(t,e,e.type,e.pendingProps,i);case 7:return Wt(t,e,e.pendingProps,i),e.child;case 8:return Wt(t,e,e.pendingProps.children,i),e.child;case 12:return Wt(t,e,e.pendingProps.children,i),e.child;case 10:return n=e.pendingProps,Hi(e,e.type,n.value),Wt(t,e,n.children,i),e.child;case 9:return a=e.type._context,n=e.pendingProps.children,Bn(e),a=It(a),n=n(a),e.flags|=1,Wt(t,e,n,i),e.child;case 14:return qh(t,e,e.type,e.pendingProps,i);case 15:return Nx(t,e,e.type,e.pendingProps,i);case 19:return Yx(t,e,i);case 31:return n=e.pendingProps,i=e.mode,n={mode:n.mode,children:n.children},t===null?(i=Vo(n,i),i.ref=e.ref,e.child=i,i.return=e,e=i):(i=Ci(t.child,n),i.ref=e.ref,e.child=i,i.return=e,e=i),e;case 22:return Hx(t,e,i);case 24:return Bn(e),n=It(Ut),t===null?(a=md(),a===null&&(a=yt,s=xd(),a.pooledCache=s,s.refCount++,s!==null&&(a.pooledCacheLanes|=i),a=s),e.memoizedState={parent:n,cache:a},yd(e),Hi(e,Ut,a)):(t.lanes&i&&(hu(t,e),Ds(e,null,null,i),Cs()),a=t.memoizedState,s=e.memoizedState,a.parent!==n?(a={parent:n,cache:n},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),Hi(e,Ut,n)):(n=s.cache,Hi(e,Ut,n),n!==a.cache&&uu(e,[Ut],i,!0))),Wt(t,e,e.pendingProps.children,i),e.child;case 29:throw e.pendingProps}throw Error(O(156,e.tag))}function fi(t){t.flags|=4}function $h(t,e){if(e.type!=="stylesheet"||e.state.loading&4)t.flags&=-16777217;else if(t.flags|=16777216,!Am(e)){if(e=Ne.current,e!==null&&((it&4194048)===it?ii!==null:(it&62914560)!==it&&!(it&536870912)||e!==ii))throw _s=fu,Qb;t.flags|=8192}}function Yl(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?hb():536870912,t.lanes|=e,Oa|=e)}function is(t,e){if(!st)switch(t.tailMode){case"hidden":e=t.tail;for(var i=null;e!==null;)e.alternate!==null&&(i=e),e=e.sibling;i===null?t.tail=null:i.sibling=null;break;case"collapsed":i=t.tail;for(var n=null;i!==null;)i.alternate!==null&&(n=i),i=i.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function jt(t){var e=t.alternate!==null&&t.alternate.child===t.child,i=0,n=0;if(e)for(var a=t.child;a!==null;)i|=a.lanes|a.childLanes,n|=a.subtreeFlags&65011712,n|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)i|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=n,t.childLanes=i,e}function I1(t,e,i){var n=e.pendingProps;switch(bd(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return jt(e),null;case 1:return jt(e),null;case 3:return i=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Di(Ut),Da(),i.pendingContext&&(i.context=i.pendingContext,i.pendingContext=null),(t===null||t.child===null)&&(ts(e)?fi(e):t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,wh())),jt(e),null;case 26:return i=e.memoizedState,t===null?(fi(e),i!==null?(jt(e),$h(e,i)):(jt(e),e.flags&=-16777217)):i?i!==t.memoizedState?(fi(e),jt(e),$h(e,i)):(jt(e),e.flags&=-16777217):(t.memoizedProps!==n&&fi(e),jt(e),e.flags&=-16777217),null;case 27:jo(e),i=$i.current;var a=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==n&&fi(e);else{if(!n){if(e.stateNode===null)throw Error(O(166));return jt(e),null}t=ti.current,ts(e)?Ch(e):(t=jm(a,n,i),e.stateNode=t,fi(e))}return jt(e),null;case 5:if(jo(e),i=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&fi(e);else{if(!n){if(e.stateNode===null)throw Error(O(166));return jt(e),null}if(t=ti.current,ts(e))Ch(e);else{switch(a=Ko($i.current),t){case 1:t=a.createElementNS("http://www.w3.org/2000/svg",i);break;case 2:t=a.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;default:switch(i){case"svg":t=a.createElementNS("http://www.w3.org/2000/svg",i);break;case"math":t=a.createElementNS("http://www.w3.org/1998/Math/MathML",i);break;case"script":t=a.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof n.is=="string"?a.createElement("select",{is:n.is}):a.createElement("select"),n.multiple?t.multiple=!0:n.size&&(t.size=n.size);break;default:t=typeof n.is=="string"?a.createElement(i,{is:n.is}):a.createElement(i)}}t[Jt]=e,t[pe]=n;t:for(a=e.child;a!==null;){if(a.tag===5||a.tag===6)t.appendChild(a.stateNode);else if(a.tag!==4&&a.tag!==27&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break t;for(;a.sibling===null;){if(a.return===null||a.return===e)break t;a=a.return}a.sibling.return=a.return,a=a.sibling}e.stateNode=t;t:switch($t(t,i,n),i){case"button":case"input":case"select":case"textarea":t=!!n.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&fi(e)}}return jt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&fi(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(O(166));if(t=$i.current,ts(e)){if(t=e.stateNode,i=e.memoizedProps,n=null,a=ne,a!==null)switch(a.tag){case 27:case 5:n=a.memoizedProps}t[Jt]=e,t=!!(t.nodeValue===i||n!==null&&n.suppressHydrationWarning===!0||Cm(t.nodeValue,i)),t||En(e)}else t=Ko(t).createTextNode(n),t[Jt]=e,e.stateNode=t}return jt(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=ts(e),n!==null&&n.dehydrated!==null){if(t===null){if(!a)throw Error(O(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(O(317));a[Jt]=e}else cl(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;jt(e),a=!1}else a=wh(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(ki(e),e):(ki(e),null)}if(ki(e),e.flags&128)return e.lanes=i,e;if(i=n!==null,t=t!==null&&t.memoizedState!==null,i){n=e.child,a=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(a=n.alternate.memoizedState.cachePool.pool);var s=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(s=n.memoizedState.cachePool.pool),s!==a&&(n.flags|=2048)}return i!==t&&i&&(e.child.flags|=8192),Yl(e,e.updateQueue),jt(e),null;case 4:return Da(),t===null&&Fd(e.stateNode.containerInfo),jt(e),null;case 10:return Di(e.type),jt(e),null;case 19:if(Qt(Yt),a=e.memoizedState,a===null)return jt(e),null;if(n=(e.flags&128)!==0,s=a.rendering,s===null)if(n)is(a,!1);else{if(Tt!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(s=Uo(t),s!==null){for(e.flags|=128,is(a,!1),t=s.updateQueue,e.updateQueue=t,Yl(e,t),e.subtreeFlags=0,t=i,i=e.child;i!==null;)Vb(i,t),i=i.sibling;return Dt(Yt,Yt.current&1|2),e.child}t=t.sibling}a.tail!==null&&ei()>qo&&(e.flags|=128,n=!0,is(a,!1),e.lanes=4194304)}else{if(!n)if(t=Uo(s),t!==null){if(e.flags|=128,n=!0,t=t.updateQueue,e.updateQueue=t,Yl(e,t),is(a,!0),a.tail===null&&a.tailMode==="hidden"&&!s.alternate&&!st)return jt(e),null}else 2*ei()-a.renderingStartTime>qo&&i!==536870912&&(e.flags|=128,n=!0,is(a,!1),e.lanes=4194304);a.isBackwards?(s.sibling=e.child,e.child=s):(t=a.last,t!==null?t.sibling=s:e.child=s,a.last=s)}return a.tail!==null?(e=a.tail,a.rendering=e,a.tail=e.sibling,a.renderingStartTime=ei(),e.sibling=null,t=Yt.current,Dt(Yt,n?t&1|2:t&1),e):(jt(e),null);case 22:case 23:return ki(e),vd(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?i&536870912&&!(e.flags&128)&&(jt(e),e.subtreeFlags&6&&(e.flags|=8192)):jt(e),i=e.updateQueue,i!==null&&Yl(e,i.retryQueue),i=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==i&&(e.flags|=2048),t!==null&&Qt(An),null;case 24:return i=null,t!==null&&(i=t.memoizedState.cache),e.memoizedState.cache!==i&&(e.flags|=2048),Di(Ut),jt(e),null;case 25:return null;case 30:return null}throw Error(O(156,e.tag))}function tv(t,e){switch(bd(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Di(Ut),Da(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return jo(e),null;case 13:if(ki(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(O(340));cl()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return Qt(Yt),null;case 4:return Da(),null;case 10:return Di(e.type),null;case 22:case 23:return ki(e),vd(),t!==null&&Qt(An),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Di(Ut),null;case 25:return null;default:return null}}function Vx(t,e){switch(bd(e),e.tag){case 3:Di(Ut),Da();break;case 26:case 27:case 5:jo(e);break;case 4:Da();break;case 13:ki(e);break;case 19:Qt(Yt);break;case 10:Di(e.type);break;case 22:case 23:ki(e),vd(),t!==null&&Qt(An);break;case 24:Di(Ut)}}function gl(t,e){try{var i=e.updateQueue,n=i!==null?i.lastEffect:null;if(n!==null){var a=n.next;i=a;do{if((i.tag&t)===t){n=void 0;var s=i.create,l=i.inst;n=s(),l.destroy=n}i=i.next}while(i!==a)}}catch(o){xt(e,e.return,o)}}function on(t,e,i){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var s=a.next;n=s;do{if((n.tag&t)===t){var l=n.inst,o=l.destroy;if(o!==void 0){l.destroy=void 0,a=e;var r=i,c=o;try{c()}catch(d){xt(a,r,d)}}}n=n.next}while(n!==s)}}catch(d){xt(e,e.return,d)}}function Gx(t){var e=t.updateQueue;if(e!==null){var i=t.stateNode;try{Kb(e,i)}catch(n){xt(t,t.return,n)}}}function qx(t,e,i){i.props=Nn(t.type,t.memoizedProps),i.state=t.memoizedState;try{i.componentWillUnmount()}catch(n){xt(t,e,n)}}function js(t,e){try{var i=t.ref;if(i!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof i=="function"?t.refCleanup=i(n):i.current=n}}catch(a){xt(t,e,a)}}function Ie(t,e){var i=t.ref,n=t.refCleanup;if(i!==null)if(typeof n=="function")try{n()}catch(a){xt(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof i=="function")try{i(null)}catch(a){xt(t,e,a)}else i.current=null}function Xx(t){var e=t.type,i=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":i.autoFocus&&n.focus();break t;case"img":i.src?n.src=i.src:i.srcSet&&(n.srcset=i.srcSet)}}catch(a){xt(t,t.return,a)}}function xc(t,e,i){try{var n=t.stateNode;Sv(n,t.type,i,e),n[pe]=e}catch(a){xt(t,t.return,a)}}function Qx(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&dn(t.type)||t.tag===4}function mc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||Qx(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&dn(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ku(t,e,i){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i).insertBefore(t,e):(e=i.nodeType===9?i.body:i.nodeName==="HTML"?i.ownerDocument.body:i,e.appendChild(t),i=i._reactRootContainer,i!=null||e.onclick!==null||(e.onclick=wr));else if(n!==4&&(n===27&&dn(t.type)&&(i=t.stateNode,e=null),t=t.child,t!==null))for(ku(t,e,i),t=t.sibling;t!==null;)ku(t,e,i),t=t.sibling}function Go(t,e,i){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?i.insertBefore(t,e):i.appendChild(t);else if(n!==4&&(n===27&&dn(t.type)&&(i=t.stateNode),t=t.child,t!==null))for(Go(t,e,i),t=t.sibling;t!==null;)Go(t,e,i),t=t.sibling}function Zx(t){var e=t.stateNode,i=t.memoizedProps;try{for(var n=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);$t(e,n,i),e[Jt]=t,e[pe]=i}catch(s){xt(t,t.return,s)}}var mi=!1,Rt=!1,yc=!1,Ph=typeof WeakSet=="function"?WeakSet:Set,Gt=null;function ev(t,e){if(t=t.containerInfo,Mu=Io,t=Eb(t),dd(t)){if("selectionStart"in t)var i={start:t.selectionStart,end:t.selectionEnd};else t:{i=(i=t.ownerDocument)&&i.defaultView||window;var n=i.getSelection&&i.getSelection();if(n&&n.rangeCount!==0){i=n.anchorNode;var a=n.anchorOffset,s=n.focusNode;n=n.focusOffset;try{i.nodeType,s.nodeType}catch{i=null;break t}var l=0,o=-1,r=-1,c=0,d=0,f=t,h=null;e:for(;;){for(var g;f!==i||a!==0&&f.nodeType!==3||(o=l+a),f!==s||n!==0&&f.nodeType!==3||(r=l+n),f.nodeType===3&&(l+=f.nodeValue.length),(g=f.firstChild)!==null;)h=f,f=g;for(;;){if(f===t)break e;if(h===i&&++c===a&&(o=l),h===s&&++d===n&&(r=l),(g=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=g}i=o===-1||r===-1?null:{start:o,end:r}}else i=null}i=i||{start:0,end:0}}else i=null;for(Ou={focusedElem:t,selectionRange:i},Io=!1,Gt=e;Gt!==null;)if(e=Gt,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Gt=t;else for(;Gt!==null;){switch(e=Gt,s=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if(t&1024&&s!==null){t=void 0,i=e,a=s.memoizedProps,s=s.memoizedState,n=i.stateNode;try{var m=Nn(i.type,a,i.elementType===i.type);t=n.getSnapshotBeforeUpdate(m,s),n.__reactInternalSnapshotBeforeUpdate=t}catch(v){xt(i,i.return,v)}}break;case 3:if(t&1024){if(t=e.stateNode.containerInfo,i=t.nodeType,i===9)Eu(t);else if(i===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":Eu(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if(t&1024)throw Error(O(163))}if(t=e.sibling,t!==null){t.return=e.return,Gt=t;break}Gt=e.return}}function Wx(t,e,i){var n=i.flags;switch(i.tag){case 0:case 11:case 15:Ri(t,i),n&4&&gl(5,i);break;case 1:if(Ri(t,i),n&4)if(t=i.stateNode,e===null)try{t.componentDidMount()}catch(l){xt(i,i.return,l)}else{var a=Nn(i.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(l){xt(i,i.return,l)}}n&64&&Gx(i),n&512&&js(i,i.return);break;case 3:if(Ri(t,i),n&64&&(t=i.updateQueue,t!==null)){if(e=null,i.child!==null)switch(i.child.tag){case 27:case 5:e=i.child.stateNode;break;case 1:e=i.child.stateNode}try{Kb(t,e)}catch(l){xt(i,i.return,l)}}break;case 27:e===null&&n&4&&Zx(i);case 26:case 5:Ri(t,i),e===null&&n&4&&Xx(i),n&512&&js(i,i.return);break;case 12:Ri(t,i);break;case 13:Ri(t,i),n&4&&Px(t,i),n&64&&(t=i.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(i=uv.bind(null,i),zv(t,i))));break;case 22:if(n=i.memoizedState!==null||mi,!n){e=e!==null&&e.memoizedState!==null||Rt,a=mi;var s=Rt;mi=n,(Rt=e)&&!s?Bi(t,i,(i.subtreeFlags&8772)!==0):Ri(t,i),mi=a,Rt=s}break;case 30:break;default:Ri(t,i)}}function Kx(t){var e=t.alternate;e!==null&&(t.alternate=null,Kx(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&sd(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var kt=null,de=!1;function hi(t,e,i){for(i=i.child;i!==null;)$x(t,e,i),i=i.sibling}function $x(t,e,i){if(ke&&typeof ke.onCommitFiberUnmount=="function")try{ke.onCommitFiberUnmount(al,i)}catch{}switch(i.tag){case 26:Rt||Ie(i,e),hi(t,e,i),i.memoizedState?i.memoizedState.count--:i.stateNode&&(i=i.stateNode,i.parentNode.removeChild(i));break;case 27:Rt||Ie(i,e);var n=kt,a=de;dn(i.type)&&(kt=i.stateNode,de=!1),hi(t,e,i),Ms(i.stateNode),kt=n,de=a;break;case 5:Rt||Ie(i,e);case 6:if(n=kt,a=de,kt=null,hi(t,e,i),kt=n,de=a,kt!==null)if(de)try{(kt.nodeType===9?kt.body:kt.nodeName==="HTML"?kt.ownerDocument.body:kt).removeChild(i.stateNode)}catch(s){xt(i,e,s)}else try{kt.removeChild(i.stateNode)}catch(s){xt(i,e,s)}break;case 18:kt!==null&&(de?(t=kt,cp(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,i.stateNode),Ks(t)):cp(kt,i.stateNode));break;case 4:n=kt,a=de,kt=i.stateNode.containerInfo,de=!0,hi(t,e,i),kt=n,de=a;break;case 0:case 11:case 14:case 15:Rt||on(2,i,e),Rt||on(4,i,e),hi(t,e,i);break;case 1:Rt||(Ie(i,e),n=i.stateNode,typeof n.componentWillUnmount=="function"&&qx(i,e,n)),hi(t,e,i);break;case 21:hi(t,e,i);break;case 22:Rt=(n=Rt)||i.memoizedState!==null,hi(t,e,i),Rt=n;break;default:hi(t,e,i)}}function Px(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ks(t)}catch(i){xt(e,e.return,i)}}function iv(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new Ph),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new Ph),e;default:throw Error(O(435,t.tag))}}function vc(t,e){var i=iv(t);e.forEach(function(n){var a=dv.bind(null,t,n);i.has(n)||(i.add(n),n.then(a,a))})}function me(t,e){var i=e.deletions;if(i!==null)for(var n=0;n<i.length;n++){var a=i[n],s=t,l=e,o=l;t:for(;o!==null;){switch(o.tag){case 27:if(dn(o.type)){kt=o.stateNode,de=!1;break t}break;case 5:kt=o.stateNode,de=!1;break t;case 3:case 4:kt=o.stateNode.containerInfo,de=!0;break t}o=o.return}if(kt===null)throw Error(O(160));$x(s,l,a),kt=null,de=!1,s=a.alternate,s!==null&&(s.return=null),a.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)Jx(e,t),e=e.sibling}var Fe=null;function Jx(t,e){var i=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:me(e,t),ye(t),n&4&&(on(3,t,t.return),gl(3,t),on(5,t,t.return));break;case 1:me(e,t),ye(t),n&512&&(Rt||i===null||Ie(i,i.return)),n&64&&mi&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(i=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=i===null?n:i.concat(n))));break;case 26:var a=Fe;if(me(e,t),ye(t),n&512&&(Rt||i===null||Ie(i,i.return)),n&4){var s=i!==null?i.memoizedState:null;if(n=t.memoizedState,i===null)if(n===null)if(t.stateNode===null){t:{n=t.type,i=t.memoizedProps,a=a.ownerDocument||a;e:switch(n){case"title":s=a.getElementsByTagName("title")[0],(!s||s[ol]||s[Jt]||s.namespaceURI==="http://www.w3.org/2000/svg"||s.hasAttribute("itemprop"))&&(s=a.createElement(n),a.head.insertBefore(s,a.querySelector("head > title"))),$t(s,n,i),s[Jt]=t,qt(s),n=s;break t;case"link":var l=pp("link","href",a).get(n+(i.href||""));if(l){for(var o=0;o<l.length;o++)if(s=l[o],s.getAttribute("href")===(i.href==null||i.href===""?null:i.href)&&s.getAttribute("rel")===(i.rel==null?null:i.rel)&&s.getAttribute("title")===(i.title==null?null:i.title)&&s.getAttribute("crossorigin")===(i.crossOrigin==null?null:i.crossOrigin)){l.splice(o,1);break e}}s=a.createElement(n),$t(s,n,i),a.head.appendChild(s);break;case"meta":if(l=pp("meta","content",a).get(n+(i.content||""))){for(o=0;o<l.length;o++)if(s=l[o],s.getAttribute("content")===(i.content==null?null:""+i.content)&&s.getAttribute("name")===(i.name==null?null:i.name)&&s.getAttribute("property")===(i.property==null?null:i.property)&&s.getAttribute("http-equiv")===(i.httpEquiv==null?null:i.httpEquiv)&&s.getAttribute("charset")===(i.charSet==null?null:i.charSet)){l.splice(o,1);break e}}s=a.createElement(n),$t(s,n,i),a.head.appendChild(s);break;default:throw Error(O(468,n))}s[Jt]=t,qt(s),n=s}t.stateNode=n}else gp(a,t.type,t.stateNode);else t.stateNode=hp(a,n,t.memoizedProps);else s!==n?(s===null?i.stateNode!==null&&(i=i.stateNode,i.parentNode.removeChild(i)):s.count--,n===null?gp(a,t.type,t.stateNode):hp(a,n,t.memoizedProps)):n===null&&t.stateNode!==null&&xc(t,t.memoizedProps,i.memoizedProps)}break;case 27:me(e,t),ye(t),n&512&&(Rt||i===null||Ie(i,i.return)),i!==null&&n&4&&xc(t,t.memoizedProps,i.memoizedProps);break;case 5:if(me(e,t),ye(t),n&512&&(Rt||i===null||Ie(i,i.return)),t.flags&32){a=t.stateNode;try{ja(a,"")}catch(g){xt(t,t.return,g)}}n&4&&t.stateNode!=null&&(a=t.memoizedProps,xc(t,a,i!==null?i.memoizedProps:a)),n&1024&&(yc=!0);break;case 6:if(me(e,t),ye(t),n&4){if(t.stateNode===null)throw Error(O(162));n=t.memoizedProps,i=t.stateNode;try{i.nodeValue=n}catch(g){xt(t,t.return,g)}}break;case 3:if(xo=null,a=Fe,Fe=$o(e.containerInfo),me(e,t),Fe=a,ye(t),n&4&&i!==null&&i.memoizedState.isDehydrated)try{Ks(e.containerInfo)}catch(g){xt(t,t.return,g)}yc&&(yc=!1,Ix(t));break;case 4:n=Fe,Fe=$o(t.stateNode.containerInfo),me(e,t),ye(t),Fe=n;break;case 12:me(e,t),ye(t);break;case 13:me(e,t),ye(t),t.child.flags&8192&&t.memoizedState!==null!=(i!==null&&i.memoizedState!==null)&&(Hd=ei()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,vc(t,n)));break;case 22:a=t.memoizedState!==null;var r=i!==null&&i.memoizedState!==null,c=mi,d=Rt;if(mi=c||a,Rt=d||r,me(e,t),Rt=d,mi=c,ye(t),n&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(i===null||r||mi||Rt||vn(t)),i=null,e=t;;){if(e.tag===5||e.tag===26){if(i===null){r=i=e;try{if(s=r.stateNode,a)l=s.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none";else{o=r.stateNode;var f=r.memoizedProps.style,h=f!=null&&f.hasOwnProperty("display")?f.display:null;o.style.display=h==null||typeof h=="boolean"?"":(""+h).trim()}}catch(g){xt(r,r.return,g)}}}else if(e.tag===6){if(i===null){r=e;try{r.stateNode.nodeValue=a?"":r.memoizedProps}catch(g){xt(r,r.return,g)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;i===e&&(i=null),e=e.return}i===e&&(i=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(i=n.retryQueue,i!==null&&(n.retryQueue=null,vc(t,i))));break;case 19:me(e,t),ye(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,vc(t,n)));break;case 30:break;case 21:break;default:me(e,t),ye(t)}}function ye(t){var e=t.flags;if(e&2){try{for(var i,n=t.return;n!==null;){if(Qx(n)){i=n;break}n=n.return}if(i==null)throw Error(O(160));switch(i.tag){case 27:var a=i.stateNode,s=mc(t);Go(t,s,a);break;case 5:var l=i.stateNode;i.flags&32&&(ja(l,""),i.flags&=-33);var o=mc(t);Go(t,o,l);break;case 3:case 4:var r=i.stateNode.containerInfo,c=mc(t);ku(t,c,r);break;default:throw Error(O(161))}}catch(d){xt(t,t.return,d)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Ix(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Ix(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ri(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)Wx(t,e.alternate,e),e=e.sibling}function vn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:on(4,e,e.return),vn(e);break;case 1:Ie(e,e.return);var i=e.stateNode;typeof i.componentWillUnmount=="function"&&qx(e,e.return,i),vn(e);break;case 27:Ms(e.stateNode);case 26:case 5:Ie(e,e.return),vn(e);break;case 22:e.memoizedState===null&&vn(e);break;case 30:vn(e);break;default:vn(e)}t=t.sibling}}function Bi(t,e,i){for(i=i&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,a=t,s=e,l=s.flags;switch(s.tag){case 0:case 11:case 15:Bi(a,s,i),gl(4,s);break;case 1:if(Bi(a,s,i),n=s,a=n.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(c){xt(n,n.return,c)}if(n=s,a=n.updateQueue,a!==null){var o=n.stateNode;try{var r=a.shared.hiddenCallbacks;if(r!==null)for(a.shared.hiddenCallbacks=null,a=0;a<r.length;a++)Wb(r[a],o)}catch(c){xt(n,n.return,c)}}i&&l&64&&Gx(s),js(s,s.return);break;case 27:Zx(s);case 26:case 5:Bi(a,s,i),i&&n===null&&l&4&&Xx(s),js(s,s.return);break;case 12:Bi(a,s,i);break;case 13:Bi(a,s,i),i&&l&4&&Px(a,s);break;case 22:s.memoizedState===null&&Bi(a,s,i),js(s,s.return);break;case 30:break;default:Bi(a,s,i)}e=e.sibling}}function Ed(t,e){var i=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(i=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==i&&(t!=null&&t.refCount++,i!=null&&dl(i))}function Bd(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&dl(t))}function Ze(t,e,i,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)tm(t,e,i,n),e=e.sibling}function tm(t,e,i,n){var a=e.flags;switch(e.tag){case 0:case 11:case 15:Ze(t,e,i,n),a&2048&&gl(9,e);break;case 1:Ze(t,e,i,n);break;case 3:Ze(t,e,i,n),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&dl(t)));break;case 12:if(a&2048){Ze(t,e,i,n),t=e.stateNode;try{var s=e.memoizedProps,l=s.id,o=s.onPostCommit;typeof o=="function"&&o(l,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(r){xt(e,e.return,r)}}else Ze(t,e,i,n);break;case 13:Ze(t,e,i,n);break;case 23:break;case 22:s=e.stateNode,l=e.alternate,e.memoizedState!==null?s._visibility&2?Ze(t,e,i,n):zs(t,e):s._visibility&2?Ze(t,e,i,n):(s._visibility|=2,ta(t,e,i,n,(e.subtreeFlags&10256)!==0)),a&2048&&Ed(l,e);break;case 24:Ze(t,e,i,n),a&2048&&Bd(e.alternate,e);break;default:Ze(t,e,i,n)}}function ta(t,e,i,n,a){for(a=a&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var s=t,l=e,o=i,r=n,c=l.flags;switch(l.tag){case 0:case 11:case 15:ta(s,l,o,r,a),gl(8,l);break;case 23:break;case 22:var d=l.stateNode;l.memoizedState!==null?d._visibility&2?ta(s,l,o,r,a):zs(s,l):(d._visibility|=2,ta(s,l,o,r,a)),a&&c&2048&&Ed(l.alternate,l);break;case 24:ta(s,l,o,r,a),a&&c&2048&&Bd(l.alternate,l);break;default:ta(s,l,o,r,a)}e=e.sibling}}function zs(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var i=t,n=e,a=n.flags;switch(n.tag){case 22:zs(i,n),a&2048&&Ed(n.alternate,n);break;case 24:zs(i,n),a&2048&&Bd(n.alternate,n);break;default:zs(i,n)}e=e.sibling}}var hs=8192;function Kn(t){if(t.subtreeFlags&hs)for(t=t.child;t!==null;)em(t),t=t.sibling}function em(t){switch(t.tag){case 26:Kn(t),t.flags&hs&&t.memoizedState!==null&&Fv(Fe,t.memoizedState,t.memoizedProps);break;case 5:Kn(t);break;case 3:case 4:var e=Fe;Fe=$o(t.stateNode.containerInfo),Kn(t),Fe=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=hs,hs=16777216,Kn(t),hs=e):Kn(t));break;default:Kn(t)}}function im(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function ns(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var i=0;i<e.length;i++){var n=e[i];Gt=n,am(n,t)}im(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)nm(t),t=t.sibling}function nm(t){switch(t.tag){case 0:case 11:case 15:ns(t),t.flags&2048&&on(9,t,t.return);break;case 3:ns(t);break;case 12:ns(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,go(t)):ns(t);break;default:ns(t)}}function go(t){var e=t.deletions;if(t.flags&16){if(e!==null)for(var i=0;i<e.length;i++){var n=e[i];Gt=n,am(n,t)}im(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:on(8,e,e.return),go(e);break;case 22:i=e.stateNode,i._visibility&2&&(i._visibility&=-3,go(e));break;default:go(e)}t=t.sibling}}function am(t,e){for(;Gt!==null;){var i=Gt;switch(i.tag){case 0:case 11:case 15:on(8,i,e);break;case 23:case 22:if(i.memoizedState!==null&&i.memoizedState.cachePool!==null){var n=i.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:dl(i.memoizedState.cache)}if(n=i.child,n!==null)n.return=i,Gt=n;else t:for(i=t;Gt!==null;){n=Gt;var a=n.sibling,s=n.return;if(Kx(n),n===i){Gt=null;break t}if(a!==null){a.return=s,Gt=a;break t}Gt=s}}}var nv={getCacheForType:function(t){var e=It(Ut),i=e.data.get(t);return i===void 0&&(i=t(),e.data.set(t,i)),i}},av=typeof WeakMap=="function"?WeakMap:Map,ft=0,yt=null,I=null,it=0,ut=0,ve=null,qi=!1,Ga=!1,Ld=!1,Ti=0,Tt=0,rn=0,Mn=0,Nd=0,Be=0,Oa=0,Ts=null,fe=null,Cu=!1,Hd=0,qo=1/0,Xo=null,Ii=null,Kt=0,tn=null,Ra=null,Sa=0,Du=0,wu=null,sm=null,As=0,ju=null;function De(){if(ft&2&&it!==0)return it&-it;if(X.T!==null){var t=za;return t!==0?t:Yd()}return bb()}function lm(){Be===0&&(Be=!(it&536870912)||st?fb():536870912);var t=Ne.current;return t!==null&&(t.flags|=32),Be}function we(t,e,i){(t===yt&&(ut===2||ut===9)||t.cancelPendingCommit!==null)&&(Ea(t,0),Xi(t,it,Be,!1)),ll(t,i),(!(ft&2)||t!==yt)&&(t===yt&&(!(ft&2)&&(Mn|=i),Tt===4&&Xi(t,it,Be,!1)),li(t))}function om(t,e,i){if(ft&6)throw Error(O(327));var n=!i&&(e&124)===0&&(e&t.expiredLanes)===0||sl(t,e),a=n?ov(t,e):Sc(t,e,!0),s=n;do{if(a===0){Ga&&!n&&Xi(t,e,0,!1);break}else{if(i=t.current.alternate,s&&!sv(i)){a=Sc(t,e,!1),s=!1;continue}if(a===2){if(s=e,t.errorRecoveryDisabledLanes&s)var l=0;else l=t.pendingLanes&-536870913,l=l!==0?l:l&536870912?536870912:0;if(l!==0){e=l;t:{var o=t;a=Ts;var r=o.current.memoizedState.isDehydrated;if(r&&(Ea(o,l).flags|=256),l=Sc(o,l,!1),l!==2){if(Ld&&!r){o.errorRecoveryDisabledLanes|=s,Mn|=s,a=4;break t}s=fe,fe=a,s!==null&&(fe===null?fe=s:fe.push.apply(fe,s))}a=l}if(s=!1,a!==2)continue}}if(a===1){Ea(t,0),Xi(t,e,0,!0);break}t:{switch(n=t,s=a,s){case 0:case 1:throw Error(O(345));case 4:if((e&4194048)!==e)break;case 6:Xi(n,e,Be,!qi);break t;case 2:fe=null;break;case 3:case 5:break;default:throw Error(O(329))}if((e&62914560)===e&&(a=Hd+300-ei(),10<a)){if(Xi(n,e,Be,!qi),hr(n,0,!0)!==0)break t;n.timeoutHandle=wm(Jh.bind(null,n,i,fe,Xo,Cu,e,Be,Mn,Oa,qi,s,2,-0,0),a);break t}Jh(n,i,fe,Xo,Cu,e,Be,Mn,Oa,qi,s,0,-0,0)}}break}while(!0);li(t)}function Jh(t,e,i,n,a,s,l,o,r,c,d,f,h,g){if(t.timeoutHandle=-1,f=e.subtreeFlags,(f&8192||(f&16785408)===16785408)&&(Xs={stylesheets:null,count:0,unsuspend:Yv},em(e),f=Vv(),f!==null)){t.cancelPendingCommit=f(tp.bind(null,t,e,s,i,n,a,l,o,r,d,1,h,g)),Xi(t,s,l,!c);return}tp(t,e,s,i,n,a,l,o,r)}function sv(t){for(var e=t;;){var i=e.tag;if((i===0||i===11||i===15)&&e.flags&16384&&(i=e.updateQueue,i!==null&&(i=i.stores,i!==null)))for(var n=0;n<i.length;n++){var a=i[n],s=a.getSnapshot;a=a.value;try{if(!je(s(),a))return!1}catch{return!1}}if(i=e.child,e.subtreeFlags&16384&&i!==null)i.return=e,e=i;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Xi(t,e,i,n){e&=~Nd,e&=~Mn,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var a=e;0<a;){var s=31-Ce(a),l=1<<s;n[s]=-1,a&=~l}i!==0&&pb(t,i,e)}function kr(){return ft&6?!0:(bl(0),!1)}function Ud(){if(I!==null){if(ut===0)var t=I.return;else t=I,_i=Vn=null,Dd(t),va=null,Vs=0,t=I;for(;t!==null;)Vx(t.alternate,t),t=t.return;I=null}}function Ea(t,e){var i=t.timeoutHandle;i!==-1&&(t.timeoutHandle=-1,kv(i)),i=t.cancelPendingCommit,i!==null&&(t.cancelPendingCommit=null,i()),Ud(),yt=t,I=i=Ci(t.current,null),it=e,ut=0,ve=null,qi=!1,Ga=sl(t,e),Ld=!1,Oa=Be=Nd=Mn=rn=Tt=0,fe=Ts=null,Cu=!1,e&8&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var a=31-Ce(n),s=1<<a;e|=t[a],n&=~s}return Ti=e,xr(),i}function rm(t,e){$=null,X.H=Ho,e===fl||e===yr?(e=Ah(),ut=3):e===Qb?(e=Ah(),ut=4):ut=e===Lx?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,ve=e,I===null&&(Tt=1,Fo(t,Ee(e,t.current)))}function cm(){var t=X.H;return X.H=Ho,t===null?Ho:t}function um(){var t=X.A;return X.A=nv,t}function zu(){Tt=4,qi||(it&4194048)!==it&&Ne.current!==null||(Ga=!0),!(rn&134217727)&&!(Mn&134217727)||yt===null||Xi(yt,it,Be,!1)}function Sc(t,e,i){var n=ft;ft|=2;var a=cm(),s=um();(yt!==t||it!==e)&&(Xo=null,Ea(t,e)),e=!1;var l=Tt;t:do try{if(ut!==0&&I!==null){var o=I,r=ve;switch(ut){case 8:Ud(),l=6;break t;case 3:case 2:case 9:case 6:Ne.current===null&&(e=!0);var c=ut;if(ut=0,ve=null,ha(t,o,r,c),i&&Ga){l=0;break t}break;default:c=ut,ut=0,ve=null,ha(t,o,r,c)}}lv(),l=Tt;break}catch(d){rm(t,d)}while(!0);return e&&t.shellSuspendCounter++,_i=Vn=null,ft=n,X.H=a,X.A=s,I===null&&(yt=null,it=0,xr()),l}function lv(){for(;I!==null;)dm(I)}function ov(t,e){var i=ft;ft|=2;var n=cm(),a=um();yt!==t||it!==e?(Xo=null,qo=ei()+500,Ea(t,e)):Ga=sl(t,e);t:do try{if(ut!==0&&I!==null){e=I;var s=ve;e:switch(ut){case 1:ut=0,ve=null,ha(t,e,s,1);break;case 2:case 9:if(Th(s)){ut=0,ve=null,Ih(e);break}e=function(){ut!==2&&ut!==9||yt!==t||(ut=7),li(t)},s.then(e,e);break t;case 3:ut=7;break t;case 4:ut=5;break t;case 7:Th(s)?(ut=0,ve=null,Ih(e)):(ut=0,ve=null,ha(t,e,s,7));break;case 5:var l=null;switch(I.tag){case 26:l=I.memoizedState;case 5:case 27:var o=I;if(!l||Am(l)){ut=0,ve=null;var r=o.sibling;if(r!==null)I=r;else{var c=o.return;c!==null?(I=c,Cr(c)):I=null}break e}}ut=0,ve=null,ha(t,e,s,5);break;case 6:ut=0,ve=null,ha(t,e,s,6);break;case 8:Ud(),Tt=6;break t;default:throw Error(O(462))}}rv();break}catch(d){rm(t,d)}while(!0);return _i=Vn=null,X.H=n,X.A=a,ft=i,I!==null?0:(yt=null,it=0,xr(),Tt)}function rv(){for(;I!==null&&!A0();)dm(I)}function dm(t){var e=Fx(t.alternate,t,Ti);t.memoizedProps=t.pendingProps,e===null?Cr(t):I=e}function Ih(t){var e=t,i=e.alternate;switch(e.tag){case 15:case 0:e=Qh(i,e,e.pendingProps,e.type,void 0,it);break;case 11:e=Qh(i,e,e.pendingProps,e.type.render,e.ref,it);break;case 5:Dd(e);default:Vx(i,e),e=I=Vb(e,Ti),e=Fx(i,e,Ti)}t.memoizedProps=t.pendingProps,e===null?Cr(t):I=e}function ha(t,e,i,n){_i=Vn=null,Dd(e),va=null,Vs=0;var a=e.return;try{if(P1(t,a,e,i,it)){Tt=1,Fo(t,Ee(i,t.current)),I=null;return}}catch(s){if(a!==null)throw I=a,s;Tt=1,Fo(t,Ee(i,t.current)),I=null;return}e.flags&32768?(st||n===1?t=!0:Ga||it&536870912?t=!1:(qi=t=!0,(n===2||n===9||n===3||n===6)&&(n=Ne.current,n!==null&&n.tag===13&&(n.flags|=16384))),fm(e,t)):Cr(e)}function Cr(t){var e=t;do{if(e.flags&32768){fm(e,qi);return}t=e.return;var i=I1(e.alternate,e,Ti);if(i!==null){I=i;return}if(e=e.sibling,e!==null){I=e;return}I=e=t}while(e!==null);Tt===0&&(Tt=5)}function fm(t,e){do{var i=tv(t.alternate,t);if(i!==null){i.flags&=32767,I=i;return}if(i=t.return,i!==null&&(i.flags|=32768,i.subtreeFlags=0,i.deletions=null),!e&&(t=t.sibling,t!==null)){I=t;return}I=t=i}while(t!==null);Tt=6,I=null}function tp(t,e,i,n,a,s,l,o,r){t.cancelPendingCommit=null;do Dr();while(Kt!==0);if(ft&6)throw Error(O(327));if(e!==null){if(e===t.current)throw Error(O(177));if(s=e.lanes|e.childLanes,s|=fd,Y0(t,i,s,l,o,r),t===yt&&(I=yt=null,it=0),Ra=e,tn=t,Sa=i,Du=s,wu=a,sm=n,e.subtreeFlags&10256||e.flags&10256?(t.callbackNode=null,t.callbackPriority=0,fv(zo,function(){return xm(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,e.subtreeFlags&13878||n){n=X.T,X.T=null,a=lt.p,lt.p=2,l=ft,ft|=4;try{ev(t,e,i)}finally{ft=l,lt.p=a,X.T=n}}Kt=1,hm(),pm(),gm()}}function hm(){if(Kt===1){Kt=0;var t=tn,e=Ra,i=(e.flags&13878)!==0;if(e.subtreeFlags&13878||i){i=X.T,X.T=null;var n=lt.p;lt.p=2;var a=ft;ft|=4;try{Jx(e,t);var s=Ou,l=Eb(t.containerInfo),o=s.focusedElem,r=s.selectionRange;if(l!==o&&o&&o.ownerDocument&&Rb(o.ownerDocument.documentElement,o)){if(r!==null&&dd(o)){var c=r.start,d=r.end;if(d===void 0&&(d=c),"selectionStart"in o)o.selectionStart=c,o.selectionEnd=Math.min(d,o.value.length);else{var f=o.ownerDocument||document,h=f&&f.defaultView||window;if(h.getSelection){var g=h.getSelection(),m=o.textContent.length,v=Math.min(r.start,m),S=r.end===void 0?v:Math.min(r.end,m);!g.extend&&v>S&&(l=S,S=v,v=l);var b=Sh(o,v),x=Sh(o,S);if(b&&x&&(g.rangeCount!==1||g.anchorNode!==b.node||g.anchorOffset!==b.offset||g.focusNode!==x.node||g.focusOffset!==x.offset)){var y=f.createRange();y.setStart(b.node,b.offset),g.removeAllRanges(),v>S?(g.addRange(y),g.extend(x.node,x.offset)):(y.setEnd(x.node,x.offset),g.addRange(y))}}}}for(f=[],g=o;g=g.parentNode;)g.nodeType===1&&f.push({element:g,left:g.scrollLeft,top:g.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<f.length;o++){var _=f[o];_.element.scrollLeft=_.left,_.element.scrollTop=_.top}}Io=!!Mu,Ou=Mu=null}finally{ft=a,lt.p=n,X.T=i}}t.current=e,Kt=2}}function pm(){if(Kt===2){Kt=0;var t=tn,e=Ra,i=(e.flags&8772)!==0;if(e.subtreeFlags&8772||i){i=X.T,X.T=null;var n=lt.p;lt.p=2;var a=ft;ft|=4;try{Wx(t,e.alternate,e)}finally{ft=a,lt.p=n,X.T=i}}Kt=3}}function gm(){if(Kt===4||Kt===3){Kt=0,M0();var t=tn,e=Ra,i=Sa,n=sm;e.subtreeFlags&10256||e.flags&10256?Kt=5:(Kt=0,Ra=tn=null,bm(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&(Ii=null),ad(i),e=e.stateNode,ke&&typeof ke.onCommitFiberRoot=="function")try{ke.onCommitFiberRoot(al,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=X.T,a=lt.p,lt.p=2,X.T=null;try{for(var s=t.onRecoverableError,l=0;l<n.length;l++){var o=n[l];s(o.value,{componentStack:o.stack})}}finally{X.T=e,lt.p=a}}Sa&3&&Dr(),li(t),a=t.pendingLanes,i&4194090&&a&42?t===ju?As++:(As=0,ju=t):As=0,bl(0)}}function bm(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,dl(e)))}function Dr(t){return hm(),pm(),gm(),xm()}function xm(){if(Kt!==5)return!1;var t=tn,e=Du;Du=0;var i=ad(Sa),n=X.T,a=lt.p;try{lt.p=32>i?32:i,X.T=null,i=wu,wu=null;var s=tn,l=Sa;if(Kt=0,Ra=tn=null,Sa=0,ft&6)throw Error(O(331));var o=ft;if(ft|=4,nm(s.current),tm(s,s.current,l,i),ft=o,bl(0,!1),ke&&typeof ke.onPostCommitFiberRoot=="function")try{ke.onPostCommitFiberRoot(al,s)}catch{}return!0}finally{lt.p=a,X.T=n,bm(t,e)}}function ep(t,e,i){e=Ee(i,e),e=vu(t.stateNode,e,2),t=Ji(t,e,2),t!==null&&(ll(t,2),li(t))}function xt(t,e,i){if(t.tag===3)ep(t,t,i);else for(;e!==null;){if(e.tag===3){ep(e,t,i);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Ii===null||!Ii.has(n))){t=Ee(i,t),i=Ex(2),n=Ji(e,i,2),n!==null&&(Bx(i,n,e,t),ll(n,2),li(n));break}}e=e.return}}function _c(t,e,i){var n=t.pingCache;if(n===null){n=t.pingCache=new av;var a=new Set;n.set(e,a)}else a=n.get(e),a===void 0&&(a=new Set,n.set(e,a));a.has(i)||(Ld=!0,a.add(i),t=cv.bind(null,t,e,i),e.then(t,t))}function cv(t,e,i){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&i,t.warmLanes&=~i,yt===t&&(it&i)===i&&(Tt===4||Tt===3&&(it&62914560)===it&&300>ei()-Hd?!(ft&2)&&Ea(t,0):Nd|=i,Oa===it&&(Oa=0)),li(t)}function mm(t,e){e===0&&(e=hb()),t=Va(t,e),t!==null&&(ll(t,e),li(t))}function uv(t){var e=t.memoizedState,i=0;e!==null&&(i=e.retryLane),mm(t,i)}function dv(t,e){var i=0;switch(t.tag){case 13:var n=t.stateNode,a=t.memoizedState;a!==null&&(i=a.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(O(314))}n!==null&&n.delete(e),mm(t,i)}function fv(t,e){return id(t,e)}var Qo=null,ea=null,Tu=!1,Zo=!1,kc=!1,On=0;function li(t){t!==ea&&t.next===null&&(ea===null?Qo=ea=t:ea=ea.next=t),Zo=!0,Tu||(Tu=!0,pv())}function bl(t,e){if(!kc&&Zo){kc=!0;do for(var i=!1,n=Qo;n!==null;){if(t!==0){var a=n.pendingLanes;if(a===0)var s=0;else{var l=n.suspendedLanes,o=n.pingedLanes;s=(1<<31-Ce(42|t)+1)-1,s&=a&~(l&~o),s=s&201326741?s&201326741|1:s?s|2:0}s!==0&&(i=!0,ip(n,s))}else s=it,s=hr(n,n===yt?s:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),!(s&3)||sl(n,s)||(i=!0,ip(n,s));n=n.next}while(i);kc=!1}}function hv(){ym()}function ym(){Zo=Tu=!1;var t=0;On!==0&&(_v()&&(t=On),On=0);for(var e=ei(),i=null,n=Qo;n!==null;){var a=n.next,s=vm(n,e);s===0?(n.next=null,i===null?Qo=a:i.next=a,a===null&&(ea=i)):(i=n,(t!==0||s&3)&&(Zo=!0)),n=a}bl(t)}function vm(t,e){for(var i=t.suspendedLanes,n=t.pingedLanes,a=t.expirationTimes,s=t.pendingLanes&-62914561;0<s;){var l=31-Ce(s),o=1<<l,r=a[l];r===-1?(!(o&i)||o&n)&&(a[l]=U0(o,e)):r<=e&&(t.expiredLanes|=o),s&=~o}if(e=yt,i=it,i=hr(t,t===e?i:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,i===0||t===e&&(ut===2||ut===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&Kr(n),t.callbackNode=null,t.callbackPriority=0;if(!(i&3)||sl(t,i)){if(e=i&-i,e===t.callbackPriority)return e;switch(n!==null&&Kr(n),ad(i)){case 2:case 8:i=ub;break;case 32:i=zo;break;case 268435456:i=db;break;default:i=zo}return n=Sm.bind(null,t),i=id(i,n),t.callbackPriority=e,t.callbackNode=i,e}return n!==null&&n!==null&&Kr(n),t.callbackPriority=2,t.callbackNode=null,2}function Sm(t,e){if(Kt!==0&&Kt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var i=t.callbackNode;if(Dr()&&t.callbackNode!==i)return null;var n=it;return n=hr(t,t===yt?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(om(t,n,e),vm(t,ei()),t.callbackNode!=null&&t.callbackNode===i?Sm.bind(null,t):null)}function ip(t,e){if(Dr())return null;om(t,e,!0)}function pv(){Cv(function(){ft&6?id(cb,hv):ym()})}function Yd(){return On===0&&(On=fb()),On}function np(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:lo(""+t)}function ap(t,e){var i=e.ownerDocument.createElement("input");return i.name=e.name,i.value=e.value,t.id&&i.setAttribute("form",t.id),e.parentNode.insertBefore(i,e),t=new FormData(t),i.parentNode.removeChild(i),t}function gv(t,e,i,n,a){if(e==="submit"&&i&&i.stateNode===a){var s=np((a[pe]||null).action),l=n.submitter;l&&(e=(e=l[pe]||null)?np(e.formAction):l.getAttribute("formAction"),e!==null&&(s=e,l=null));var o=new pr("action","action",null,n,a);t.push({event:o,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(On!==0){var r=l?ap(a,l):new FormData(a);mu(i,{pending:!0,data:r,method:a.method,action:s},null,r)}}else typeof s=="function"&&(o.preventDefault(),r=l?ap(a,l):new FormData(a),mu(i,{pending:!0,data:r,method:a.method,action:s},s,r))},currentTarget:a}]})}}for(var Cc=0;Cc<lu.length;Cc++){var Dc=lu[Cc],bv=Dc.toLowerCase(),xv=Dc[0].toUpperCase()+Dc.slice(1);Xe(bv,"on"+xv)}Xe(Lb,"onAnimationEnd");Xe(Nb,"onAnimationIteration");Xe(Hb,"onAnimationStart");Xe("dblclick","onDoubleClick");Xe("focusin","onFocus");Xe("focusout","onBlur");Xe(E1,"onTransitionRun");Xe(B1,"onTransitionStart");Xe(L1,"onTransitionCancel");Xe(Ub,"onTransitionEnd");wa("onMouseEnter",["mouseout","mouseover"]);wa("onMouseLeave",["mouseout","mouseover"]);wa("onPointerEnter",["pointerout","pointerover"]);wa("onPointerLeave",["pointerout","pointerover"]);Un("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Un("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Un("onBeforeInput",["compositionend","keypress","textInput","paste"]);Un("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Un("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Un("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Gs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),mv=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Gs));function _m(t,e){e=(e&4)!==0;for(var i=0;i<t.length;i++){var n=t[i],a=n.event;n=n.listeners;t:{var s=void 0;if(e)for(var l=n.length-1;0<=l;l--){var o=n[l],r=o.instance,c=o.currentTarget;if(o=o.listener,r!==s&&a.isPropagationStopped())break t;s=o,a.currentTarget=c;try{s(a)}catch(d){Yo(d)}a.currentTarget=null,s=r}else for(l=0;l<n.length;l++){if(o=n[l],r=o.instance,c=o.currentTarget,o=o.listener,r!==s&&a.isPropagationStopped())break t;s=o,a.currentTarget=c;try{s(a)}catch(d){Yo(d)}a.currentTarget=null,s=r}}}}function J(t,e){var i=e[Ic];i===void 0&&(i=e[Ic]=new Set);var n=t+"__bubble";i.has(n)||(km(e,t,2,!1),i.add(n))}function wc(t,e,i){var n=0;e&&(n|=4),km(i,t,n,e)}var Fl="_reactListening"+Math.random().toString(36).slice(2);function Fd(t){if(!t[Fl]){t[Fl]=!0,xb.forEach(function(i){i!=="selectionchange"&&(mv.has(i)||wc(i,!1,t),wc(i,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Fl]||(e[Fl]=!0,wc("selectionchange",!1,e))}}function km(t,e,i,n){switch(Bm(e)){case 2:var a=Xv;break;case 8:a=Qv;break;default:a=Xd}i=a.bind(null,e,i,t),a=void 0,!nu||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),n?a!==void 0?t.addEventListener(e,i,{capture:!0,passive:a}):t.addEventListener(e,i,!0):a!==void 0?t.addEventListener(e,i,{passive:a}):t.addEventListener(e,i,!1)}function jc(t,e,i,n,a){var s=n;if(!(e&1)&&!(e&2)&&n!==null)t:for(;;){if(n===null)return;var l=n.tag;if(l===3||l===4){var o=n.stateNode.containerInfo;if(o===a)break;if(l===4)for(l=n.return;l!==null;){var r=l.tag;if((r===3||r===4)&&l.stateNode.containerInfo===a)return;l=l.return}for(;o!==null;){if(l=aa(o),l===null)return;if(r=l.tag,r===5||r===6||r===26||r===27){n=s=l;continue t}o=o.parentNode}}n=n.return}Db(function(){var c=s,d=od(i),f=[];t:{var h=Yb.get(t);if(h!==void 0){var g=pr,m=t;switch(t){case"keypress":if(ro(i)===0)break t;case"keydown":case"keyup":g=f1;break;case"focusin":m="focus",g=nc;break;case"focusout":m="blur",g=nc;break;case"beforeblur":case"afterblur":g=nc;break;case"click":if(i.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=dh;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=t1;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=g1;break;case Lb:case Nb:case Hb:g=n1;break;case Ub:g=x1;break;case"scroll":case"scrollend":g=J0;break;case"wheel":g=y1;break;case"copy":case"cut":case"paste":g=s1;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=hh;break;case"toggle":case"beforetoggle":g=S1}var v=(e&4)!==0,S=!v&&(t==="scroll"||t==="scrollend"),b=v?h!==null?h+"Capture":null:h;v=[];for(var x=c,y;x!==null;){var _=x;if(y=_.stateNode,_=_.tag,_!==5&&_!==26&&_!==27||y===null||b===null||(_=Ns(x,b),_!=null&&v.push(qs(x,_,y))),S)break;x=x.return}0<v.length&&(h=new g(h,m,null,i,d),f.push({event:h,listeners:v}))}}if(!(e&7)){t:{if(h=t==="mouseover"||t==="pointerover",g=t==="mouseout"||t==="pointerout",h&&i!==iu&&(m=i.relatedTarget||i.fromElement)&&(aa(m)||m[Ya]))break t;if((g||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,g?(m=i.relatedTarget||i.toElement,g=c,m=m?aa(m):null,m!==null&&(S=nl(m),v=m.tag,m!==S||v!==5&&v!==27&&v!==6)&&(m=null)):(g=null,m=c),g!==m)){if(v=dh,_="onMouseLeave",b="onMouseEnter",x="mouse",(t==="pointerout"||t==="pointerover")&&(v=hh,_="onPointerLeave",b="onPointerEnter",x="pointer"),S=g==null?h:fs(g),y=m==null?h:fs(m),h=new v(_,x+"leave",g,i,d),h.target=S,h.relatedTarget=y,_=null,aa(d)===c&&(v=new v(b,x+"enter",m,i,d),v.target=y,v.relatedTarget=S,_=v),S=_,g&&m)e:{for(v=g,b=m,x=0,y=v;y;y=$n(y))x++;for(y=0,_=b;_;_=$n(_))y++;for(;0<x-y;)v=$n(v),x--;for(;0<y-x;)b=$n(b),y--;for(;x--;){if(v===b||b!==null&&v===b.alternate)break e;v=$n(v),b=$n(b)}v=null}else v=null;g!==null&&sp(f,h,g,v,!1),m!==null&&S!==null&&sp(f,S,m,v,!0)}}t:{if(h=c?fs(c):window,g=h.nodeName&&h.nodeName.toLowerCase(),g==="select"||g==="input"&&h.type==="file")var D=xh;else if(bh(h))if(Mb)D=M1;else{D=T1;var A=z1}else g=h.nodeName,!g||g.toLowerCase()!=="input"||h.type!=="checkbox"&&h.type!=="radio"?c&&ld(c.elementType)&&(D=xh):D=A1;if(D&&(D=D(t,c))){Ab(f,D,i,d);break t}A&&A(t,h,c),t==="focusout"&&c&&h.type==="number"&&c.memoizedProps.value!=null&&eu(h,"number",h.value)}switch(A=c?fs(c):window,t){case"focusin":(bh(A)||A.contentEditable==="true")&&(oa=A,au=c,vs=null);break;case"focusout":vs=au=oa=null;break;case"mousedown":su=!0;break;case"contextmenu":case"mouseup":case"dragend":su=!1,_h(f,i,d);break;case"selectionchange":if(R1)break;case"keydown":case"keyup":_h(f,i,d)}var j;if(ud)t:{switch(t){case"compositionstart":var T="onCompositionStart";break t;case"compositionend":T="onCompositionEnd";break t;case"compositionupdate":T="onCompositionUpdate";break t}T=void 0}else la?zb(t,i)&&(T="onCompositionEnd"):t==="keydown"&&i.keyCode===229&&(T="onCompositionStart");T&&(jb&&i.locale!=="ko"&&(la||T!=="onCompositionStart"?T==="onCompositionEnd"&&la&&(j=wb()):(Gi=d,rd="value"in Gi?Gi.value:Gi.textContent,la=!0)),A=Wo(c,T),0<A.length&&(T=new fh(T,t,null,i,d),f.push({event:T,listeners:A}),j?T.data=j:(j=Tb(i),j!==null&&(T.data=j)))),(j=k1?C1(t,i):D1(t,i))&&(T=Wo(c,"onBeforeInput"),0<T.length&&(A=new fh("onBeforeInput","beforeinput",null,i,d),f.push({event:A,listeners:T}),A.data=j)),gv(f,t,c,i,d)}_m(f,e)})}function qs(t,e,i){return{instance:t,listener:e,currentTarget:i}}function Wo(t,e){for(var i=e+"Capture",n=[];t!==null;){var a=t,s=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||s===null||(a=Ns(t,i),a!=null&&n.unshift(qs(t,a,s)),a=Ns(t,e),a!=null&&n.push(qs(t,a,s))),t.tag===3)return n;t=t.return}return[]}function $n(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function sp(t,e,i,n,a){for(var s=e._reactName,l=[];i!==null&&i!==n;){var o=i,r=o.alternate,c=o.stateNode;if(o=o.tag,r!==null&&r===n)break;o!==5&&o!==26&&o!==27||c===null||(r=c,a?(c=Ns(i,s),c!=null&&l.unshift(qs(i,c,r))):a||(c=Ns(i,s),c!=null&&l.push(qs(i,c,r)))),i=i.return}l.length!==0&&t.push({event:e,listeners:l})}var yv=/\r\n?/g,vv=/\u0000|\uFFFD/g;function lp(t){return(typeof t=="string"?t:""+t).replace(yv,`
`).replace(vv,"")}function Cm(t,e){return e=lp(e),lp(t)===e}function wr(){}function gt(t,e,i,n,a,s){switch(i){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||ja(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&ja(t,""+n);break;case"className":El(t,"class",n);break;case"tabIndex":El(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":El(t,i,n);break;case"style":Cb(t,n,s);break;case"data":if(e!=="object"){El(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||i!=="href")){t.removeAttribute(i);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(i);break}n=lo(""+n),t.setAttribute(i,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(i,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof s=="function"&&(i==="formAction"?(e!=="input"&&gt(t,e,"name",a.name,a,null),gt(t,e,"formEncType",a.formEncType,a,null),gt(t,e,"formMethod",a.formMethod,a,null),gt(t,e,"formTarget",a.formTarget,a,null)):(gt(t,e,"encType",a.encType,a,null),gt(t,e,"method",a.method,a,null),gt(t,e,"target",a.target,a,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(i);break}n=lo(""+n),t.setAttribute(i,n);break;case"onClick":n!=null&&(t.onclick=wr);break;case"onScroll":n!=null&&J("scroll",t);break;case"onScrollEnd":n!=null&&J("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(O(61));if(i=n.__html,i!=null){if(a.children!=null)throw Error(O(60));t.innerHTML=i}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}i=lo(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",i);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(i,""+n):t.removeAttribute(i);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(i,""):t.removeAttribute(i);break;case"capture":case"download":n===!0?t.setAttribute(i,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(i,n):t.removeAttribute(i);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(i,n):t.removeAttribute(i);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(i):t.setAttribute(i,n);break;case"popover":J("beforetoggle",t),J("toggle",t),so(t,"popover",n);break;case"xlinkActuate":di(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":di(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":di(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":di(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":di(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":di(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":di(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":di(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":di(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":so(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<i.length)||i[0]!=="o"&&i[0]!=="O"||i[1]!=="n"&&i[1]!=="N")&&(i=$0.get(i)||i,so(t,i,n))}}function Au(t,e,i,n,a,s){switch(i){case"style":Cb(t,n,s);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(O(61));if(i=n.__html,i!=null){if(a.children!=null)throw Error(O(60));t.innerHTML=i}}break;case"children":typeof n=="string"?ja(t,n):(typeof n=="number"||typeof n=="bigint")&&ja(t,""+n);break;case"onScroll":n!=null&&J("scroll",t);break;case"onScrollEnd":n!=null&&J("scrollend",t);break;case"onClick":n!=null&&(t.onclick=wr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!mb.hasOwnProperty(i))t:{if(i[0]==="o"&&i[1]==="n"&&(a=i.endsWith("Capture"),e=i.slice(2,a?i.length-7:void 0),s=t[pe]||null,s=s!=null?s[i]:null,typeof s=="function"&&t.removeEventListener(e,s,a),typeof n=="function")){typeof s!="function"&&s!==null&&(i in t?t[i]=null:t.hasAttribute(i)&&t.removeAttribute(i)),t.addEventListener(e,n,a);break t}i in t?t[i]=n:n===!0?t.setAttribute(i,""):so(t,i,n)}}}function $t(t,e,i){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":J("error",t),J("load",t);var n=!1,a=!1,s;for(s in i)if(i.hasOwnProperty(s)){var l=i[s];if(l!=null)switch(s){case"src":n=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(O(137,e));default:gt(t,e,s,l,i,null)}}a&&gt(t,e,"srcSet",i.srcSet,i,null),n&&gt(t,e,"src",i.src,i,null);return;case"input":J("invalid",t);var o=s=l=a=null,r=null,c=null;for(n in i)if(i.hasOwnProperty(n)){var d=i[n];if(d!=null)switch(n){case"name":a=d;break;case"type":l=d;break;case"checked":r=d;break;case"defaultChecked":c=d;break;case"value":s=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(d!=null)throw Error(O(137,e));break;default:gt(t,e,n,d,i,null)}}Sb(t,s,o,r,c,l,a,!1),To(t);return;case"select":J("invalid",t),n=l=s=null;for(a in i)if(i.hasOwnProperty(a)&&(o=i[a],o!=null))switch(a){case"value":s=o;break;case"defaultValue":l=o;break;case"multiple":n=o;default:gt(t,e,a,o,i,null)}e=s,i=l,t.multiple=!!n,e!=null?ga(t,!!n,e,!1):i!=null&&ga(t,!!n,i,!0);return;case"textarea":J("invalid",t),s=a=n=null;for(l in i)if(i.hasOwnProperty(l)&&(o=i[l],o!=null))switch(l){case"value":n=o;break;case"defaultValue":a=o;break;case"children":s=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(O(91));break;default:gt(t,e,l,o,i,null)}kb(t,n,a,s),To(t);return;case"option":for(r in i)if(i.hasOwnProperty(r)&&(n=i[r],n!=null))switch(r){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:gt(t,e,r,n,i,null)}return;case"dialog":J("beforetoggle",t),J("toggle",t),J("cancel",t),J("close",t);break;case"iframe":case"object":J("load",t);break;case"video":case"audio":for(n=0;n<Gs.length;n++)J(Gs[n],t);break;case"image":J("error",t),J("load",t);break;case"details":J("toggle",t);break;case"embed":case"source":case"link":J("error",t),J("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in i)if(i.hasOwnProperty(c)&&(n=i[c],n!=null))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(O(137,e));default:gt(t,e,c,n,i,null)}return;default:if(ld(e)){for(d in i)i.hasOwnProperty(d)&&(n=i[d],n!==void 0&&Au(t,e,d,n,i,void 0));return}}for(o in i)i.hasOwnProperty(o)&&(n=i[o],n!=null&&gt(t,e,o,n,i,null))}function Sv(t,e,i,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,s=null,l=null,o=null,r=null,c=null,d=null;for(g in i){var f=i[g];if(i.hasOwnProperty(g)&&f!=null)switch(g){case"checked":break;case"value":break;case"defaultValue":r=f;default:n.hasOwnProperty(g)||gt(t,e,g,null,n,f)}}for(var h in n){var g=n[h];if(f=i[h],n.hasOwnProperty(h)&&(g!=null||f!=null))switch(h){case"type":s=g;break;case"name":a=g;break;case"checked":c=g;break;case"defaultChecked":d=g;break;case"value":l=g;break;case"defaultValue":o=g;break;case"children":case"dangerouslySetInnerHTML":if(g!=null)throw Error(O(137,e));break;default:g!==f&&gt(t,e,h,g,n,f)}}tu(t,l,o,r,c,d,s,a);return;case"select":g=l=o=h=null;for(s in i)if(r=i[s],i.hasOwnProperty(s)&&r!=null)switch(s){case"value":break;case"multiple":g=r;default:n.hasOwnProperty(s)||gt(t,e,s,null,n,r)}for(a in n)if(s=n[a],r=i[a],n.hasOwnProperty(a)&&(s!=null||r!=null))switch(a){case"value":h=s;break;case"defaultValue":o=s;break;case"multiple":l=s;default:s!==r&&gt(t,e,a,s,n,r)}e=o,i=l,n=g,h!=null?ga(t,!!i,h,!1):!!n!=!!i&&(e!=null?ga(t,!!i,e,!0):ga(t,!!i,i?[]:"",!1));return;case"textarea":g=h=null;for(o in i)if(a=i[o],i.hasOwnProperty(o)&&a!=null&&!n.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:gt(t,e,o,null,n,a)}for(l in n)if(a=n[l],s=i[l],n.hasOwnProperty(l)&&(a!=null||s!=null))switch(l){case"value":h=a;break;case"defaultValue":g=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(O(91));break;default:a!==s&&gt(t,e,l,a,n,s)}_b(t,h,g);return;case"option":for(var m in i)if(h=i[m],i.hasOwnProperty(m)&&h!=null&&!n.hasOwnProperty(m))switch(m){case"selected":t.selected=!1;break;default:gt(t,e,m,null,n,h)}for(r in n)if(h=n[r],g=i[r],n.hasOwnProperty(r)&&h!==g&&(h!=null||g!=null))switch(r){case"selected":t.selected=h&&typeof h!="function"&&typeof h!="symbol";break;default:gt(t,e,r,h,n,g)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in i)h=i[v],i.hasOwnProperty(v)&&h!=null&&!n.hasOwnProperty(v)&&gt(t,e,v,null,n,h);for(c in n)if(h=n[c],g=i[c],n.hasOwnProperty(c)&&h!==g&&(h!=null||g!=null))switch(c){case"children":case"dangerouslySetInnerHTML":if(h!=null)throw Error(O(137,e));break;default:gt(t,e,c,h,n,g)}return;default:if(ld(e)){for(var S in i)h=i[S],i.hasOwnProperty(S)&&h!==void 0&&!n.hasOwnProperty(S)&&Au(t,e,S,void 0,n,h);for(d in n)h=n[d],g=i[d],!n.hasOwnProperty(d)||h===g||h===void 0&&g===void 0||Au(t,e,d,h,n,g);return}}for(var b in i)h=i[b],i.hasOwnProperty(b)&&h!=null&&!n.hasOwnProperty(b)&&gt(t,e,b,null,n,h);for(f in n)h=n[f],g=i[f],!n.hasOwnProperty(f)||h===g||h==null&&g==null||gt(t,e,f,h,n,g)}var Mu=null,Ou=null;function Ko(t){return t.nodeType===9?t:t.ownerDocument}function op(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Dm(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Ru(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var zc=null;function _v(){var t=window.event;return t&&t.type==="popstate"?t===zc?!1:(zc=t,!0):(zc=null,!1)}var wm=typeof setTimeout=="function"?setTimeout:void 0,kv=typeof clearTimeout=="function"?clearTimeout:void 0,rp=typeof Promise=="function"?Promise:void 0,Cv=typeof queueMicrotask=="function"?queueMicrotask:typeof rp<"u"?function(t){return rp.resolve(null).then(t).catch(Dv)}:wm;function Dv(t){setTimeout(function(){throw t})}function dn(t){return t==="head"}function cp(t,e){var i=e,n=0,a=0;do{var s=i.nextSibling;if(t.removeChild(i),s&&s.nodeType===8)if(i=s.data,i==="/$"){if(0<n&&8>n){i=n;var l=t.ownerDocument;if(i&1&&Ms(l.documentElement),i&2&&Ms(l.body),i&4)for(i=l.head,Ms(i),l=i.firstChild;l;){var o=l.nextSibling,r=l.nodeName;l[ol]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&l.rel.toLowerCase()==="stylesheet"||i.removeChild(l),l=o}}if(a===0){t.removeChild(s),Ks(e);return}a--}else i==="$"||i==="$?"||i==="$!"?a++:n=i.charCodeAt(0)-48;else n=0;i=s}while(i);Ks(e)}function Eu(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var i=e;switch(e=e.nextSibling,i.nodeName){case"HTML":case"HEAD":case"BODY":Eu(i),sd(i);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(i.rel.toLowerCase()==="stylesheet")continue}t.removeChild(i)}}function wv(t,e,i,n){for(;t.nodeType===1;){var a=i;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[ol])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(s=t.getAttribute("rel"),s==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(s!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(s=t.getAttribute("src"),(s!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&s&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var s=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===s)return t}else return t;if(t=Ge(t.nextSibling),t===null)break}return null}function jv(t,e,i){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!i||(t=Ge(t.nextSibling),t===null))return null;return t}function Bu(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function zv(t,e){var i=t.ownerDocument;if(t.data!=="$?"||i.readyState==="complete")e();else{var n=function(){e(),i.removeEventListener("DOMContentLoaded",n)};i.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function Ge(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Lu=null;function up(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var i=t.data;if(i==="$"||i==="$!"||i==="$?"){if(e===0)return t;e--}else i==="/$"&&e++}t=t.previousSibling}return null}function jm(t,e,i){switch(e=Ko(i),t){case"html":if(t=e.documentElement,!t)throw Error(O(452));return t;case"head":if(t=e.head,!t)throw Error(O(453));return t;case"body":if(t=e.body,!t)throw Error(O(454));return t;default:throw Error(O(451))}}function Ms(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);sd(t)}var He=new Map,dp=new Set;function $o(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Ai=lt.d;lt.d={f:Tv,r:Av,D:Mv,C:Ov,L:Rv,m:Ev,X:Lv,S:Bv,M:Nv};function Tv(){var t=Ai.f(),e=kr();return t||e}function Av(t){var e=Fa(t);e!==null&&e.tag===5&&e.type==="form"?vx(e):Ai.r(t)}var qa=typeof document>"u"?null:document;function zm(t,e,i){var n=qa;if(n&&typeof e=="string"&&e){var a=Re(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof i=="string"&&(a+='[crossorigin="'+i+'"]'),dp.has(a)||(dp.add(a),t={rel:t,crossOrigin:i,href:e},n.querySelector(a)===null&&(e=n.createElement("link"),$t(e,"link",t),qt(e),n.head.appendChild(e)))}}function Mv(t){Ai.D(t),zm("dns-prefetch",t,null)}function Ov(t,e){Ai.C(t,e),zm("preconnect",t,e)}function Rv(t,e,i){Ai.L(t,e,i);var n=qa;if(n&&t&&e){var a='link[rel="preload"][as="'+Re(e)+'"]';e==="image"&&i&&i.imageSrcSet?(a+='[imagesrcset="'+Re(i.imageSrcSet)+'"]',typeof i.imageSizes=="string"&&(a+='[imagesizes="'+Re(i.imageSizes)+'"]')):a+='[href="'+Re(t)+'"]';var s=a;switch(e){case"style":s=Ba(t);break;case"script":s=Xa(t)}He.has(s)||(t=St({rel:"preload",href:e==="image"&&i&&i.imageSrcSet?void 0:t,as:e},i),He.set(s,t),n.querySelector(a)!==null||e==="style"&&n.querySelector(xl(s))||e==="script"&&n.querySelector(ml(s))||(e=n.createElement("link"),$t(e,"link",t),qt(e),n.head.appendChild(e)))}}function Ev(t,e){Ai.m(t,e);var i=qa;if(i&&t){var n=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+Re(n)+'"][href="'+Re(t)+'"]',s=a;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Xa(t)}if(!He.has(s)&&(t=St({rel:"modulepreload",href:t},e),He.set(s,t),i.querySelector(a)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(i.querySelector(ml(s)))return}n=i.createElement("link"),$t(n,"link",t),qt(n),i.head.appendChild(n)}}}function Bv(t,e,i){Ai.S(t,e,i);var n=qa;if(n&&t){var a=pa(n).hoistableStyles,s=Ba(t);e=e||"default";var l=a.get(s);if(!l){var o={loading:0,preload:null};if(l=n.querySelector(xl(s)))o.loading=5;else{t=St({rel:"stylesheet",href:t,"data-precedence":e},i),(i=He.get(s))&&Vd(t,i);var r=l=n.createElement("link");qt(r),$t(r,"link",t),r._p=new Promise(function(c,d){r.onload=c,r.onerror=d}),r.addEventListener("load",function(){o.loading|=1}),r.addEventListener("error",function(){o.loading|=2}),o.loading|=4,bo(l,e,n)}l={type:"stylesheet",instance:l,count:1,state:o},a.set(s,l)}}}function Lv(t,e){Ai.X(t,e);var i=qa;if(i&&t){var n=pa(i).hoistableScripts,a=Xa(t),s=n.get(a);s||(s=i.querySelector(ml(a)),s||(t=St({src:t,async:!0},e),(e=He.get(a))&&Gd(t,e),s=i.createElement("script"),qt(s),$t(s,"link",t),i.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},n.set(a,s))}}function Nv(t,e){Ai.M(t,e);var i=qa;if(i&&t){var n=pa(i).hoistableScripts,a=Xa(t),s=n.get(a);s||(s=i.querySelector(ml(a)),s||(t=St({src:t,async:!0,type:"module"},e),(e=He.get(a))&&Gd(t,e),s=i.createElement("script"),qt(s),$t(s,"link",t),i.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},n.set(a,s))}}function fp(t,e,i,n){var a=(a=$i.current)?$o(a):null;if(!a)throw Error(O(446));switch(t){case"meta":case"title":return null;case"style":return typeof i.precedence=="string"&&typeof i.href=="string"?(e=Ba(i.href),i=pa(a).hoistableStyles,n=i.get(e),n||(n={type:"style",instance:null,count:0,state:null},i.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(i.rel==="stylesheet"&&typeof i.href=="string"&&typeof i.precedence=="string"){t=Ba(i.href);var s=pa(a).hoistableStyles,l=s.get(t);if(l||(a=a.ownerDocument||a,l={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},s.set(t,l),(s=a.querySelector(xl(t)))&&!s._p&&(l.instance=s,l.state.loading=5),He.has(t)||(i={rel:"preload",as:"style",href:i.href,crossOrigin:i.crossOrigin,integrity:i.integrity,media:i.media,hrefLang:i.hrefLang,referrerPolicy:i.referrerPolicy},He.set(t,i),s||Hv(a,t,i,l.state))),e&&n===null)throw Error(O(528,""));return l}if(e&&n!==null)throw Error(O(529,""));return null;case"script":return e=i.async,i=i.src,typeof i=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Xa(i),i=pa(a).hoistableScripts,n=i.get(e),n||(n={type:"script",instance:null,count:0,state:null},i.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(O(444,t))}}function Ba(t){return'href="'+Re(t)+'"'}function xl(t){return'link[rel="stylesheet"]['+t+"]"}function Tm(t){return St({},t,{"data-precedence":t.precedence,precedence:null})}function Hv(t,e,i,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),$t(e,"link",i),qt(e),t.head.appendChild(e))}function Xa(t){return'[src="'+Re(t)+'"]'}function ml(t){return"script[async]"+t}function hp(t,e,i){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+Re(i.href)+'"]');if(n)return e.instance=n,qt(n),n;var a=St({},i,{"data-href":i.href,"data-precedence":i.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),qt(n),$t(n,"style",a),bo(n,i.precedence,t),e.instance=n;case"stylesheet":a=Ba(i.href);var s=t.querySelector(xl(a));if(s)return e.state.loading|=4,e.instance=s,qt(s),s;n=Tm(i),(a=He.get(a))&&Vd(n,a),s=(t.ownerDocument||t).createElement("link"),qt(s);var l=s;return l._p=new Promise(function(o,r){l.onload=o,l.onerror=r}),$t(s,"link",n),e.state.loading|=4,bo(s,i.precedence,t),e.instance=s;case"script":return s=Xa(i.src),(a=t.querySelector(ml(s)))?(e.instance=a,qt(a),a):(n=i,(a=He.get(s))&&(n=St({},i),Gd(n,a)),t=t.ownerDocument||t,a=t.createElement("script"),qt(a),$t(a,"link",n),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(O(443,e.type))}else e.type==="stylesheet"&&!(e.state.loading&4)&&(n=e.instance,e.state.loading|=4,bo(n,i.precedence,t));return e.instance}function bo(t,e,i){for(var n=i.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=n.length?n[n.length-1]:null,s=a,l=0;l<n.length;l++){var o=n[l];if(o.dataset.precedence===e)s=o;else if(s!==a)break}s?s.parentNode.insertBefore(t,s.nextSibling):(e=i.nodeType===9?i.head:i,e.insertBefore(t,e.firstChild))}function Vd(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Gd(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var xo=null;function pp(t,e,i){if(xo===null){var n=new Map,a=xo=new Map;a.set(i,n)}else a=xo,n=a.get(i),n||(n=new Map,a.set(i,n));if(n.has(t))return n;for(n.set(t,null),i=i.getElementsByTagName(t),a=0;a<i.length;a++){var s=i[a];if(!(s[ol]||s[Jt]||t==="link"&&s.getAttribute("rel")==="stylesheet")&&s.namespaceURI!=="http://www.w3.org/2000/svg"){var l=s.getAttribute(e)||"";l=t+l;var o=n.get(l);o?o.push(s):n.set(l,[s])}}return n}function gp(t,e,i){t=t.ownerDocument||t,t.head.insertBefore(i,e==="title"?t.querySelector("head > title"):null)}function Uv(t,e,i){if(i===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function Am(t){return!(t.type==="stylesheet"&&!(t.state.loading&3))}var Xs=null;function Yv(){}function Fv(t,e,i){if(Xs===null)throw Error(O(475));var n=Xs;if(e.type==="stylesheet"&&(typeof i.media!="string"||matchMedia(i.media).matches!==!1)&&!(e.state.loading&4)){if(e.instance===null){var a=Ba(i.href),s=t.querySelector(xl(a));if(s){t=s._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(n.count++,n=Po.bind(n),t.then(n,n)),e.state.loading|=4,e.instance=s,qt(s);return}s=t.ownerDocument||t,i=Tm(i),(a=He.get(a))&&Vd(i,a),s=s.createElement("link"),qt(s);var l=s;l._p=new Promise(function(o,r){l.onload=o,l.onerror=r}),$t(s,"link",i),e.instance=s}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(e,t),(t=e.state.preload)&&!(e.state.loading&3)&&(n.count++,e=Po.bind(n),t.addEventListener("load",e),t.addEventListener("error",e))}}function Vv(){if(Xs===null)throw Error(O(475));var t=Xs;return t.stylesheets&&t.count===0&&Nu(t,t.stylesheets),0<t.count?function(e){var i=setTimeout(function(){if(t.stylesheets&&Nu(t,t.stylesheets),t.unsuspend){var n=t.unsuspend;t.unsuspend=null,n()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(i)}}:null}function Po(){if(this.count--,this.count===0){if(this.stylesheets)Nu(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Jo=null;function Nu(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Jo=new Map,e.forEach(Gv,t),Jo=null,Po.call(t))}function Gv(t,e){if(!(e.state.loading&4)){var i=Jo.get(t);if(i)var n=i.get(null);else{i=new Map,Jo.set(t,i);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<a.length;s++){var l=a[s];(l.nodeName==="LINK"||l.getAttribute("media")!=="not all")&&(i.set(l.dataset.precedence,l),n=l)}n&&i.set(null,n)}a=e.instance,l=a.getAttribute("data-precedence"),s=i.get(l)||n,s===n&&i.set(null,a),i.set(l,a),this.count++,n=Po.bind(this),a.addEventListener("load",n),a.addEventListener("error",n),s?s.parentNode.insertBefore(a,s.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var Qs={$$typeof:yi,Provider:null,Consumer:null,_currentValue:wn,_currentValue2:wn,_threadCount:0};function qv(t,e,i,n,a,s,l,o){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=$r(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=$r(0),this.hiddenUpdates=$r(null),this.identifierPrefix=n,this.onUncaughtError=a,this.onCaughtError=s,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Mm(t,e,i,n,a,s,l,o,r,c,d,f){return t=new qv(t,e,i,l,o,r,c,f),e=1,s===!0&&(e|=24),s=Se(3,null,null,e),t.current=s,s.stateNode=t,e=xd(),e.refCount++,t.pooledCache=e,e.refCount++,s.memoizedState={element:n,isDehydrated:i,cache:e},yd(s),t}function Om(t){return t?(t=ua,t):ua}function Rm(t,e,i,n,a,s){a=Om(a),n.context===null?n.context=a:n.pendingContext=a,n=Pi(e),n.payload={element:i},s=s===void 0?null:s,s!==null&&(n.callback=s),i=Ji(t,n,e),i!==null&&(we(i,t,e),ks(i,t,e))}function bp(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var i=t.retryLane;t.retryLane=i!==0&&i<e?i:e}}function qd(t,e){bp(t,e),(t=t.alternate)&&bp(t,e)}function Em(t){if(t.tag===13){var e=Va(t,67108864);e!==null&&we(e,t,67108864),qd(t,67108864)}}var Io=!0;function Xv(t,e,i,n){var a=X.T;X.T=null;var s=lt.p;try{lt.p=2,Xd(t,e,i,n)}finally{lt.p=s,X.T=a}}function Qv(t,e,i,n){var a=X.T;X.T=null;var s=lt.p;try{lt.p=8,Xd(t,e,i,n)}finally{lt.p=s,X.T=a}}function Xd(t,e,i,n){if(Io){var a=Hu(n);if(a===null)jc(t,e,n,tr,i),xp(t,n);else if(Wv(a,t,e,i,n))n.stopPropagation();else if(xp(t,n),e&4&&-1<Zv.indexOf(t)){for(;a!==null;){var s=Fa(a);if(s!==null)switch(s.tag){case 3:if(s=s.stateNode,s.current.memoizedState.isDehydrated){var l=mn(s.pendingLanes);if(l!==0){var o=s;for(o.pendingLanes|=2,o.entangledLanes|=2;l;){var r=1<<31-Ce(l);o.entanglements[1]|=r,l&=~r}li(s),!(ft&6)&&(qo=ei()+500,bl(0))}}break;case 13:o=Va(s,2),o!==null&&we(o,s,2),kr(),qd(s,2)}if(s=Hu(n),s===null&&jc(t,e,n,tr,i),s===a)break;a=s}a!==null&&n.stopPropagation()}else jc(t,e,n,null,i)}}function Hu(t){return t=od(t),Qd(t)}var tr=null;function Qd(t){if(tr=null,t=aa(t),t!==null){var e=nl(t);if(e===null)t=null;else{var i=e.tag;if(i===13){if(t=sb(e),t!==null)return t;t=null}else if(i===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return tr=t,null}function Bm(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(O0()){case cb:return 2;case ub:return 8;case zo:case R0:return 32;case db:return 268435456;default:return 32}default:return 32}}var Uu=!1,en=null,nn=null,an=null,Zs=new Map,Ws=new Map,Fi=[],Zv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function xp(t,e){switch(t){case"focusin":case"focusout":en=null;break;case"dragenter":case"dragleave":nn=null;break;case"mouseover":case"mouseout":an=null;break;case"pointerover":case"pointerout":Zs.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ws.delete(e.pointerId)}}function as(t,e,i,n,a,s){return t===null||t.nativeEvent!==s?(t={blockedOn:e,domEventName:i,eventSystemFlags:n,nativeEvent:s,targetContainers:[a]},e!==null&&(e=Fa(e),e!==null&&Em(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function Wv(t,e,i,n,a){switch(e){case"focusin":return en=as(en,t,e,i,n,a),!0;case"dragenter":return nn=as(nn,t,e,i,n,a),!0;case"mouseover":return an=as(an,t,e,i,n,a),!0;case"pointerover":var s=a.pointerId;return Zs.set(s,as(Zs.get(s)||null,t,e,i,n,a)),!0;case"gotpointercapture":return s=a.pointerId,Ws.set(s,as(Ws.get(s)||null,t,e,i,n,a)),!0}return!1}function Lm(t){var e=aa(t.target);if(e!==null){var i=nl(e);if(i!==null){if(e=i.tag,e===13){if(e=sb(i),e!==null){t.blockedOn=e,F0(t.priority,function(){if(i.tag===13){var n=De();n=nd(n);var a=Va(i,n);a!==null&&we(a,i,n),qd(i,n)}});return}}else if(e===3&&i.stateNode.current.memoizedState.isDehydrated){t.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}t.blockedOn=null}function mo(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var i=Hu(t.nativeEvent);if(i===null){i=t.nativeEvent;var n=new i.constructor(i.type,i);iu=n,i.target.dispatchEvent(n),iu=null}else return e=Fa(i),e!==null&&Em(e),t.blockedOn=i,!1;e.shift()}return!0}function mp(t,e,i){mo(t)&&i.delete(e)}function Kv(){Uu=!1,en!==null&&mo(en)&&(en=null),nn!==null&&mo(nn)&&(nn=null),an!==null&&mo(an)&&(an=null),Zs.forEach(mp),Ws.forEach(mp)}function Vl(t,e){t.blockedOn===e&&(t.blockedOn=null,Uu||(Uu=!0,Ft.unstable_scheduleCallback(Ft.unstable_NormalPriority,Kv)))}var Gl=null;function yp(t){Gl!==t&&(Gl=t,Ft.unstable_scheduleCallback(Ft.unstable_NormalPriority,function(){Gl===t&&(Gl=null);for(var e=0;e<t.length;e+=3){var i=t[e],n=t[e+1],a=t[e+2];if(typeof n!="function"){if(Qd(n||i)===null)continue;break}var s=Fa(i);s!==null&&(t.splice(e,3),e-=3,mu(s,{pending:!0,data:a,method:i.method,action:n},n,a))}}))}function Ks(t){function e(r){return Vl(r,t)}en!==null&&Vl(en,t),nn!==null&&Vl(nn,t),an!==null&&Vl(an,t),Zs.forEach(e),Ws.forEach(e);for(var i=0;i<Fi.length;i++){var n=Fi[i];n.blockedOn===t&&(n.blockedOn=null)}for(;0<Fi.length&&(i=Fi[0],i.blockedOn===null);)Lm(i),i.blockedOn===null&&Fi.shift();if(i=(t.ownerDocument||t).$$reactFormReplay,i!=null)for(n=0;n<i.length;n+=3){var a=i[n],s=i[n+1],l=a[pe]||null;if(typeof s=="function")l||yp(i);else if(l){var o=null;if(s&&s.hasAttribute("formAction")){if(a=s,l=s[pe]||null)o=l.formAction;else if(Qd(a)!==null)continue}else o=l.action;typeof o=="function"?i[n+1]=o:(i.splice(n,3),n-=3),yp(i)}}}function Zd(t){this._internalRoot=t}jr.prototype.render=Zd.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(O(409));var i=e.current,n=De();Rm(i,n,t,e,null,null)};jr.prototype.unmount=Zd.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Rm(t.current,2,null,t,null,null),kr(),e[Ya]=null}};function jr(t){this._internalRoot=t}jr.prototype.unstable_scheduleHydration=function(t){if(t){var e=bb();t={blockedOn:null,target:t,priority:e};for(var i=0;i<Fi.length&&e!==0&&e<Fi[i].priority;i++);Fi.splice(i,0,t),i===0&&Lm(t)}};var vp=nb.version;if(vp!=="19.1.1")throw Error(O(527,vp,"19.1.1"));lt.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(O(188)):(t=Object.keys(t).join(","),Error(O(268,t)));return t=D0(e),t=t!==null?lb(t):null,t=t===null?null:t.stateNode,t};var $v={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:X,reconcilerVersion:"19.1.1"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ql=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ql.isDisabled&&ql.supportsFiber)try{al=ql.inject($v),ke=ql}catch{}}dr.createRoot=function(t,e){if(!ab(t))throw Error(O(299));var i=!1,n="",a=Mx,s=Ox,l=Rx,o=null;return e!=null&&(e.unstable_strictMode===!0&&(i=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(s=e.onCaughtError),e.onRecoverableError!==void 0&&(l=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(o=e.unstable_transitionCallbacks)),e=Mm(t,1,!1,null,null,i,n,a,s,l,o,null),t[Ya]=e.current,Fd(t),new Zd(e)};dr.hydrateRoot=function(t,e,i){if(!ab(t))throw Error(O(299));var n=!1,a="",s=Mx,l=Ox,o=Rx,r=null,c=null;return i!=null&&(i.unstable_strictMode===!0&&(n=!0),i.identifierPrefix!==void 0&&(a=i.identifierPrefix),i.onUncaughtError!==void 0&&(s=i.onUncaughtError),i.onCaughtError!==void 0&&(l=i.onCaughtError),i.onRecoverableError!==void 0&&(o=i.onRecoverableError),i.unstable_transitionCallbacks!==void 0&&(r=i.unstable_transitionCallbacks),i.formState!==void 0&&(c=i.formState)),e=Mm(t,1,!0,e,i??null,n,a,s,l,o,r,c),e.context=Om(null),i=e.current,n=De(),n=nd(n),a=Pi(n),a.callback=null,Ji(i,a,n),i=n,e.current.lanes=i,ll(e,i),li(e),t[Ya]=e.current,Fd(t),new jr(e)};dr.version="19.1.1";function Nm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Nm)}catch(t){console.error(t)}}Nm(),Pg.exports=dr;var Pv=Pg.exports;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function yl(t){return t+.5|0}const Qi=(t,e,i)=>Math.max(Math.min(t,i),e);function ps(t){return Qi(yl(t*2.55),0,255)}function sn(t){return Qi(yl(t*255),0,255)}function xi(t){return Qi(yl(t/2.55)/100,0,1)}function Sp(t){return Qi(yl(t*100),0,100)}const ze={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Yu=[..."0123456789ABCDEF"],Jv=t=>Yu[t&15],Iv=t=>Yu[(t&240)>>4]+Yu[t&15],Xl=t=>(t&240)>>4===(t&15),tS=t=>Xl(t.r)&&Xl(t.g)&&Xl(t.b)&&Xl(t.a);function eS(t){var e=t.length,i;return t[0]==="#"&&(e===4||e===5?i={r:255&ze[t[1]]*17,g:255&ze[t[2]]*17,b:255&ze[t[3]]*17,a:e===5?ze[t[4]]*17:255}:(e===7||e===9)&&(i={r:ze[t[1]]<<4|ze[t[2]],g:ze[t[3]]<<4|ze[t[4]],b:ze[t[5]]<<4|ze[t[6]],a:e===9?ze[t[7]]<<4|ze[t[8]]:255})),i}const iS=(t,e)=>t<255?e(t):"";function nS(t){var e=tS(t)?Jv:Iv;return t?"#"+e(t.r)+e(t.g)+e(t.b)+iS(t.a,e):void 0}const aS=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Hm(t,e,i){const n=e*Math.min(i,1-i),a=(s,l=(s+t/30)%12)=>i-n*Math.max(Math.min(l-3,9-l,1),-1);return[a(0),a(8),a(4)]}function sS(t,e,i){const n=(a,s=(a+t/60)%6)=>i-i*e*Math.max(Math.min(s,4-s,1),0);return[n(5),n(3),n(1)]}function lS(t,e,i){const n=Hm(t,1,.5);let a;for(e+i>1&&(a=1/(e+i),e*=a,i*=a),a=0;a<3;a++)n[a]*=1-e-i,n[a]+=e;return n}function oS(t,e,i,n,a){return t===a?(e-i)/n+(e<i?6:0):e===a?(i-t)/n+2:(t-e)/n+4}function Wd(t){const i=t.r/255,n=t.g/255,a=t.b/255,s=Math.max(i,n,a),l=Math.min(i,n,a),o=(s+l)/2;let r,c,d;return s!==l&&(d=s-l,c=o>.5?d/(2-s-l):d/(s+l),r=oS(i,n,a,d,s),r=r*60+.5),[r|0,c||0,o]}function Kd(t,e,i,n){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,i,n)).map(sn)}function $d(t,e,i){return Kd(Hm,t,e,i)}function rS(t,e,i){return Kd(lS,t,e,i)}function cS(t,e,i){return Kd(sS,t,e,i)}function Um(t){return(t%360+360)%360}function uS(t){const e=aS.exec(t);let i=255,n;if(!e)return;e[5]!==n&&(i=e[6]?ps(+e[5]):sn(+e[5]));const a=Um(+e[2]),s=+e[3]/100,l=+e[4]/100;return e[1]==="hwb"?n=rS(a,s,l):e[1]==="hsv"?n=cS(a,s,l):n=$d(a,s,l),{r:n[0],g:n[1],b:n[2],a:i}}function dS(t,e){var i=Wd(t);i[0]=Um(i[0]+e),i=$d(i),t.r=i[0],t.g=i[1],t.b=i[2]}function fS(t){if(!t)return;const e=Wd(t),i=e[0],n=Sp(e[1]),a=Sp(e[2]);return t.a<255?`hsla(${i}, ${n}%, ${a}%, ${xi(t.a)})`:`hsl(${i}, ${n}%, ${a}%)`}const _p={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},kp={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function hS(){const t={},e=Object.keys(kp),i=Object.keys(_p);let n,a,s,l,o;for(n=0;n<e.length;n++){for(l=o=e[n],a=0;a<i.length;a++)s=i[a],o=o.replace(s,_p[s]);s=parseInt(kp[l],16),t[o]=[s>>16&255,s>>8&255,s&255]}return t}let Ql;function pS(t){Ql||(Ql=hS(),Ql.transparent=[0,0,0,0]);const e=Ql[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:e.length===4?e[3]:255}}const gS=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function bS(t){const e=gS.exec(t);let i=255,n,a,s;if(e){if(e[7]!==n){const l=+e[7];i=e[8]?ps(l):Qi(l*255,0,255)}return n=+e[1],a=+e[3],s=+e[5],n=255&(e[2]?ps(n):Qi(n,0,255)),a=255&(e[4]?ps(a):Qi(a,0,255)),s=255&(e[6]?ps(s):Qi(s,0,255)),{r:n,g:a,b:s,a:i}}}function xS(t){return t&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${xi(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`)}const Tc=t=>t<=.0031308?t*12.92:Math.pow(t,1/2.4)*1.055-.055,Pn=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function mS(t,e,i){const n=Pn(xi(t.r)),a=Pn(xi(t.g)),s=Pn(xi(t.b));return{r:sn(Tc(n+i*(Pn(xi(e.r))-n))),g:sn(Tc(a+i*(Pn(xi(e.g))-a))),b:sn(Tc(s+i*(Pn(xi(e.b))-s))),a:t.a+i*(e.a-t.a)}}function Zl(t,e,i){if(t){let n=Wd(t);n[e]=Math.max(0,Math.min(n[e]+n[e]*i,e===0?360:1)),n=$d(n),t.r=n[0],t.g=n[1],t.b=n[2]}}function Ym(t,e){return t&&Object.assign(e||{},t)}function Cp(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=sn(t[3]))):(e=Ym(t,{r:0,g:0,b:0,a:1}),e.a=sn(e.a)),e}function yS(t){return t.charAt(0)==="r"?bS(t):uS(t)}class $s{constructor(e){if(e instanceof $s)return e;const i=typeof e;let n;i==="object"?n=Cp(e):i==="string"&&(n=eS(e)||pS(e)||yS(e)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var e=Ym(this._rgb);return e&&(e.a=xi(e.a)),e}set rgb(e){this._rgb=Cp(e)}rgbString(){return this._valid?xS(this._rgb):void 0}hexString(){return this._valid?nS(this._rgb):void 0}hslString(){return this._valid?fS(this._rgb):void 0}mix(e,i){if(e){const n=this.rgb,a=e.rgb;let s;const l=i===s?.5:i,o=2*l-1,r=n.a-a.a,c=((o*r===-1?o:(o+r)/(1+o*r))+1)/2;s=1-c,n.r=255&c*n.r+s*a.r+.5,n.g=255&c*n.g+s*a.g+.5,n.b=255&c*n.b+s*a.b+.5,n.a=l*n.a+(1-l)*a.a,this.rgb=n}return this}interpolate(e,i){return e&&(this._rgb=mS(this._rgb,e._rgb,i)),this}clone(){return new $s(this.rgb)}alpha(e){return this._rgb.a=sn(e),this}clearer(e){const i=this._rgb;return i.a*=1-e,this}greyscale(){const e=this._rgb,i=yl(e.r*.3+e.g*.59+e.b*.11);return e.r=e.g=e.b=i,this}opaquer(e){const i=this._rgb;return i.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Zl(this._rgb,2,e),this}darken(e){return Zl(this._rgb,2,-e),this}saturate(e){return Zl(this._rgb,1,e),this}desaturate(e){return Zl(this._rgb,1,-e),this}rotate(e){return dS(this._rgb,e),this}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function pi(){}const vS=(()=>{let t=0;return()=>t++})();function ct(t){return t==null}function Lt(t){if(Array.isArray&&Array.isArray(t))return!0;const e=Object.prototype.toString.call(t);return e.slice(0,7)==="[object"&&e.slice(-6)==="Array]"}function et(t){return t!==null&&Object.prototype.toString.call(t)==="[object Object]"}function ee(t){return(typeof t=="number"||t instanceof Number)&&isFinite(+t)}function We(t,e){return ee(t)?t:e}function P(t,e){return typeof t>"u"?e:t}const SS=(t,e)=>typeof t=="string"&&t.endsWith("%")?parseFloat(t)/100*e:+t;function vt(t,e,i){if(t&&typeof t.call=="function")return t.apply(i,e)}function dt(t,e,i,n){let a,s,l;if(Lt(t))for(s=t.length,a=0;a<s;a++)e.call(i,t[a],a);else if(et(t))for(l=Object.keys(t),s=l.length,a=0;a<s;a++)e.call(i,t[l[a]],l[a])}function er(t,e){let i,n,a,s;if(!t||!e||t.length!==e.length)return!1;for(i=0,n=t.length;i<n;++i)if(a=t[i],s=e[i],a.datasetIndex!==s.datasetIndex||a.index!==s.index)return!1;return!0}function ir(t){if(Lt(t))return t.map(ir);if(et(t)){const e=Object.create(null),i=Object.keys(t),n=i.length;let a=0;for(;a<n;++a)e[i[a]]=ir(t[i[a]]);return e}return t}function Fm(t){return["__proto__","prototype","constructor"].indexOf(t)===-1}function _S(t,e,i,n){if(!Fm(t))return;const a=e[t],s=i[t];et(a)&&et(s)?Ps(a,s,n):e[t]=ir(s)}function Ps(t,e,i){const n=Lt(e)?e:[e],a=n.length;if(!et(t))return t;i=i||{};const s=i.merger||_S;let l;for(let o=0;o<a;++o){if(l=n[o],!et(l))continue;const r=Object.keys(l);for(let c=0,d=r.length;c<d;++c)s(r[c],t,l,i)}return t}function Os(t,e){return Ps(t,e,{merger:kS})}function kS(t,e,i){if(!Fm(t))return;const n=e[t],a=i[t];et(n)&&et(a)?Os(n,a):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=ir(a))}const Dp={"":t=>t,x:t=>t.x,y:t=>t.y};function CS(t){const e=t.split("."),i=[];let n="";for(const a of e)n+=a,n.endsWith("\\")?n=n.slice(0,-1)+".":(i.push(n),n="");return i}function DS(t){const e=CS(t);return i=>{for(const n of e){if(n==="")break;i=i&&i[n]}return i}}function La(t,e){return(Dp[e]||(Dp[e]=DS(e)))(t)}function Pd(t){return t.charAt(0).toUpperCase()+t.slice(1)}const Js=t=>typeof t<"u",cn=t=>typeof t=="function",wp=(t,e)=>{if(t.size!==e.size)return!1;for(const i of t)if(!e.has(i))return!1;return!0};function wS(t){return t.type==="mouseup"||t.type==="click"||t.type==="contextmenu"}const Zt=Math.PI,qe=2*Zt,jS=qe+Zt,nr=Number.POSITIVE_INFINITY,zS=Zt/180,Ve=Zt/2,gn=Zt/4,jp=Zt*2/3,Vm=Math.log10,ni=Math.sign;function Rs(t,e,i){return Math.abs(t-e)<i}function zp(t){const e=Math.round(t);t=Rs(t,e,t/1e3)?e:t;const i=Math.pow(10,Math.floor(Vm(t))),n=t/i;return(n<=1?1:n<=2?2:n<=5?5:10)*i}function TS(t){const e=[],i=Math.sqrt(t);let n;for(n=1;n<i;n++)t%n===0&&(e.push(n),e.push(t/n));return i===(i|0)&&e.push(i),e.sort((a,s)=>a-s).pop(),e}function AS(t){return typeof t=="symbol"||typeof t=="object"&&t!==null&&!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t)}function Is(t){return!AS(t)&&!isNaN(parseFloat(t))&&isFinite(t)}function MS(t,e){const i=Math.round(t);return i-e<=t&&i+e>=t}function OS(t,e,i){let n,a,s;for(n=0,a=t.length;n<a;n++)s=t[n][i],isNaN(s)||(e.min=Math.min(e.min,s),e.max=Math.max(e.max,s))}function kn(t){return t*(Zt/180)}function RS(t){return t*(180/Zt)}function Tp(t){if(!ee(t))return;let e=1,i=0;for(;Math.round(t*e)/e!==t;)e*=10,i++;return i}function ES(t,e){const i=e.x-t.x,n=e.y-t.y,a=Math.sqrt(i*i+n*n);let s=Math.atan2(n,i);return s<-.5*Zt&&(s+=qe),{angle:s,distance:a}}function Fu(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function BS(t,e){return(t-e+jS)%qe-Zt}function Pe(t){return(t%qe+qe)%qe}function Gm(t,e,i,n){const a=Pe(t),s=Pe(e),l=Pe(i),o=Pe(s-a),r=Pe(l-a),c=Pe(a-s),d=Pe(a-l);return a===s||a===l||n&&s===l||o>r&&c<d}function _e(t,e,i){return Math.max(e,Math.min(i,t))}function LS(t){return _e(t,-32768,32767)}function Zi(t,e,i,n=1e-6){return t>=Math.min(e,i)-n&&t<=Math.max(e,i)+n}function Jd(t,e,i){i=i||(l=>t[l]<e);let n=t.length-1,a=0,s;for(;n-a>1;)s=a+n>>1,i(s)?a=s:n=s;return{lo:a,hi:n}}const Cn=(t,e,i,n)=>Jd(t,i,n?a=>{const s=t[a][e];return s<i||s===i&&t[a+1][e]===i}:a=>t[a][e]<i),NS=(t,e,i)=>Jd(t,i,n=>t[n][e]>=i);function HS(t,e,i){let n=0,a=t.length;for(;n<a&&t[n]<e;)n++;for(;a>n&&t[a-1]>i;)a--;return n>0||a<t.length?t.slice(n,a):t}const qm=["push","pop","shift","splice","unshift"];function US(t,e){if(t._chartjs){t._chartjs.listeners.push(e);return}Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),qm.forEach(i=>{const n="_onData"+Pd(i),a=t[i];Object.defineProperty(t,i,{configurable:!0,enumerable:!1,value(...s){const l=a.apply(this,s);return t._chartjs.listeners.forEach(o=>{typeof o[n]=="function"&&o[n](...s)}),l}})})}function Ap(t,e){const i=t._chartjs;if(!i)return;const n=i.listeners,a=n.indexOf(e);a!==-1&&n.splice(a,1),!(n.length>0)&&(qm.forEach(s=>{delete t[s]}),delete t._chartjs)}function Xm(t){const e=new Set(t);return e.size===t.length?t:Array.from(e)}const Qm=function(){return typeof window>"u"?function(t){return t()}:window.requestAnimationFrame}();function Zm(t,e){let i=[],n=!1;return function(...a){i=a,n||(n=!0,Qm.call(window,()=>{n=!1,t.apply(e,i)}))}}function YS(t,e){let i;return function(...n){return e?(clearTimeout(i),i=setTimeout(t,e,n)):t.apply(this,n),e}}const Id=t=>t==="start"?"left":t==="end"?"right":"center",Pt=(t,e,i)=>t==="start"?e:t==="end"?i:(e+i)/2,FS=(t,e,i,n)=>t===(n?"left":"right")?i:t==="center"?(e+i)/2:e;function VS(t,e,i){const n=e.length;let a=0,s=n;if(t._sorted){const{iScale:l,vScale:o,_parsed:r}=t,c=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,d=l.axis,{min:f,max:h,minDefined:g,maxDefined:m}=l.getUserBounds();if(g){if(a=Math.min(Cn(r,d,f).lo,i?n:Cn(e,d,l.getPixelForValue(f)).lo),c){const v=r.slice(0,a+1).reverse().findIndex(S=>!ct(S[o.axis]));a-=Math.max(0,v)}a=_e(a,0,n-1)}if(m){let v=Math.max(Cn(r,l.axis,h,!0).hi+1,i?0:Cn(e,d,l.getPixelForValue(h),!0).hi+1);if(c){const S=r.slice(v-1).findIndex(b=>!ct(b[o.axis]));v+=Math.max(0,S)}s=_e(v,a,n)-a}else s=n-a}return{start:a,count:s}}function GS(t){const{xScale:e,yScale:i,_scaleRanges:n}=t,a={xmin:e.min,xmax:e.max,ymin:i.min,ymax:i.max};if(!n)return t._scaleRanges=a,!0;const s=n.xmin!==e.min||n.xmax!==e.max||n.ymin!==i.min||n.ymax!==i.max;return Object.assign(n,a),s}const Wl=t=>t===0||t===1,Mp=(t,e,i)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*qe/i)),Op=(t,e,i)=>Math.pow(2,-10*t)*Math.sin((t-e)*qe/i)+1,Es={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*Ve)+1,easeOutSine:t=>Math.sin(t*Ve),easeInOutSine:t=>-.5*(Math.cos(Zt*t)-1),easeInExpo:t=>t===0?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>t===1?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>Wl(t)?t:t<.5?.5*Math.pow(2,10*(t*2-1)):.5*(-Math.pow(2,-10*(t*2-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>Wl(t)?t:Mp(t,.075,.3),easeOutElastic:t=>Wl(t)?t:Op(t,.075,.3),easeInOutElastic(t){return Wl(t)?t:t<.5?.5*Mp(t*2,.1125,.45):.5+.5*Op(t*2-1,.1125,.45)},easeInBack(t){return t*t*((1.70158+1)*t-1.70158)},easeOutBack(t){return(t-=1)*t*((1.70158+1)*t********)+1},easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-Es.easeOutBounce(1-t),easeOutBounce(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},easeInOutBounce:t=>t<.5?Es.easeInBounce(t*2)*.5:Es.easeOutBounce(t*2-1)*.5+.5};function tf(t){if(t&&typeof t=="object"){const e=t.toString();return e==="[object CanvasPattern]"||e==="[object CanvasGradient]"}return!1}function Rp(t){return tf(t)?t:new $s(t)}function Ac(t){return tf(t)?t:new $s(t).saturate(.5).darken(.1).hexString()}const qS=["x","y","borderWidth","radius","tension"],XS=["color","borderColor","backgroundColor"];function QS(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>e!=="onProgress"&&e!=="onComplete"&&e!=="fn"}),t.set("animations",{colors:{type:"color",properties:XS},numbers:{type:"number",properties:qS}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>e|0}}}})}function ZS(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const Ep=new Map;function WS(t,e){e=e||{};const i=t+JSON.stringify(e);let n=Ep.get(i);return n||(n=new Intl.NumberFormat(t,e),Ep.set(i,n)),n}function Wm(t,e,i){return WS(e,i).format(t)}const KS={values(t){return Lt(t)?t:""+t},numeric(t,e,i){if(t===0)return"0";const n=this.chart.options.locale;let a,s=t;if(i.length>1){const c=Math.max(Math.abs(i[0].value),Math.abs(i[i.length-1].value));(c<1e-4||c>1e15)&&(a="scientific"),s=$S(t,i)}const l=Vm(Math.abs(s)),o=isNaN(l)?1:Math.max(Math.min(-1*Math.floor(l),20),0),r={notation:a,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(r,this.options.ticks.format),Wm(t,n,r)}};function $S(t,e){let i=e.length>3?e[2].value-e[1].value:e[1].value-e[0].value;return Math.abs(i)>=1&&t!==Math.floor(t)&&(i=t-Math.floor(t)),i}var Km={formatters:KS};function PS(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,i)=>i.lineWidth,tickColor:(e,i)=>i.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Km.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&e!=="callback"&&e!=="parser",_indexable:e=>e!=="borderDash"&&e!=="tickBorderDash"&&e!=="dash"}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:e=>e!=="backdropPadding"&&e!=="callback",_indexable:e=>e!=="backdropPadding"})}const Hn=Object.create(null),Vu=Object.create(null);function Bs(t,e){if(!e)return t;const i=e.split(".");for(let n=0,a=i.length;n<a;++n){const s=i[n];t=t[s]||(t[s]=Object.create(null))}return t}function Mc(t,e,i){return typeof e=="string"?Ps(Bs(t,e),i):Ps(Bs(t,""),e)}class JS{constructor(e,i){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=n=>n.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(n,a)=>Ac(a.backgroundColor),this.hoverBorderColor=(n,a)=>Ac(a.borderColor),this.hoverColor=(n,a)=>Ac(a.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(i)}set(e,i){return Mc(this,e,i)}get(e){return Bs(this,e)}describe(e,i){return Mc(Vu,e,i)}override(e,i){return Mc(Hn,e,i)}route(e,i,n,a){const s=Bs(this,e),l=Bs(this,n),o="_"+i;Object.defineProperties(s,{[o]:{value:s[i],writable:!0},[i]:{enumerable:!0,get(){const r=this[o],c=l[a];return et(r)?Object.assign({},c,r):P(r,c)},set(r){this[o]=r}}})}apply(e){e.forEach(i=>i(this))}}var Et=new JS({_scriptable:t=>!t.startsWith("on"),_indexable:t=>t!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[QS,ZS,PS]);function IS(t){return!t||ct(t.size)||ct(t.family)?null:(t.style?t.style+" ":"")+(t.weight?t.weight+" ":"")+t.size+"px "+t.family}function Bp(t,e,i,n,a){let s=e[a];return s||(s=e[a]=t.measureText(a).width,i.push(a)),s>n&&(n=s),n}function bn(t,e,i){const n=t.currentDevicePixelRatio,a=i!==0?Math.max(i/2,.5):0;return Math.round((e-a)*n)/n+a}function Lp(t,e){!e&&!t||(e=e||t.getContext("2d"),e.save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function Gu(t,e,i,n){$m(t,e,i,n,null)}function $m(t,e,i,n,a){let s,l,o,r,c,d,f,h;const g=e.pointStyle,m=e.rotation,v=e.radius;let S=(m||0)*zS;if(g&&typeof g=="object"&&(s=g.toString(),s==="[object HTMLImageElement]"||s==="[object HTMLCanvasElement]")){t.save(),t.translate(i,n),t.rotate(S),t.drawImage(g,-g.width/2,-g.height/2,g.width,g.height),t.restore();return}if(!(isNaN(v)||v<=0)){switch(t.beginPath(),g){default:a?t.ellipse(i,n,a/2,v,0,0,qe):t.arc(i,n,v,0,qe),t.closePath();break;case"triangle":d=a?a/2:v,t.moveTo(i+Math.sin(S)*d,n-Math.cos(S)*v),S+=jp,t.lineTo(i+Math.sin(S)*d,n-Math.cos(S)*v),S+=jp,t.lineTo(i+Math.sin(S)*d,n-Math.cos(S)*v),t.closePath();break;case"rectRounded":c=v*.516,r=v-c,l=Math.cos(S+gn)*r,f=Math.cos(S+gn)*(a?a/2-c:r),o=Math.sin(S+gn)*r,h=Math.sin(S+gn)*(a?a/2-c:r),t.arc(i-f,n-o,c,S-Zt,S-Ve),t.arc(i+h,n-l,c,S-Ve,S),t.arc(i+f,n+o,c,S,S+Ve),t.arc(i-h,n+l,c,S+Ve,S+Zt),t.closePath();break;case"rect":if(!m){r=Math.SQRT1_2*v,d=a?a/2:r,t.rect(i-d,n-r,2*d,2*r);break}S+=gn;case"rectRot":f=Math.cos(S)*(a?a/2:v),l=Math.cos(S)*v,o=Math.sin(S)*v,h=Math.sin(S)*(a?a/2:v),t.moveTo(i-f,n-o),t.lineTo(i+h,n-l),t.lineTo(i+f,n+o),t.lineTo(i-h,n+l),t.closePath();break;case"crossRot":S+=gn;case"cross":f=Math.cos(S)*(a?a/2:v),l=Math.cos(S)*v,o=Math.sin(S)*v,h=Math.sin(S)*(a?a/2:v),t.moveTo(i-f,n-o),t.lineTo(i+f,n+o),t.moveTo(i+h,n-l),t.lineTo(i-h,n+l);break;case"star":f=Math.cos(S)*(a?a/2:v),l=Math.cos(S)*v,o=Math.sin(S)*v,h=Math.sin(S)*(a?a/2:v),t.moveTo(i-f,n-o),t.lineTo(i+f,n+o),t.moveTo(i+h,n-l),t.lineTo(i-h,n+l),S+=gn,f=Math.cos(S)*(a?a/2:v),l=Math.cos(S)*v,o=Math.sin(S)*v,h=Math.sin(S)*(a?a/2:v),t.moveTo(i-f,n-o),t.lineTo(i+f,n+o),t.moveTo(i+h,n-l),t.lineTo(i-h,n+l);break;case"line":l=a?a/2:Math.cos(S)*v,o=Math.sin(S)*v,t.moveTo(i-l,n-o),t.lineTo(i+l,n+o);break;case"dash":t.moveTo(i,n),t.lineTo(i+Math.cos(S)*(a?a/2:v),n+Math.sin(S)*v);break;case!1:t.closePath();break}t.fill(),e.borderWidth>0&&t.stroke()}}function tl(t,e,i){return i=i||.5,!e||t&&t.x>e.left-i&&t.x<e.right+i&&t.y>e.top-i&&t.y<e.bottom+i}function zr(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function Tr(t){t.restore()}function t2(t,e,i,n,a){if(!e)return t.lineTo(i.x,i.y);if(a==="middle"){const s=(e.x+i.x)/2;t.lineTo(s,e.y),t.lineTo(s,i.y)}else a==="after"!=!!n?t.lineTo(e.x,i.y):t.lineTo(i.x,e.y);t.lineTo(i.x,i.y)}function e2(t,e,i,n){if(!e)return t.lineTo(i.x,i.y);t.bezierCurveTo(n?e.cp1x:e.cp2x,n?e.cp1y:e.cp2y,n?i.cp2x:i.cp1x,n?i.cp2y:i.cp1y,i.x,i.y)}function i2(t,e){e.translation&&t.translate(e.translation[0],e.translation[1]),ct(e.rotation)||t.rotate(e.rotation),e.color&&(t.fillStyle=e.color),e.textAlign&&(t.textAlign=e.textAlign),e.textBaseline&&(t.textBaseline=e.textBaseline)}function n2(t,e,i,n,a){if(a.strikethrough||a.underline){const s=t.measureText(n),l=e-s.actualBoundingBoxLeft,o=e+s.actualBoundingBoxRight,r=i-s.actualBoundingBoxAscent,c=i+s.actualBoundingBoxDescent,d=a.strikethrough?(r+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=a.decorationWidth||2,t.moveTo(l,d),t.lineTo(o,d),t.stroke()}}function a2(t,e){const i=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=i}function el(t,e,i,n,a,s={}){const l=Lt(e)?e:[e],o=s.strokeWidth>0&&s.strokeColor!=="";let r,c;for(t.save(),t.font=a.string,i2(t,s),r=0;r<l.length;++r)c=l[r],s.backdrop&&a2(t,s.backdrop),o&&(s.strokeColor&&(t.strokeStyle=s.strokeColor),ct(s.strokeWidth)||(t.lineWidth=s.strokeWidth),t.strokeText(c,i,n,s.maxWidth)),t.fillText(c,i,n,s.maxWidth),n2(t,i,n,c,s),n+=Number(a.lineHeight);t.restore()}function ar(t,e){const{x:i,y:n,w:a,h:s,radius:l}=e;t.arc(i+l.topLeft,n+l.topLeft,l.topLeft,1.5*Zt,Zt,!0),t.lineTo(i,n+s-l.bottomLeft),t.arc(i+l.bottomLeft,n+s-l.bottomLeft,l.bottomLeft,Zt,Ve,!0),t.lineTo(i+a-l.bottomRight,n+s),t.arc(i+a-l.bottomRight,n+s-l.bottomRight,l.bottomRight,Ve,0,!0),t.lineTo(i+a,n+l.topRight),t.arc(i+a-l.topRight,n+l.topRight,l.topRight,0,-Ve,!0),t.lineTo(i+l.topLeft,n)}const s2=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,l2=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function o2(t,e){const i=(""+t).match(s2);if(!i||i[1]==="normal")return e*1.2;switch(t=+i[2],i[3]){case"px":return t;case"%":t/=100;break}return e*t}const r2=t=>+t||0;function Pm(t,e){const i={},n=et(e),a=n?Object.keys(e):e,s=et(t)?n?l=>P(t[l],t[e[l]]):l=>t[l]:()=>t;for(const l of a)i[l]=r2(s(l));return i}function Jm(t){return Pm(t,{top:"y",right:"x",bottom:"y",left:"x"})}function _a(t){return Pm(t,["topLeft","topRight","bottomLeft","bottomRight"])}function Ue(t){const e=Jm(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function te(t,e){t=t||{},e=e||Et.font;let i=P(t.size,e.size);typeof i=="string"&&(i=parseInt(i,10));let n=P(t.style,e.style);n&&!(""+n).match(l2)&&(console.warn('Invalid font style specified: "'+n+'"'),n=void 0);const a={family:P(t.family,e.family),lineHeight:o2(P(t.lineHeight,e.lineHeight),i),size:i,style:n,weight:P(t.weight,e.weight),string:""};return a.string=IS(a),a}function Kl(t,e,i,n){let a,s,l;for(a=0,s=t.length;a<s;++a)if(l=t[a],l!==void 0&&l!==void 0)return l}function c2(t,e,i){const{min:n,max:a}=t,s=SS(e,(a-n)/2),l=(o,r)=>i&&o===0?0:o+r;return{min:l(n,-Math.abs(s)),max:l(a,s)}}function Gn(t,e){return Object.assign(Object.create(t),e)}function ef(t,e=[""],i,n,a=()=>t[0]){const s=i||t;typeof n>"u"&&(n=iy("_fallback",t));const l={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:s,_fallback:n,_getTarget:a,override:o=>ef([o,...t],e,s,n)};return new Proxy(l,{deleteProperty(o,r){return delete o[r],delete o._keys,delete t[0][r],!0},get(o,r){return ty(o,r,()=>x2(r,e,t,o))},getOwnPropertyDescriptor(o,r){return Reflect.getOwnPropertyDescriptor(o._scopes[0],r)},getPrototypeOf(){return Reflect.getPrototypeOf(t[0])},has(o,r){return Hp(o).includes(r)},ownKeys(o){return Hp(o)},set(o,r,c){const d=o._storage||(o._storage=a());return o[r]=d[r]=c,delete o._keys,!0}})}function Na(t,e,i,n){const a={_cacheable:!1,_proxy:t,_context:e,_subProxy:i,_stack:new Set,_descriptors:Im(t,n),setContext:s=>Na(t,s,i,n),override:s=>Na(t.override(s),e,i,n)};return new Proxy(a,{deleteProperty(s,l){return delete s[l],delete t[l],!0},get(s,l,o){return ty(s,l,()=>d2(s,l,o))},getOwnPropertyDescriptor(s,l){return s._descriptors.allKeys?Reflect.has(t,l)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(t,l)},getPrototypeOf(){return Reflect.getPrototypeOf(t)},has(s,l){return Reflect.has(t,l)},ownKeys(){return Reflect.ownKeys(t)},set(s,l,o){return t[l]=o,delete s[l],!0}})}function Im(t,e={scriptable:!0,indexable:!0}){const{_scriptable:i=e.scriptable,_indexable:n=e.indexable,_allKeys:a=e.allKeys}=t;return{allKeys:a,scriptable:i,indexable:n,isScriptable:cn(i)?i:()=>i,isIndexable:cn(n)?n:()=>n}}const u2=(t,e)=>t?t+Pd(e):e,nf=(t,e)=>et(e)&&t!=="adapters"&&(Object.getPrototypeOf(e)===null||e.constructor===Object);function ty(t,e,i){if(Object.prototype.hasOwnProperty.call(t,e)||e==="constructor")return t[e];const n=i();return t[e]=n,n}function d2(t,e,i){const{_proxy:n,_context:a,_subProxy:s,_descriptors:l}=t;let o=n[e];return cn(o)&&l.isScriptable(e)&&(o=f2(e,o,t,i)),Lt(o)&&o.length&&(o=h2(e,o,t,l.isIndexable)),nf(e,o)&&(o=Na(o,a,s&&s[e],l)),o}function f2(t,e,i,n){const{_proxy:a,_context:s,_subProxy:l,_stack:o}=i;if(o.has(t))throw new Error("Recursion detected: "+Array.from(o).join("->")+"->"+t);o.add(t);let r=e(s,l||n);return o.delete(t),nf(t,r)&&(r=af(a._scopes,a,t,r)),r}function h2(t,e,i,n){const{_proxy:a,_context:s,_subProxy:l,_descriptors:o}=i;if(typeof s.index<"u"&&n(t))return e[s.index%e.length];if(et(e[0])){const r=e,c=a._scopes.filter(d=>d!==r);e=[];for(const d of r){const f=af(c,a,t,d);e.push(Na(f,s,l&&l[t],o))}}return e}function ey(t,e,i){return cn(t)?t(e,i):t}const p2=(t,e)=>t===!0?e:typeof t=="string"?La(e,t):void 0;function g2(t,e,i,n,a){for(const s of e){const l=p2(i,s);if(l){t.add(l);const o=ey(l._fallback,i,a);if(typeof o<"u"&&o!==i&&o!==n)return o}else if(l===!1&&typeof n<"u"&&i!==n)return null}return!1}function af(t,e,i,n){const a=e._rootScopes,s=ey(e._fallback,i,n),l=[...t,...a],o=new Set;o.add(n);let r=Np(o,l,i,s||i,n);return r===null||typeof s<"u"&&s!==i&&(r=Np(o,l,s,r,n),r===null)?!1:ef(Array.from(o),[""],a,s,()=>b2(e,i,n))}function Np(t,e,i,n,a){for(;i;)i=g2(t,e,i,n,a);return i}function b2(t,e,i){const n=t._getTarget();e in n||(n[e]={});const a=n[e];return Lt(a)&&et(i)?i:a||{}}function x2(t,e,i,n){let a;for(const s of e)if(a=iy(u2(s,t),i),typeof a<"u")return nf(t,a)?af(i,n,t,a):a}function iy(t,e){for(const i of e){if(!i)continue;const n=i[t];if(typeof n<"u")return n}}function Hp(t){let e=t._keys;return e||(e=t._keys=m2(t._scopes)),e}function m2(t){const e=new Set;for(const i of t)for(const n of Object.keys(i).filter(a=>!a.startsWith("_")))e.add(n);return Array.from(e)}const y2=Number.EPSILON||1e-14,Ha=(t,e)=>e<t.length&&!t[e].skip&&t[e],ny=t=>t==="x"?"y":"x";function v2(t,e,i,n){const a=t.skip?e:t,s=e,l=i.skip?e:i,o=Fu(s,a),r=Fu(l,s);let c=o/(o+r),d=r/(o+r);c=isNaN(c)?0:c,d=isNaN(d)?0:d;const f=n*c,h=n*d;return{previous:{x:s.x-f*(l.x-a.x),y:s.y-f*(l.y-a.y)},next:{x:s.x+h*(l.x-a.x),y:s.y+h*(l.y-a.y)}}}function S2(t,e,i){const n=t.length;let a,s,l,o,r,c=Ha(t,0);for(let d=0;d<n-1;++d)if(r=c,c=Ha(t,d+1),!(!r||!c)){if(Rs(e[d],0,y2)){i[d]=i[d+1]=0;continue}a=i[d]/e[d],s=i[d+1]/e[d],o=Math.pow(a,2)+Math.pow(s,2),!(o<=9)&&(l=3/Math.sqrt(o),i[d]=a*l*e[d],i[d+1]=s*l*e[d])}}function _2(t,e,i="x"){const n=ny(i),a=t.length;let s,l,o,r=Ha(t,0);for(let c=0;c<a;++c){if(l=o,o=r,r=Ha(t,c+1),!o)continue;const d=o[i],f=o[n];l&&(s=(d-l[i])/3,o[`cp1${i}`]=d-s,o[`cp1${n}`]=f-s*e[c]),r&&(s=(r[i]-d)/3,o[`cp2${i}`]=d+s,o[`cp2${n}`]=f+s*e[c])}}function k2(t,e="x"){const i=ny(e),n=t.length,a=Array(n).fill(0),s=Array(n);let l,o,r,c=Ha(t,0);for(l=0;l<n;++l)if(o=r,r=c,c=Ha(t,l+1),!!r){if(c){const d=c[e]-r[e];a[l]=d!==0?(c[i]-r[i])/d:0}s[l]=o?c?ni(a[l-1])!==ni(a[l])?0:(a[l-1]+a[l])/2:a[l-1]:a[l]}S2(t,a,s),_2(t,s,e)}function $l(t,e,i){return Math.max(Math.min(t,i),e)}function C2(t,e){let i,n,a,s,l,o=tl(t[0],e);for(i=0,n=t.length;i<n;++i)l=s,s=o,o=i<n-1&&tl(t[i+1],e),s&&(a=t[i],l&&(a.cp1x=$l(a.cp1x,e.left,e.right),a.cp1y=$l(a.cp1y,e.top,e.bottom)),o&&(a.cp2x=$l(a.cp2x,e.left,e.right),a.cp2y=$l(a.cp2y,e.top,e.bottom)))}function D2(t,e,i,n,a){let s,l,o,r;if(e.spanGaps&&(t=t.filter(c=>!c.skip)),e.cubicInterpolationMode==="monotone")k2(t,a);else{let c=n?t[t.length-1]:t[0];for(s=0,l=t.length;s<l;++s)o=t[s],r=v2(c,o,t[Math.min(s+1,l-(n?0:1))%l],e.tension),o.cp1x=r.previous.x,o.cp1y=r.previous.y,o.cp2x=r.next.x,o.cp2y=r.next.y,c=o}e.capBezierPoints&&C2(t,i)}function sf(){return typeof window<"u"&&typeof document<"u"}function lf(t){let e=t.parentNode;return e&&e.toString()==="[object ShadowRoot]"&&(e=e.host),e}function sr(t,e,i){let n;return typeof t=="string"?(n=parseInt(t,10),t.indexOf("%")!==-1&&(n=n/100*e.parentNode[i])):n=t,n}const Ar=t=>t.ownerDocument.defaultView.getComputedStyle(t,null);function w2(t,e){return Ar(t).getPropertyValue(e)}const j2=["top","right","bottom","left"];function Rn(t,e,i){const n={};i=i?"-"+i:"";for(let a=0;a<4;a++){const s=j2[a];n[s]=parseFloat(t[e+"-"+s+i])||0}return n.width=n.left+n.right,n.height=n.top+n.bottom,n}const z2=(t,e,i)=>(t>0||e>0)&&(!i||!i.shadowRoot);function T2(t,e){const i=t.touches,n=i&&i.length?i[0]:t,{offsetX:a,offsetY:s}=n;let l=!1,o,r;if(z2(a,s,t.target))o=a,r=s;else{const c=e.getBoundingClientRect();o=n.clientX-c.left,r=n.clientY-c.top,l=!0}return{x:o,y:r,box:l}}function Sn(t,e){if("native"in t)return t;const{canvas:i,currentDevicePixelRatio:n}=e,a=Ar(i),s=a.boxSizing==="border-box",l=Rn(a,"padding"),o=Rn(a,"border","width"),{x:r,y:c,box:d}=T2(t,i),f=l.left+(d&&o.left),h=l.top+(d&&o.top);let{width:g,height:m}=e;return s&&(g-=l.width+o.width,m-=l.height+o.height),{x:Math.round((r-f)/g*i.width/n),y:Math.round((c-h)/m*i.height/n)}}function A2(t,e,i){let n,a;if(e===void 0||i===void 0){const s=t&&lf(t);if(!s)e=t.clientWidth,i=t.clientHeight;else{const l=s.getBoundingClientRect(),o=Ar(s),r=Rn(o,"border","width"),c=Rn(o,"padding");e=l.width-c.width-r.width,i=l.height-c.height-r.height,n=sr(o.maxWidth,s,"clientWidth"),a=sr(o.maxHeight,s,"clientHeight")}}return{width:e,height:i,maxWidth:n||nr,maxHeight:a||nr}}const Pl=t=>Math.round(t*10)/10;function M2(t,e,i,n){const a=Ar(t),s=Rn(a,"margin"),l=sr(a.maxWidth,t,"clientWidth")||nr,o=sr(a.maxHeight,t,"clientHeight")||nr,r=A2(t,e,i);let{width:c,height:d}=r;if(a.boxSizing==="content-box"){const h=Rn(a,"border","width"),g=Rn(a,"padding");c-=g.width+h.width,d-=g.height+h.height}return c=Math.max(0,c-s.width),d=Math.max(0,n?c/n:d-s.height),c=Pl(Math.min(c,l,r.maxWidth)),d=Pl(Math.min(d,o,r.maxHeight)),c&&!d&&(d=Pl(c/2)),(e!==void 0||i!==void 0)&&n&&r.height&&d>r.height&&(d=r.height,c=Pl(Math.floor(d*n))),{width:c,height:d}}function Up(t,e,i){const n=e||1,a=Math.floor(t.height*n),s=Math.floor(t.width*n);t.height=Math.floor(t.height),t.width=Math.floor(t.width);const l=t.canvas;return l.style&&(i||!l.style.height&&!l.style.width)&&(l.style.height=`${t.height}px`,l.style.width=`${t.width}px`),t.currentDevicePixelRatio!==n||l.height!==a||l.width!==s?(t.currentDevicePixelRatio=n,l.height=a,l.width=s,t.ctx.setTransform(n,0,0,n,0,0),!0):!1}const O2=function(){let t=!1;try{const e={get passive(){return t=!0,!1}};sf()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch{}return t}();function Yp(t,e){const i=w2(t,e),n=i&&i.match(/^(\d+)(\.\d+)?px$/);return n?+n[1]:void 0}function _n(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:t.y+i*(e.y-t.y)}}function R2(t,e,i,n){return{x:t.x+i*(e.x-t.x),y:n==="middle"?i<.5?t.y:e.y:n==="after"?i<1?t.y:e.y:i>0?e.y:t.y}}function E2(t,e,i,n){const a={x:t.cp2x,y:t.cp2y},s={x:e.cp1x,y:e.cp1y},l=_n(t,a,i),o=_n(a,s,i),r=_n(s,e,i),c=_n(l,o,i),d=_n(o,r,i);return _n(c,d,i)}const B2=function(t,e){return{x(i){return t+t+e-i},setWidth(i){e=i},textAlign(i){return i==="center"?i:i==="right"?"left":"right"},xPlus(i,n){return i-n},leftForLtr(i,n){return i-n}}},L2=function(){return{x(t){return t},setWidth(t){},textAlign(t){return t},xPlus(t,e){return t+e},leftForLtr(t,e){return t}}};function ka(t,e,i){return t?B2(e,i):L2()}function ay(t,e){let i,n;(e==="ltr"||e==="rtl")&&(i=t.canvas.style,n=[i.getPropertyValue("direction"),i.getPropertyPriority("direction")],i.setProperty("direction",e,"important"),t.prevTextDirection=n)}function sy(t,e){e!==void 0&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function ly(t){return t==="angle"?{between:Gm,compare:BS,normalize:Pe}:{between:Zi,compare:(e,i)=>e-i,normalize:e=>e}}function Fp({start:t,end:e,count:i,loop:n,style:a}){return{start:t%i,end:e%i,loop:n&&(e-t+1)%i===0,style:a}}function N2(t,e,i){const{property:n,start:a,end:s}=i,{between:l,normalize:o}=ly(n),r=e.length;let{start:c,end:d,loop:f}=t,h,g;if(f){for(c+=r,d+=r,h=0,g=r;h<g&&l(o(e[c%r][n]),a,s);++h)c--,d--;c%=r,d%=r}return d<c&&(d+=r),{start:c,end:d,loop:f,style:t.style}}function oy(t,e,i){if(!i)return[t];const{property:n,start:a,end:s}=i,l=e.length,{compare:o,between:r,normalize:c}=ly(n),{start:d,end:f,loop:h,style:g}=N2(t,e,i),m=[];let v=!1,S=null,b,x,y;const _=()=>r(a,y,b)&&o(a,y)!==0,D=()=>o(s,b)===0||r(s,y,b),A=()=>v||_(),j=()=>!v||D();for(let T=d,M=d;T<=f;++T)x=e[T%l],!x.skip&&(b=c(x[n]),b!==y&&(v=r(b,a,s),S===null&&A()&&(S=o(b,a)===0?T:M),S!==null&&j()&&(m.push(Fp({start:S,end:T,loop:h,count:l,style:g})),S=null),M=T,y=b));return S!==null&&m.push(Fp({start:S,end:f,loop:h,count:l,style:g})),m}function ry(t,e){const i=[],n=t.segments;for(let a=0;a<n.length;a++){const s=oy(n[a],t.points,e);s.length&&i.push(...s)}return i}function H2(t,e,i,n){let a=0,s=e-1;if(i&&!n)for(;a<e&&!t[a].skip;)a++;for(;a<e&&t[a].skip;)a++;for(a%=e,i&&(s+=a);s>a&&t[s%e].skip;)s--;return s%=e,{start:a,end:s}}function U2(t,e,i,n){const a=t.length,s=[];let l=e,o=t[e],r;for(r=e+1;r<=i;++r){const c=t[r%a];c.skip||c.stop?o.skip||(n=!1,s.push({start:e%a,end:(r-1)%a,loop:n}),e=l=c.stop?r:null):(l=r,o.skip&&(e=r)),o=c}return l!==null&&s.push({start:e%a,end:l%a,loop:n}),s}function Y2(t,e){const i=t.points,n=t.options.spanGaps,a=i.length;if(!a)return[];const s=!!t._loop,{start:l,end:o}=H2(i,a,s,n);if(n===!0)return Vp(t,[{start:l,end:o,loop:s}],i,e);const r=o<l?o+a:o,c=!!t._fullLoop&&l===0&&o===a-1;return Vp(t,U2(i,l,r,c),i,e)}function Vp(t,e,i,n){return!n||!n.setContext||!i?e:F2(t,e,i,n)}function F2(t,e,i,n){const a=t._chart.getContext(),s=Gp(t.options),{_datasetIndex:l,options:{spanGaps:o}}=t,r=i.length,c=[];let d=s,f=e[0].start,h=f;function g(m,v,S,b){const x=o?-1:1;if(m!==v){for(m+=r;i[m%r].skip;)m-=x;for(;i[v%r].skip;)v+=x;m%r!==v%r&&(c.push({start:m%r,end:v%r,loop:S,style:b}),d=b,f=v%r)}}for(const m of e){f=o?f:m.start;let v=i[f%r],S;for(h=f+1;h<=m.end;h++){const b=i[h%r];S=Gp(n.setContext(Gn(a,{type:"segment",p0:v,p1:b,p0DataIndex:(h-1)%r,p1DataIndex:h%r,datasetIndex:l}))),V2(S,d)&&g(f,h-1,m.loop,d),v=b,d=S}f<h-1&&g(f,h-1,m.loop,d)}return c}function Gp(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function V2(t,e){if(!e)return!1;const i=[],n=function(a,s){return tf(s)?(i.includes(s)||i.push(s),i.indexOf(s)):s};return JSON.stringify(t,n)!==JSON.stringify(e,n)}function Jl(t,e,i){return t.options.clip?t[i]:e[i]}function G2(t,e){const{xScale:i,yScale:n}=t;return i&&n?{left:Jl(i,e,"left"),right:Jl(i,e,"right"),top:Jl(n,e,"top"),bottom:Jl(n,e,"bottom")}:e}function cy(t,e){const i=e._clip;if(i.disabled)return!1;const n=G2(e,t.chartArea);return{left:i.left===!1?0:n.left-(i.left===!0?0:i.left),right:i.right===!1?t.width:n.right+(i.right===!0?0:i.right),top:i.top===!1?0:n.top-(i.top===!0?0:i.top),bottom:i.bottom===!1?t.height:n.bottom+(i.bottom===!0?0:i.bottom)}}/*!
 * Chart.js v4.5.0
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class q2{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,i,n,a){const s=i.listeners[a],l=i.duration;s.forEach(o=>o({chart:e,initial:i.initial,numSteps:l,currentStep:Math.min(n-i.start,l)}))}_refresh(){this._request||(this._running=!0,this._request=Qm.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(e=Date.now()){let i=0;this._charts.forEach((n,a)=>{if(!n.running||!n.items.length)return;const s=n.items;let l=s.length-1,o=!1,r;for(;l>=0;--l)r=s[l],r._active?(r._total>n.duration&&(n.duration=r._total),r.tick(e),o=!0):(s[l]=s[s.length-1],s.pop());o&&(a.draw(),this._notify(a,n,e,"progress")),s.length||(n.running=!1,this._notify(a,n,e,"complete"),n.initial=!1),i+=s.length}),this._lastDate=e,i===0&&(this._running=!1)}_getAnims(e){const i=this._charts;let n=i.get(e);return n||(n={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},i.set(e,n)),n}listen(e,i,n){this._getAnims(e).listeners[i].push(n)}add(e,i){!i||!i.length||this._getAnims(e).items.push(...i)}has(e){return this._getAnims(e).items.length>0}start(e){const i=this._charts.get(e);i&&(i.running=!0,i.start=Date.now(),i.duration=i.items.reduce((n,a)=>Math.max(n,a._duration),0),this._refresh())}running(e){if(!this._running)return!1;const i=this._charts.get(e);return!(!i||!i.running||!i.items.length)}stop(e){const i=this._charts.get(e);if(!i||!i.items.length)return;const n=i.items;let a=n.length-1;for(;a>=0;--a)n[a].cancel();i.items=[],this._notify(e,i,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var gi=new q2;const qp="transparent",X2={boolean(t,e,i){return i>.5?e:t},color(t,e,i){const n=Rp(t||qp),a=n.valid&&Rp(e||qp);return a&&a.valid?a.mix(n,i).hexString():e},number(t,e,i){return t+(e-t)*i}};class Q2{constructor(e,i,n,a){const s=i[n];a=Kl([e.to,a,s,e.from]);const l=Kl([e.from,s,a]);this._active=!0,this._fn=e.fn||X2[e.type||typeof l],this._easing=Es[e.easing]||Es.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=i,this._prop=n,this._from=l,this._to=a,this._promises=void 0}active(){return this._active}update(e,i,n){if(this._active){this._notify(!1);const a=this._target[this._prop],s=n-this._start,l=this._duration-s;this._start=n,this._duration=Math.floor(Math.max(l,e.duration)),this._total+=s,this._loop=!!e.loop,this._to=Kl([e.to,i,a,e.from]),this._from=Kl([e.from,a,i])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const i=e-this._start,n=this._duration,a=this._prop,s=this._from,l=this._loop,o=this._to;let r;if(this._active=s!==o&&(l||i<n),!this._active){this._target[a]=o,this._notify(!0);return}if(i<0){this._target[a]=s;return}r=i/n%2,r=l&&r>1?2-r:r,r=this._easing(Math.min(1,Math.max(0,r))),this._target[a]=this._fn(s,o,r)}wait(){const e=this._promises||(this._promises=[]);return new Promise((i,n)=>{e.push({res:i,rej:n})})}_notify(e){const i=e?"res":"rej",n=this._promises||[];for(let a=0;a<n.length;a++)n[a][i]()}}class uy{constructor(e,i){this._chart=e,this._properties=new Map,this.configure(i)}configure(e){if(!et(e))return;const i=Object.keys(Et.animation),n=this._properties;Object.getOwnPropertyNames(e).forEach(a=>{const s=e[a];if(!et(s))return;const l={};for(const o of i)l[o]=s[o];(Lt(s.properties)&&s.properties||[a]).forEach(o=>{(o===a||!n.has(o))&&n.set(o,l)})})}_animateOptions(e,i){const n=i.options,a=W2(e,n);if(!a)return[];const s=this._createAnimations(a,n);return n.$shared&&Z2(e.options.$animations,n).then(()=>{e.options=n},()=>{}),s}_createAnimations(e,i){const n=this._properties,a=[],s=e.$animations||(e.$animations={}),l=Object.keys(i),o=Date.now();let r;for(r=l.length-1;r>=0;--r){const c=l[r];if(c.charAt(0)==="$")continue;if(c==="options"){a.push(...this._animateOptions(e,i));continue}const d=i[c];let f=s[c];const h=n.get(c);if(f)if(h&&f.active()){f.update(h,d,o);continue}else f.cancel();if(!h||!h.duration){e[c]=d;continue}s[c]=f=new Q2(h,e,c,d),a.push(f)}return a}update(e,i){if(this._properties.size===0){Object.assign(e,i);return}const n=this._createAnimations(e,i);if(n.length)return gi.add(this._chart,n),!0}}function Z2(t,e){const i=[],n=Object.keys(e);for(let a=0;a<n.length;a++){const s=t[n[a]];s&&s.active()&&i.push(s.wait())}return Promise.all(i)}function W2(t,e){if(!e)return;let i=t.options;if(!i){t.options=e;return}return i.$shared&&(t.options=i=Object.assign({},i,{$shared:!1,$animations:{}})),i}function Xp(t,e){const i=t&&t.options||{},n=i.reverse,a=i.min===void 0?e:0,s=i.max===void 0?e:0;return{start:n?s:a,end:n?a:s}}function K2(t,e,i){if(i===!1)return!1;const n=Xp(t,i),a=Xp(e,i);return{top:a.end,right:n.end,bottom:a.start,left:n.start}}function $2(t){let e,i,n,a;return et(t)?(e=t.top,i=t.right,n=t.bottom,a=t.left):e=i=n=a=t,{top:e,right:i,bottom:n,left:a,disabled:t===!1}}function dy(t,e){const i=[],n=t._getSortedDatasetMetas(e);let a,s;for(a=0,s=n.length;a<s;++a)i.push(n[a].index);return i}function Qp(t,e,i,n={}){const a=t.keys,s=n.mode==="single";let l,o,r,c;if(e===null)return;let d=!1;for(l=0,o=a.length;l<o;++l){if(r=+a[l],r===i){if(d=!0,n.all)continue;break}c=t.values[r],ee(c)&&(s||e===0||ni(e)===ni(c))&&(e+=c)}return!d&&!n.all?0:e}function P2(t,e){const{iScale:i,vScale:n}=e,a=i.axis==="x"?"x":"y",s=n.axis==="x"?"x":"y",l=Object.keys(t),o=new Array(l.length);let r,c,d;for(r=0,c=l.length;r<c;++r)d=l[r],o[r]={[a]:d,[s]:t[d]};return o}function Oc(t,e){const i=t&&t.options.stacked;return i||i===void 0&&e.stack!==void 0}function J2(t,e,i){return`${t.id}.${e.id}.${i.stack||i.type}`}function I2(t){const{min:e,max:i,minDefined:n,maxDefined:a}=t.getUserBounds();return{min:n?e:Number.NEGATIVE_INFINITY,max:a?i:Number.POSITIVE_INFINITY}}function t_(t,e,i){const n=t[e]||(t[e]={});return n[i]||(n[i]={})}function Zp(t,e,i,n){for(const a of e.getMatchingVisibleMetas(n).reverse()){const s=t[a.index];if(i&&s>0||!i&&s<0)return a.index}return null}function Wp(t,e){const{chart:i,_cachedMeta:n}=t,a=i._stacks||(i._stacks={}),{iScale:s,vScale:l,index:o}=n,r=s.axis,c=l.axis,d=J2(s,l,n),f=e.length;let h;for(let g=0;g<f;++g){const m=e[g],{[r]:v,[c]:S}=m,b=m._stacks||(m._stacks={});h=b[c]=t_(a,d,v),h[o]=S,h._top=Zp(h,l,!0,n.type),h._bottom=Zp(h,l,!1,n.type);const x=h._visualValues||(h._visualValues={});x[o]=S}}function Rc(t,e){const i=t.scales;return Object.keys(i).filter(n=>i[n].axis===e).shift()}function e_(t,e){return Gn(t,{active:!1,dataset:void 0,datasetIndex:e,index:e,mode:"default",type:"dataset"})}function i_(t,e,i){return Gn(t,{active:!1,dataIndex:e,parsed:void 0,raw:void 0,element:i,index:e,mode:"default",type:"data"})}function ss(t,e){const i=t.controller.index,n=t.vScale&&t.vScale.axis;if(n){e=e||t._parsed;for(const a of e){const s=a._stacks;if(!s||s[n]===void 0||s[n][i]===void 0)return;delete s[n][i],s[n]._visualValues!==void 0&&s[n]._visualValues[i]!==void 0&&delete s[n]._visualValues[i]}}}const Ec=t=>t==="reset"||t==="none",Kp=(t,e)=>e?t:Object.assign({},t),n_=(t,e,i)=>t&&!e.hidden&&e._stacked&&{keys:dy(i,!0),values:null};class Ca{constructor(e,i){this.chart=e,this._ctx=e.ctx,this.index=i,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=Oc(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&ss(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,i=this._cachedMeta,n=this.getDataset(),a=(f,h,g,m)=>f==="x"?h:f==="r"?m:g,s=i.xAxisID=P(n.xAxisID,Rc(e,"x")),l=i.yAxisID=P(n.yAxisID,Rc(e,"y")),o=i.rAxisID=P(n.rAxisID,Rc(e,"r")),r=i.indexAxis,c=i.iAxisID=a(r,s,l,o),d=i.vAxisID=a(r,l,s,o);i.xScale=this.getScaleForId(s),i.yScale=this.getScaleForId(l),i.rScale=this.getScaleForId(o),i.iScale=this.getScaleForId(c),i.vScale=this.getScaleForId(d)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const i=this._cachedMeta;return e===i.iScale?i.vScale:i.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&Ap(this._data,this),e._stacked&&ss(e)}_dataCheck(){const e=this.getDataset(),i=e.data||(e.data=[]),n=this._data;if(et(i)){const a=this._cachedMeta;this._data=P2(i,a)}else if(n!==i){if(n){Ap(n,this);const a=this._cachedMeta;ss(a),a._parsed=[]}i&&Object.isExtensible(i)&&US(i,this),this._syncList=[],this._data=i}}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const i=this._cachedMeta,n=this.getDataset();let a=!1;this._dataCheck();const s=i._stacked;i._stacked=Oc(i.vScale,i),i.stack!==n.stack&&(a=!0,ss(i),i.stack=n.stack),this._resyncElements(e),(a||s!==i._stacked)&&(Wp(this,i._parsed),i._stacked=Oc(i.vScale,i))}configure(){const e=this.chart.config,i=e.datasetScopeKeys(this._type),n=e.getOptionScopes(this.getDataset(),i,!0);this.options=e.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,i){const{_cachedMeta:n,_data:a}=this,{iScale:s,_stacked:l}=n,o=s.axis;let r=e===0&&i===a.length?!0:n._sorted,c=e>0&&n._parsed[e-1],d,f,h;if(this._parsing===!1)n._parsed=a,n._sorted=!0,h=a;else{Lt(a[e])?h=this.parseArrayData(n,a,e,i):et(a[e])?h=this.parseObjectData(n,a,e,i):h=this.parsePrimitiveData(n,a,e,i);const g=()=>f[o]===null||c&&f[o]<c[o];for(d=0;d<i;++d)n._parsed[d+e]=f=h[d],r&&(g()&&(r=!1),c=f);n._sorted=r}l&&Wp(this,h)}parsePrimitiveData(e,i,n,a){const{iScale:s,vScale:l}=e,o=s.axis,r=l.axis,c=s.getLabels(),d=s===l,f=new Array(a);let h,g,m;for(h=0,g=a;h<g;++h)m=h+n,f[h]={[o]:d||s.parse(c[m],m),[r]:l.parse(i[m],m)};return f}parseArrayData(e,i,n,a){const{xScale:s,yScale:l}=e,o=new Array(a);let r,c,d,f;for(r=0,c=a;r<c;++r)d=r+n,f=i[d],o[r]={x:s.parse(f[0],d),y:l.parse(f[1],d)};return o}parseObjectData(e,i,n,a){const{xScale:s,yScale:l}=e,{xAxisKey:o="x",yAxisKey:r="y"}=this._parsing,c=new Array(a);let d,f,h,g;for(d=0,f=a;d<f;++d)h=d+n,g=i[h],c[d]={x:s.parse(La(g,o),h),y:l.parse(La(g,r),h)};return c}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,i,n){const a=this.chart,s=this._cachedMeta,l=i[e.axis],o={keys:dy(a,!0),values:i._stacks[e.axis]._visualValues};return Qp(o,l,s.index,{mode:n})}updateRangeFromParsed(e,i,n,a){const s=n[i.axis];let l=s===null?NaN:s;const o=a&&n._stacks[i.axis];a&&o&&(a.values=o,l=Qp(a,s,this._cachedMeta.index)),e.min=Math.min(e.min,l),e.max=Math.max(e.max,l)}getMinMax(e,i){const n=this._cachedMeta,a=n._parsed,s=n._sorted&&e===n.iScale,l=a.length,o=this._getOtherScale(e),r=n_(i,n,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:d,max:f}=I2(o);let h,g;function m(){g=a[h];const v=g[o.axis];return!ee(g[e.axis])||d>v||f<v}for(h=0;h<l&&!(!m()&&(this.updateRangeFromParsed(c,e,g,r),s));++h);if(s){for(h=l-1;h>=0;--h)if(!m()){this.updateRangeFromParsed(c,e,g,r);break}}return c}getAllParsedValues(e){const i=this._cachedMeta._parsed,n=[];let a,s,l;for(a=0,s=i.length;a<s;++a)l=i[a][e.axis],ee(l)&&n.push(l);return n}getMaxOverflow(){return!1}getLabelAndValue(e){const i=this._cachedMeta,n=i.iScale,a=i.vScale,s=this.getParsed(e);return{label:n?""+n.getLabelForValue(s[n.axis]):"",value:a?""+a.getLabelForValue(s[a.axis]):""}}_update(e){const i=this._cachedMeta;this.update(e||"default"),i._clip=$2(P(this.options.clip,K2(i.xScale,i.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,i=this.chart,n=this._cachedMeta,a=n.data||[],s=i.chartArea,l=[],o=this._drawStart||0,r=this._drawCount||a.length-o,c=this.options.drawActiveElementsOnTop;let d;for(n.dataset&&n.dataset.draw(e,s,o,r),d=o;d<o+r;++d){const f=a[d];f.hidden||(f.active&&c?l.push(f):f.draw(e,s))}for(d=0;d<l.length;++d)l[d].draw(e,s)}getStyle(e,i){const n=i?"active":"default";return e===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(e||0,n)}getContext(e,i,n){const a=this.getDataset();let s;if(e>=0&&e<this._cachedMeta.data.length){const l=this._cachedMeta.data[e];s=l.$context||(l.$context=i_(this.getContext(),e,l)),s.parsed=this.getParsed(e),s.raw=a.data[e],s.index=s.dataIndex=e}else s=this.$context||(this.$context=e_(this.chart.getContext(),this.index)),s.dataset=a,s.index=s.datasetIndex=this.index;return s.active=!!i,s.mode=n,s}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,i){return this._resolveElementOptions(this.dataElementType.id,i,e)}_resolveElementOptions(e,i="default",n){const a=i==="active",s=this._cachedDataOpts,l=e+"-"+i,o=s[l],r=this.enableOptionSharing&&Js(n);if(o)return Kp(o,r);const c=this.chart.config,d=c.datasetElementScopeKeys(this._type,e),f=a?[`${e}Hover`,"hover",e,""]:[e,""],h=c.getOptionScopes(this.getDataset(),d),g=Object.keys(Et.elements[e]),m=()=>this.getContext(n,a,i),v=c.resolveNamedOptions(h,g,m,f);return v.$shared&&(v.$shared=r,s[l]=Object.freeze(Kp(v,r))),v}_resolveAnimations(e,i,n){const a=this.chart,s=this._cachedDataOpts,l=`animation-${i}`,o=s[l];if(o)return o;let r;if(a.options.animation!==!1){const d=this.chart.config,f=d.datasetAnimationScopeKeys(this._type,i),h=d.getOptionScopes(this.getDataset(),f);r=d.createResolver(h,this.getContext(e,n,i))}const c=new uy(a,r&&r.animations);return r&&r._cacheable&&(s[l]=Object.freeze(c)),c}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,i){return!i||Ec(e)||this.chart._animationsDisabled}_getSharedOptions(e,i){const n=this.resolveDataElementOptions(e,i),a=this._sharedOptions,s=this.getSharedOptions(n),l=this.includeOptions(i,s)||s!==a;return this.updateSharedOptions(s,i,n),{sharedOptions:s,includeOptions:l}}updateElement(e,i,n,a){Ec(a)?Object.assign(e,n):this._resolveAnimations(i,a).update(e,n)}updateSharedOptions(e,i,n){e&&!Ec(i)&&this._resolveAnimations(void 0,i).update(e,n)}_setStyle(e,i,n,a){e.active=a;const s=this.getStyle(i,a);this._resolveAnimations(i,n,a).update(e,{options:!a&&this.getSharedOptions(s)||s})}removeHoverStyle(e,i,n){this._setStyle(e,n,"active",!1)}setHoverStyle(e,i,n){this._setStyle(e,n,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const i=this._data,n=this._cachedMeta.data;for(const[o,r,c]of this._syncList)this[o](r,c);this._syncList=[];const a=n.length,s=i.length,l=Math.min(s,a);l&&this.parse(0,l),s>a?this._insertElements(a,s-a,e):s<a&&this._removeElements(s,a-s)}_insertElements(e,i,n=!0){const a=this._cachedMeta,s=a.data,l=e+i;let o;const r=c=>{for(c.length+=i,o=c.length-1;o>=l;o--)c[o]=c[o-i]};for(r(s),o=e;o<l;++o)s[o]=new this.dataElementType;this._parsing&&r(a._parsed),this.parse(e,i),n&&this.updateElements(s,e,i,"reset")}updateElements(e,i,n,a){}_removeElements(e,i){const n=this._cachedMeta;if(this._parsing){const a=n._parsed.splice(e,i);n._stacked&&ss(n,a)}n.data.splice(e,i)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[i,n,a]=e;this[i](n,a)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,i){i&&this._sync(["_removeElements",e,i]);const n=arguments.length-2;n&&this._sync(["_insertElements",e,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}Z(Ca,"defaults",{}),Z(Ca,"datasetElementType",null),Z(Ca,"dataElementType",null);function a_(t,e){if(!t._cache.$bar){const i=t.getMatchingVisibleMetas(e);let n=[];for(let a=0,s=i.length;a<s;a++)n=n.concat(i[a].controller.getAllParsedValues(t));t._cache.$bar=Xm(n.sort((a,s)=>a-s))}return t._cache.$bar}function s_(t){const e=t.iScale,i=a_(e,t.type);let n=e._length,a,s,l,o;const r=()=>{l===32767||l===-32768||(Js(o)&&(n=Math.min(n,Math.abs(l-o)||n)),o=l)};for(a=0,s=i.length;a<s;++a)l=e.getPixelForValue(i[a]),r();for(o=void 0,a=0,s=e.ticks.length;a<s;++a)l=e.getPixelForTick(a),r();return n}function l_(t,e,i,n){const a=i.barThickness;let s,l;return ct(a)?(s=e.min*i.categoryPercentage,l=i.barPercentage):(s=a*n,l=1),{chunk:s/n,ratio:l,start:e.pixels[t]-s/2}}function o_(t,e,i,n){const a=e.pixels,s=a[t];let l=t>0?a[t-1]:null,o=t<a.length-1?a[t+1]:null;const r=i.categoryPercentage;l===null&&(l=s-(o===null?e.end-e.start:o-s)),o===null&&(o=s+s-l);const c=s-(s-Math.min(l,o))/2*r;return{chunk:Math.abs(o-l)/2*r/n,ratio:i.barPercentage,start:c}}function r_(t,e,i,n){const a=i.parse(t[0],n),s=i.parse(t[1],n),l=Math.min(a,s),o=Math.max(a,s);let r=l,c=o;Math.abs(l)>Math.abs(o)&&(r=o,c=l),e[i.axis]=c,e._custom={barStart:r,barEnd:c,start:a,end:s,min:l,max:o}}function fy(t,e,i,n){return Lt(t)?r_(t,e,i,n):e[i.axis]=i.parse(t,n),e}function $p(t,e,i,n){const a=t.iScale,s=t.vScale,l=a.getLabels(),o=a===s,r=[];let c,d,f,h;for(c=i,d=i+n;c<d;++c)h=e[c],f={},f[a.axis]=o||a.parse(l[c],c),r.push(fy(h,f,s,c));return r}function Bc(t){return t&&t.barStart!==void 0&&t.barEnd!==void 0}function c_(t,e,i){return t!==0?ni(t):(e.isHorizontal()?1:-1)*(e.min>=i?1:-1)}function u_(t){let e,i,n,a,s;return t.horizontal?(e=t.base>t.x,i="left",n="right"):(e=t.base<t.y,i="bottom",n="top"),e?(a="end",s="start"):(a="start",s="end"),{start:i,end:n,reverse:e,top:a,bottom:s}}function d_(t,e,i,n){let a=e.borderSkipped;const s={};if(!a){t.borderSkipped=s;return}if(a===!0){t.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:l,end:o,reverse:r,top:c,bottom:d}=u_(t);a==="middle"&&i&&(t.enableBorderRadius=!0,(i._top||0)===n?a=c:(i._bottom||0)===n?a=d:(s[Pp(d,l,o,r)]=!0,a=c)),s[Pp(a,l,o,r)]=!0,t.borderSkipped=s}function Pp(t,e,i,n){return n?(t=f_(t,e,i),t=Jp(t,i,e)):t=Jp(t,e,i),t}function f_(t,e,i){return t===e?i:t===i?e:t}function Jp(t,e,i){return t==="start"?e:t==="end"?i:t}function h_(t,{inflateAmount:e},i){t.inflateAmount=e==="auto"?i===1?.33:0:e}class yo extends Ca{parsePrimitiveData(e,i,n,a){return $p(e,i,n,a)}parseArrayData(e,i,n,a){return $p(e,i,n,a)}parseObjectData(e,i,n,a){const{iScale:s,vScale:l}=e,{xAxisKey:o="x",yAxisKey:r="y"}=this._parsing,c=s.axis==="x"?o:r,d=l.axis==="x"?o:r,f=[];let h,g,m,v;for(h=n,g=n+a;h<g;++h)v=i[h],m={},m[s.axis]=s.parse(La(v,c),h),f.push(fy(La(v,d),m,l,h));return f}updateRangeFromParsed(e,i,n,a){super.updateRangeFromParsed(e,i,n,a);const s=n._custom;s&&i===this._cachedMeta.vScale&&(e.min=Math.min(e.min,s.min),e.max=Math.max(e.max,s.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const i=this._cachedMeta,{iScale:n,vScale:a}=i,s=this.getParsed(e),l=s._custom,o=Bc(l)?"["+l.start+", "+l.end+"]":""+a.getLabelForValue(s[a.axis]);return{label:""+n.getLabelForValue(s[n.axis]),value:o}}initialize(){this.enableOptionSharing=!0,super.initialize();const e=this._cachedMeta;e.stack=this.getDataset().stack}update(e){const i=this._cachedMeta;this.updateElements(i.data,0,i.data.length,e)}updateElements(e,i,n,a){const s=a==="reset",{index:l,_cachedMeta:{vScale:o}}=this,r=o.getBasePixel(),c=o.isHorizontal(),d=this._getRuler(),{sharedOptions:f,includeOptions:h}=this._getSharedOptions(i,a);for(let g=i;g<i+n;g++){const m=this.getParsed(g),v=s||ct(m[o.axis])?{base:r,head:r}:this._calculateBarValuePixels(g),S=this._calculateBarIndexPixels(g,d),b=(m._stacks||{})[o.axis],x={horizontal:c,base:v.base,enableBorderRadius:!b||Bc(m._custom)||l===b._top||l===b._bottom,x:c?v.head:S.center,y:c?S.center:v.head,height:c?S.size:Math.abs(v.size),width:c?Math.abs(v.size):S.size};h&&(x.options=f||this.resolveDataElementOptions(g,e[g].active?"active":a));const y=x.options||e[g].options;d_(x,y,b,l),h_(x,y,d.ratio),this.updateElement(e[g],g,x,a)}}_getStacks(e,i){const{iScale:n}=this._cachedMeta,a=n.getMatchingVisibleMetas(this._type).filter(d=>d.controller.options.grouped),s=n.options.stacked,l=[],o=this._cachedMeta.controller.getParsed(i),r=o&&o[n.axis],c=d=>{const f=d._parsed.find(g=>g[n.axis]===r),h=f&&f[d.vScale.axis];if(ct(h)||isNaN(h))return!0};for(const d of a)if(!(i!==void 0&&c(d))&&((s===!1||l.indexOf(d.stack)===-1||s===void 0&&d.stack===void 0)&&l.push(d.stack),d.index===e))break;return l.length||l.push(void 0),l}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,i=this.chart.options.indexAxis;return Object.keys(e).filter(n=>e[n].axis===i).shift()}_getAxis(){const e={},i=this.getFirstScaleIdForIndexAxis();for(const n of this.chart.data.datasets)e[P(this.chart.options.indexAxis==="x"?n.xAxisID:n.yAxisID,i)]=!0;return Object.keys(e)}_getStackIndex(e,i,n){const a=this._getStacks(e,n),s=i!==void 0?a.indexOf(i):-1;return s===-1?a.length-1:s}_getRuler(){const e=this.options,i=this._cachedMeta,n=i.iScale,a=[];let s,l;for(s=0,l=i.data.length;s<l;++s)a.push(n.getPixelForValue(this.getParsed(s)[n.axis],s));const o=e.barThickness;return{min:o||s_(i),pixels:a,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:e.grouped,ratio:o?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:i,_stacked:n,index:a},options:{base:s,minBarLength:l}}=this,o=s||0,r=this.getParsed(e),c=r._custom,d=Bc(c);let f=r[i.axis],h=0,g=n?this.applyStack(i,r,n):f,m,v;g!==f&&(h=g-f,g=f),d&&(f=c.barStart,g=c.barEnd-c.barStart,f!==0&&ni(f)!==ni(c.barEnd)&&(h=0),h+=f);const S=!ct(s)&&!d?s:h;let b=i.getPixelForValue(S);if(this.chart.getDataVisibility(e)?m=i.getPixelForValue(h+g):m=b,v=m-b,Math.abs(v)<l){v=c_(v,i,o)*l,f===o&&(b-=v/2);const x=i.getPixelForDecimal(0),y=i.getPixelForDecimal(1),_=Math.min(x,y),D=Math.max(x,y);b=Math.max(Math.min(b,D),_),m=b+v,n&&!d&&(r._stacks[i.axis]._visualValues[a]=i.getValueForPixel(m)-i.getValueForPixel(b))}if(b===i.getPixelForValue(o)){const x=ni(v)*i.getLineWidthForValue(o)/2;b+=x,v-=x}return{size:v,base:b,head:m,center:m+v/2}}_calculateBarIndexPixels(e,i){const n=i.scale,a=this.options,s=a.skipNull,l=P(a.maxBarThickness,1/0);let o,r;const c=this._getAxisCount();if(i.grouped){const d=s?this._getStackCount(e):i.stackCount,f=a.barThickness==="flex"?o_(e,i,a,d*c):l_(e,i,a,d*c),h=this.chart.options.indexAxis==="x"?this.getDataset().xAxisID:this.getDataset().yAxisID,g=this._getAxis().indexOf(P(h,this.getFirstScaleIdForIndexAxis())),m=this._getStackIndex(this.index,this._cachedMeta.stack,s?e:void 0)+g;o=f.start+f.chunk*m+f.chunk/2,r=Math.min(l,f.chunk*f.ratio)}else o=n.getPixelForValue(this.getParsed(e)[n.axis],e),r=Math.min(l,i.min*i.ratio);return{base:o-r/2,head:o+r/2,center:o,size:r}}draw(){const e=this._cachedMeta,i=e.vScale,n=e.data,a=n.length;let s=0;for(;s<a;++s)this.getParsed(s)[i.axis]!==null&&!n[s].hidden&&n[s].draw(this._ctx)}}Z(yo,"id","bar"),Z(yo,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),Z(yo,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class vo extends Ca{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const i=this._cachedMeta,{dataset:n,data:a=[],_dataset:s}=i,l=this.chart._animationsDisabled;let{start:o,count:r}=VS(i,a,l);this._drawStart=o,this._drawCount=r,GS(i)&&(o=0,r=a.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!s._decimated,n.points=a;const c=this.resolveDatasetElementOptions(e);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(n,void 0,{animated:!l,options:c},e),this.updateElements(a,o,r,e)}updateElements(e,i,n,a){const s=a==="reset",{iScale:l,vScale:o,_stacked:r,_dataset:c}=this._cachedMeta,{sharedOptions:d,includeOptions:f}=this._getSharedOptions(i,a),h=l.axis,g=o.axis,{spanGaps:m,segment:v}=this.options,S=Is(m)?m:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||s||a==="none",x=i+n,y=e.length;let _=i>0&&this.getParsed(i-1);for(let D=0;D<y;++D){const A=e[D],j=b?A:{};if(D<i||D>=x){j.skip=!0;continue}const T=this.getParsed(D),M=ct(T[g]),L=j[h]=l.getPixelForValue(T[h],D),Y=j[g]=s||M?o.getBasePixel():o.getPixelForValue(r?this.applyStack(o,T,r):T[g],D);j.skip=isNaN(L)||isNaN(Y)||M,j.stop=D>0&&Math.abs(T[h]-_[h])>S,v&&(j.parsed=T,j.raw=c.data[D]),f&&(j.options=d||this.resolveDataElementOptions(D,A.active?"active":a)),b||this.updateElement(A,D,j,a),_=T}}getMaxOverflow(){const e=this._cachedMeta,i=e.dataset,n=i.options&&i.options.borderWidth||0,a=e.data||[];if(!a.length)return n;const s=a[0].size(this.resolveDataElementOptions(0)),l=a[a.length-1].size(this.resolveDataElementOptions(a.length-1));return Math.max(n,s,l)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}Z(vo,"id","line"),Z(vo,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),Z(vo,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});function xn(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class of{constructor(e){Z(this,"options");this.options=e||{}}static override(e){Object.assign(of.prototype,e)}init(){}formats(){return xn()}parse(){return xn()}format(){return xn()}add(){return xn()}diff(){return xn()}startOf(){return xn()}endOf(){return xn()}}var p_={_date:of};function g_(t,e,i,n){const{controller:a,data:s,_sorted:l}=t,o=a._cachedMeta.iScale,r=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null;if(o&&e===o.axis&&e!=="r"&&l&&s.length){const c=o._reversePixels?NS:Cn;if(n){if(a._sharedOptions){const d=s[0],f=typeof d.getRange=="function"&&d.getRange(e);if(f){const h=c(s,e,i-f),g=c(s,e,i+f);return{lo:h.lo,hi:g.hi}}}}else{const d=c(s,e,i);if(r){const{vScale:f}=a._cachedMeta,{_parsed:h}=t,g=h.slice(0,d.lo+1).reverse().findIndex(v=>!ct(v[f.axis]));d.lo-=Math.max(0,g);const m=h.slice(d.hi).findIndex(v=>!ct(v[f.axis]));d.hi+=Math.max(0,m)}return d}}return{lo:0,hi:s.length-1}}function Mr(t,e,i,n,a){const s=t.getSortedVisibleDatasetMetas(),l=i[e];for(let o=0,r=s.length;o<r;++o){const{index:c,data:d}=s[o],{lo:f,hi:h}=g_(s[o],e,l,a);for(let g=f;g<=h;++g){const m=d[g];m.skip||n(m,c,g)}}}function b_(t){const e=t.indexOf("x")!==-1,i=t.indexOf("y")!==-1;return function(n,a){const s=e?Math.abs(n.x-a.x):0,l=i?Math.abs(n.y-a.y):0;return Math.sqrt(Math.pow(s,2)+Math.pow(l,2))}}function Lc(t,e,i,n,a){const s=[];return!a&&!t.isPointInArea(e)||Mr(t,i,e,function(o,r,c){!a&&!tl(o,t.chartArea,0)||o.inRange(e.x,e.y,n)&&s.push({element:o,datasetIndex:r,index:c})},!0),s}function x_(t,e,i,n){let a=[];function s(l,o,r){const{startAngle:c,endAngle:d}=l.getProps(["startAngle","endAngle"],n),{angle:f}=ES(l,{x:e.x,y:e.y});Gm(f,c,d)&&a.push({element:l,datasetIndex:o,index:r})}return Mr(t,i,e,s),a}function m_(t,e,i,n,a,s){let l=[];const o=b_(i);let r=Number.POSITIVE_INFINITY;function c(d,f,h){const g=d.inRange(e.x,e.y,a);if(n&&!g)return;const m=d.getCenterPoint(a);if(!(!!s||t.isPointInArea(m))&&!g)return;const S=o(e,m);S<r?(l=[{element:d,datasetIndex:f,index:h}],r=S):S===r&&l.push({element:d,datasetIndex:f,index:h})}return Mr(t,i,e,c),l}function Nc(t,e,i,n,a,s){return!s&&!t.isPointInArea(e)?[]:i==="r"&&!n?x_(t,e,i,a):m_(t,e,i,n,a,s)}function Ip(t,e,i,n,a){const s=[],l=i==="x"?"inXRange":"inYRange";let o=!1;return Mr(t,i,e,(r,c,d)=>{r[l]&&r[l](e[i],a)&&(s.push({element:r,datasetIndex:c,index:d}),o=o||r.inRange(e.x,e.y,a))}),n&&!o?[]:s}var y_={modes:{index(t,e,i,n){const a=Sn(e,t),s=i.axis||"x",l=i.includeInvisible||!1,o=i.intersect?Lc(t,a,s,n,l):Nc(t,a,s,!1,n,l),r=[];return o.length?(t.getSortedVisibleDatasetMetas().forEach(c=>{const d=o[0].index,f=c.data[d];f&&!f.skip&&r.push({element:f,datasetIndex:c.index,index:d})}),r):[]},dataset(t,e,i,n){const a=Sn(e,t),s=i.axis||"xy",l=i.includeInvisible||!1;let o=i.intersect?Lc(t,a,s,n,l):Nc(t,a,s,!1,n,l);if(o.length>0){const r=o[0].datasetIndex,c=t.getDatasetMeta(r).data;o=[];for(let d=0;d<c.length;++d)o.push({element:c[d],datasetIndex:r,index:d})}return o},point(t,e,i,n){const a=Sn(e,t),s=i.axis||"xy",l=i.includeInvisible||!1;return Lc(t,a,s,n,l)},nearest(t,e,i,n){const a=Sn(e,t),s=i.axis||"xy",l=i.includeInvisible||!1;return Nc(t,a,s,i.intersect,n,l)},x(t,e,i,n){const a=Sn(e,t);return Ip(t,a,"x",i.intersect,n)},y(t,e,i,n){const a=Sn(e,t);return Ip(t,a,"y",i.intersect,n)}}};const hy=["left","top","right","bottom"];function ls(t,e){return t.filter(i=>i.pos===e)}function tg(t,e){return t.filter(i=>hy.indexOf(i.pos)===-1&&i.box.axis===e)}function os(t,e){return t.sort((i,n)=>{const a=e?n:i,s=e?i:n;return a.weight===s.weight?a.index-s.index:a.weight-s.weight})}function v_(t){const e=[];let i,n,a,s,l,o;for(i=0,n=(t||[]).length;i<n;++i)a=t[i],{position:s,options:{stack:l,stackWeight:o=1}}=a,e.push({index:i,box:a,pos:s,horizontal:a.isHorizontal(),weight:a.weight,stack:l&&s+l,stackWeight:o});return e}function S_(t){const e={};for(const i of t){const{stack:n,pos:a,stackWeight:s}=i;if(!n||!hy.includes(a))continue;const l=e[n]||(e[n]={count:0,placed:0,weight:0,size:0});l.count++,l.weight+=s}return e}function __(t,e){const i=S_(t),{vBoxMaxWidth:n,hBoxMaxHeight:a}=e;let s,l,o;for(s=0,l=t.length;s<l;++s){o=t[s];const{fullSize:r}=o.box,c=i[o.stack],d=c&&o.stackWeight/c.weight;o.horizontal?(o.width=d?d*n:r&&e.availableWidth,o.height=a):(o.width=n,o.height=d?d*a:r&&e.availableHeight)}return i}function k_(t){const e=v_(t),i=os(e.filter(c=>c.box.fullSize),!0),n=os(ls(e,"left"),!0),a=os(ls(e,"right")),s=os(ls(e,"top"),!0),l=os(ls(e,"bottom")),o=tg(e,"x"),r=tg(e,"y");return{fullSize:i,leftAndTop:n.concat(s),rightAndBottom:a.concat(r).concat(l).concat(o),chartArea:ls(e,"chartArea"),vertical:n.concat(a).concat(r),horizontal:s.concat(l).concat(o)}}function eg(t,e,i,n){return Math.max(t[i],e[i])+Math.max(t[n],e[n])}function py(t,e){t.top=Math.max(t.top,e.top),t.left=Math.max(t.left,e.left),t.bottom=Math.max(t.bottom,e.bottom),t.right=Math.max(t.right,e.right)}function C_(t,e,i,n){const{pos:a,box:s}=i,l=t.maxPadding;if(!et(a)){i.size&&(t[a]-=i.size);const f=n[i.stack]||{size:0,count:1};f.size=Math.max(f.size,i.horizontal?s.height:s.width),i.size=f.size/f.count,t[a]+=i.size}s.getPadding&&py(l,s.getPadding());const o=Math.max(0,e.outerWidth-eg(l,t,"left","right")),r=Math.max(0,e.outerHeight-eg(l,t,"top","bottom")),c=o!==t.w,d=r!==t.h;return t.w=o,t.h=r,i.horizontal?{same:c,other:d}:{same:d,other:c}}function D_(t){const e=t.maxPadding;function i(n){const a=Math.max(e[n]-t[n],0);return t[n]+=a,a}t.y+=i("top"),t.x+=i("left"),i("right"),i("bottom")}function w_(t,e){const i=e.maxPadding;function n(a){const s={left:0,top:0,right:0,bottom:0};return a.forEach(l=>{s[l]=Math.max(e[l],i[l])}),s}return n(t?["left","right"]:["top","bottom"])}function gs(t,e,i,n){const a=[];let s,l,o,r,c,d;for(s=0,l=t.length,c=0;s<l;++s){o=t[s],r=o.box,r.update(o.width||e.w,o.height||e.h,w_(o.horizontal,e));const{same:f,other:h}=C_(e,i,o,n);c|=f&&a.length,d=d||h,r.fullSize||a.push(o)}return c&&gs(a,e,i,n)||d}function Il(t,e,i,n,a){t.top=i,t.left=e,t.right=e+n,t.bottom=i+a,t.width=n,t.height=a}function ig(t,e,i,n){const a=i.padding;let{x:s,y:l}=e;for(const o of t){const r=o.box,c=n[o.stack]||{placed:0,weight:1},d=o.stackWeight/c.weight||1;if(o.horizontal){const f=e.w*d,h=c.size||r.height;Js(c.start)&&(l=c.start),r.fullSize?Il(r,a.left,l,i.outerWidth-a.right-a.left,h):Il(r,e.left+c.placed,l,f,h),c.start=l,c.placed+=f,l=r.bottom}else{const f=e.h*d,h=c.size||r.width;Js(c.start)&&(s=c.start),r.fullSize?Il(r,s,a.top,h,i.outerHeight-a.bottom-a.top):Il(r,s,e.top+c.placed,h,f),c.start=s,c.placed+=f,s=r.right}}e.x=s,e.y=l}var Le={addBox(t,e){t.boxes||(t.boxes=[]),e.fullSize=e.fullSize||!1,e.position=e.position||"top",e.weight=e.weight||0,e._layers=e._layers||function(){return[{z:0,draw(i){e.draw(i)}}]},t.boxes.push(e)},removeBox(t,e){const i=t.boxes?t.boxes.indexOf(e):-1;i!==-1&&t.boxes.splice(i,1)},configure(t,e,i){e.fullSize=i.fullSize,e.position=i.position,e.weight=i.weight},update(t,e,i,n){if(!t)return;const a=Ue(t.options.layout.padding),s=Math.max(e-a.width,0),l=Math.max(i-a.height,0),o=k_(t.boxes),r=o.vertical,c=o.horizontal;dt(t.boxes,v=>{typeof v.beforeLayout=="function"&&v.beforeLayout()});const d=r.reduce((v,S)=>S.box.options&&S.box.options.display===!1?v:v+1,0)||1,f=Object.freeze({outerWidth:e,outerHeight:i,padding:a,availableWidth:s,availableHeight:l,vBoxMaxWidth:s/2/d,hBoxMaxHeight:l/2}),h=Object.assign({},a);py(h,Ue(n));const g=Object.assign({maxPadding:h,w:s,h:l,x:a.left,y:a.top},a),m=__(r.concat(c),f);gs(o.fullSize,g,f,m),gs(r,g,f,m),gs(c,g,f,m)&&gs(r,g,f,m),D_(g),ig(o.leftAndTop,g,f,m),g.x+=g.w,g.y+=g.h,ig(o.rightAndBottom,g,f,m),t.chartArea={left:g.left,top:g.top,right:g.left+g.w,bottom:g.top+g.h,height:g.h,width:g.w},dt(o.chartArea,v=>{const S=v.box;Object.assign(S,t.chartArea),S.update(g.w,g.h,{left:0,top:0,right:0,bottom:0})})}};class gy{acquireContext(e,i){}releaseContext(e){return!1}addEventListener(e,i,n){}removeEventListener(e,i,n){}getDevicePixelRatio(){return 1}getMaximumSize(e,i,n,a){return i=Math.max(0,i||e.width),n=n||e.height,{width:i,height:Math.max(0,a?Math.floor(i/a):n)}}isAttached(e){return!0}updateConfig(e){}}class j_ extends gy{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const So="$chartjs",z_={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},ng=t=>t===null||t==="";function T_(t,e){const i=t.style,n=t.getAttribute("height"),a=t.getAttribute("width");if(t[So]={initial:{height:n,width:a,style:{display:i.display,height:i.height,width:i.width}}},i.display=i.display||"block",i.boxSizing=i.boxSizing||"border-box",ng(a)){const s=Yp(t,"width");s!==void 0&&(t.width=s)}if(ng(n))if(t.style.height==="")t.height=t.width/(e||2);else{const s=Yp(t,"height");s!==void 0&&(t.height=s)}return t}const by=O2?{passive:!0}:!1;function A_(t,e,i){t&&t.addEventListener(e,i,by)}function M_(t,e,i){t&&t.canvas&&t.canvas.removeEventListener(e,i,by)}function O_(t,e){const i=z_[t.type]||t.type,{x:n,y:a}=Sn(t,e);return{type:i,chart:e,native:t,x:n!==void 0?n:null,y:a!==void 0?a:null}}function lr(t,e){for(const i of t)if(i===e||i.contains(e))return!0}function R_(t,e,i){const n=t.canvas,a=new MutationObserver(s=>{let l=!1;for(const o of s)l=l||lr(o.addedNodes,n),l=l&&!lr(o.removedNodes,n);l&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}function E_(t,e,i){const n=t.canvas,a=new MutationObserver(s=>{let l=!1;for(const o of s)l=l||lr(o.removedNodes,n),l=l&&!lr(o.addedNodes,n);l&&i()});return a.observe(document,{childList:!0,subtree:!0}),a}const il=new Map;let ag=0;function xy(){const t=window.devicePixelRatio;t!==ag&&(ag=t,il.forEach((e,i)=>{i.currentDevicePixelRatio!==t&&e()}))}function B_(t,e){il.size||window.addEventListener("resize",xy),il.set(t,e)}function L_(t){il.delete(t),il.size||window.removeEventListener("resize",xy)}function N_(t,e,i){const n=t.canvas,a=n&&lf(n);if(!a)return;const s=Zm((o,r)=>{const c=a.clientWidth;i(o,r),c<a.clientWidth&&i()},window),l=new ResizeObserver(o=>{const r=o[0],c=r.contentRect.width,d=r.contentRect.height;c===0&&d===0||s(c,d)});return l.observe(a),B_(t,s),l}function Hc(t,e,i){i&&i.disconnect(),e==="resize"&&L_(t)}function H_(t,e,i){const n=t.canvas,a=Zm(s=>{t.ctx!==null&&i(O_(s,t))},t);return A_(n,e,a),a}class U_ extends gy{acquireContext(e,i){const n=e&&e.getContext&&e.getContext("2d");return n&&n.canvas===e?(T_(e,i),n):null}releaseContext(e){const i=e.canvas;if(!i[So])return!1;const n=i[So].initial;["height","width"].forEach(s=>{const l=n[s];ct(l)?i.removeAttribute(s):i.setAttribute(s,l)});const a=n.style||{};return Object.keys(a).forEach(s=>{i.style[s]=a[s]}),i.width=i.width,delete i[So],!0}addEventListener(e,i,n){this.removeEventListener(e,i);const a=e.$proxies||(e.$proxies={}),l={attach:R_,detach:E_,resize:N_}[i]||H_;a[i]=l(e,i,n)}removeEventListener(e,i){const n=e.$proxies||(e.$proxies={}),a=n[i];if(!a)return;({attach:Hc,detach:Hc,resize:Hc}[i]||M_)(e,i,a),n[i]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,i,n,a){return M2(e,i,n,a)}isAttached(e){const i=e&&lf(e);return!!(i&&i.isConnected)}}function Y_(t){return!sf()||typeof OffscreenCanvas<"u"&&t instanceof OffscreenCanvas?j_:U_}class ai{constructor(){Z(this,"x");Z(this,"y");Z(this,"active",!1);Z(this,"options");Z(this,"$animations")}tooltipPosition(e){const{x:i,y:n}=this.getProps(["x","y"],e);return{x:i,y:n}}hasValue(){return Is(this.x)&&Is(this.y)}getProps(e,i){const n=this.$animations;if(!i||!n)return this;const a={};return e.forEach(s=>{a[s]=n[s]&&n[s].active()?n[s]._to:this[s]}),a}}Z(ai,"defaults",{}),Z(ai,"defaultRoutes");function F_(t,e){const i=t.options.ticks,n=V_(t),a=Math.min(i.maxTicksLimit||n,n),s=i.major.enabled?q_(e):[],l=s.length,o=s[0],r=s[l-1],c=[];if(l>a)return X_(e,c,s,l/a),c;const d=G_(s,e,a);if(l>0){let f,h;const g=l>1?Math.round((r-o)/(l-1)):null;for(to(e,c,d,ct(g)?0:o-g,o),f=0,h=l-1;f<h;f++)to(e,c,d,s[f],s[f+1]);return to(e,c,d,r,ct(g)?e.length:r+g),c}return to(e,c,d),c}function V_(t){const e=t.options.offset,i=t._tickSize(),n=t._length/i+(e?0:1),a=t._maxLength/i;return Math.floor(Math.min(n,a))}function G_(t,e,i){const n=Q_(t),a=e.length/i;if(!n)return Math.max(a,1);const s=TS(n);for(let l=0,o=s.length-1;l<o;l++){const r=s[l];if(r>a)return r}return Math.max(a,1)}function q_(t){const e=[];let i,n;for(i=0,n=t.length;i<n;i++)t[i].major&&e.push(i);return e}function X_(t,e,i,n){let a=0,s=i[0],l;for(n=Math.ceil(n),l=0;l<t.length;l++)l===s&&(e.push(t[l]),a++,s=i[a*n])}function to(t,e,i,n,a){const s=P(n,0),l=Math.min(P(a,t.length),t.length);let o=0,r,c,d;for(i=Math.ceil(i),a&&(r=a-n,i=r/Math.floor(r/i)),d=s;d<0;)o++,d=Math.round(s+o*i);for(c=Math.max(s,0);c<l;c++)c===d&&(e.push(t[c]),o++,d=Math.round(s+o*i))}function Q_(t){const e=t.length;let i,n;if(e<2)return!1;for(n=t[0],i=1;i<e;++i)if(t[i]-t[i-1]!==n)return!1;return n}const Z_=t=>t==="left"?"right":t==="right"?"left":t,sg=(t,e,i)=>e==="top"||e==="left"?t[e]+i:t[e]-i,lg=(t,e)=>Math.min(e||t,t);function og(t,e){const i=[],n=t.length/e,a=t.length;let s=0;for(;s<a;s+=n)i.push(t[Math.floor(s)]);return i}function W_(t,e,i){const n=t.ticks.length,a=Math.min(e,n-1),s=t._startPixel,l=t._endPixel,o=1e-6;let r=t.getPixelForTick(a),c;if(!(i&&(n===1?c=Math.max(r-s,l-r):e===0?c=(t.getPixelForTick(1)-r)/2:c=(r-t.getPixelForTick(a-1))/2,r+=a<e?c:-c,r<s-o||r>l+o)))return r}function K_(t,e){dt(t,i=>{const n=i.gc,a=n.length/2;let s;if(a>e){for(s=0;s<a;++s)delete i.data[n[s]];n.splice(0,a)}})}function rs(t){return t.drawTicks?t.tickLength:0}function rg(t,e){if(!t.display)return 0;const i=te(t.font,e),n=Ue(t.padding);return(Lt(t.text)?t.text.length:1)*i.lineHeight+n.height}function $_(t,e){return Gn(t,{scale:e,type:"scale"})}function P_(t,e,i){return Gn(t,{tick:i,index:e,type:"tick"})}function J_(t,e,i){let n=Id(t);return(i&&e!=="right"||!i&&e==="right")&&(n=Z_(n)),n}function I_(t,e,i,n){const{top:a,left:s,bottom:l,right:o,chart:r}=t,{chartArea:c,scales:d}=r;let f=0,h,g,m;const v=l-a,S=o-s;if(t.isHorizontal()){if(g=Pt(n,s,o),et(i)){const b=Object.keys(i)[0],x=i[b];m=d[b].getPixelForValue(x)+v-e}else i==="center"?m=(c.bottom+c.top)/2+v-e:m=sg(t,i,e);h=o-s}else{if(et(i)){const b=Object.keys(i)[0],x=i[b];g=d[b].getPixelForValue(x)-S+e}else i==="center"?g=(c.left+c.right)/2-S+e:g=sg(t,i,e);m=Pt(n,l,a),f=i==="left"?-Ve:Ve}return{titleX:g,titleY:m,maxWidth:h,rotation:f}}class Qa extends ai{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,i){return e}getUserBounds(){let{_userMin:e,_userMax:i,_suggestedMin:n,_suggestedMax:a}=this;return e=We(e,Number.POSITIVE_INFINITY),i=We(i,Number.NEGATIVE_INFINITY),n=We(n,Number.POSITIVE_INFINITY),a=We(a,Number.NEGATIVE_INFINITY),{min:We(e,n),max:We(i,a),minDefined:ee(e),maxDefined:ee(i)}}getMinMax(e){let{min:i,max:n,minDefined:a,maxDefined:s}=this.getUserBounds(),l;if(a&&s)return{min:i,max:n};const o=this.getMatchingVisibleMetas();for(let r=0,c=o.length;r<c;++r)l=o[r].controller.getMinMax(this,e),a||(i=Math.min(i,l.min)),s||(n=Math.max(n,l.max));return i=s&&i>n?n:i,n=a&&i>n?i:n,{min:We(i,We(n,i)),max:We(n,We(i,n))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(e=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){vt(this.options.beforeUpdate,[this])}update(e,i,n){const{beginAtZero:a,grace:s,ticks:l}=this.options,o=l.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=i,this._margins=n=Object.assign({left:0,right:0,top:0,bottom:0},n),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+n.left+n.right:this.height+n.top+n.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=c2(this,s,a),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const r=o<this.ticks.length;this._convertTicksToLabels(r?og(this.ticks,o):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),l.display&&(l.autoSkip||l.source==="auto")&&(this.ticks=F_(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),r&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e=this.options.reverse,i,n;this.isHorizontal()?(i=this.left,n=this.right):(i=this.top,n=this.bottom,e=!e),this._startPixel=i,this._endPixel=n,this._reversePixels=e,this._length=n-i,this._alignToPixels=this.options.alignToPixels}afterUpdate(){vt(this.options.afterUpdate,[this])}beforeSetDimensions(){vt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){vt(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),vt(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){vt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const i=this.options.ticks;let n,a,s;for(n=0,a=e.length;n<a;n++)s=e[n],s.label=vt(i.callback,[s.value,n,e],this)}afterTickToLabelConversion(){vt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){vt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,i=e.ticks,n=lg(this.ticks.length,e.ticks.maxTicksLimit),a=i.minRotation||0,s=i.maxRotation;let l=a,o,r,c;if(!this._isVisible()||!i.display||a>=s||n<=1||!this.isHorizontal()){this.labelRotation=a;return}const d=this._getLabelSizes(),f=d.widest.width,h=d.highest.height,g=_e(this.chart.width-f,0,this.maxWidth);o=e.offset?this.maxWidth/n:g/(n-1),f+6>o&&(o=g/(n-(e.offset?.5:1)),r=this.maxHeight-rs(e.grid)-i.padding-rg(e.title,this.chart.options.font),c=Math.sqrt(f*f+h*h),l=RS(Math.min(Math.asin(_e((d.highest.height+6)/o,-1,1)),Math.asin(_e(r/c,-1,1))-Math.asin(_e(h/c,-1,1)))),l=Math.max(a,Math.min(s,l))),this.labelRotation=l}afterCalculateLabelRotation(){vt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){vt(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:i,options:{ticks:n,title:a,grid:s}}=this,l=this._isVisible(),o=this.isHorizontal();if(l){const r=rg(a,i.options.font);if(o?(e.width=this.maxWidth,e.height=rs(s)+r):(e.height=this.maxHeight,e.width=rs(s)+r),n.display&&this.ticks.length){const{first:c,last:d,widest:f,highest:h}=this._getLabelSizes(),g=n.padding*2,m=kn(this.labelRotation),v=Math.cos(m),S=Math.sin(m);if(o){const b=n.mirror?0:S*f.width+v*h.height;e.height=Math.min(this.maxHeight,e.height+b+g)}else{const b=n.mirror?0:v*f.width+S*h.height;e.width=Math.min(this.maxWidth,e.width+b+g)}this._calculatePadding(c,d,S,v)}}this._handleMargins(),o?(this.width=this._length=i.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=i.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,i,n,a){const{ticks:{align:s,padding:l},position:o}=this.options,r=this.labelRotation!==0,c=o!=="top"&&this.axis==="x";if(this.isHorizontal()){const d=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let h=0,g=0;r?c?(h=a*e.width,g=n*i.height):(h=n*e.height,g=a*i.width):s==="start"?g=i.width:s==="end"?h=e.width:s!=="inner"&&(h=e.width/2,g=i.width/2),this.paddingLeft=Math.max((h-d+l)*this.width/(this.width-d),0),this.paddingRight=Math.max((g-f+l)*this.width/(this.width-f),0)}else{let d=i.height/2,f=e.height/2;s==="start"?(d=0,f=e.height):s==="end"&&(d=i.height,f=0),this.paddingTop=d+l,this.paddingBottom=f+l}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){vt(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:i}=this.options;return i==="top"||i==="bottom"||e==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){this.beforeTickToLabelConversion(),this.generateTickLabels(e);let i,n;for(i=0,n=e.length;i<n;i++)ct(e[i].label)&&(e.splice(i,1),n--,i--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const i=this.options.ticks.sampleSize;let n=this.ticks;i<n.length&&(n=og(n,i)),this._labelSizes=e=this._computeLabelSizes(n,n.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,i,n){const{ctx:a,_longestTextCache:s}=this,l=[],o=[],r=Math.floor(i/lg(i,n));let c=0,d=0,f,h,g,m,v,S,b,x,y,_,D;for(f=0;f<i;f+=r){if(m=e[f].label,v=this._resolveTickFontOptions(f),a.font=S=v.string,b=s[S]=s[S]||{data:{},gc:[]},x=v.lineHeight,y=_=0,!ct(m)&&!Lt(m))y=Bp(a,b.data,b.gc,y,m),_=x;else if(Lt(m))for(h=0,g=m.length;h<g;++h)D=m[h],!ct(D)&&!Lt(D)&&(y=Bp(a,b.data,b.gc,y,D),_+=x);l.push(y),o.push(_),c=Math.max(y,c),d=Math.max(_,d)}K_(s,i);const A=l.indexOf(c),j=o.indexOf(d),T=M=>({width:l[M]||0,height:o[M]||0});return{first:T(0),last:T(i-1),widest:T(A),highest:T(j),widths:l,heights:o}}getLabelForValue(e){return e}getPixelForValue(e,i){return NaN}getValueForPixel(e){}getPixelForTick(e){const i=this.ticks;return e<0||e>i.length-1?null:this.getPixelForValue(i[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const i=this._startPixel+e*this._length;return LS(this._alignToPixels?bn(this.chart,i,0):i)}getDecimalForPixel(e){const i=(e-this._startPixel)/this._length;return this._reversePixels?1-i:i}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:i}=this;return e<0&&i<0?i:e>0&&i>0?e:0}getContext(e){const i=this.ticks||[];if(e>=0&&e<i.length){const n=i[e];return n.$context||(n.$context=P_(this.getContext(),e,n))}return this.$context||(this.$context=$_(this.chart.getContext(),this))}_tickSize(){const e=this.options.ticks,i=kn(this.labelRotation),n=Math.abs(Math.cos(i)),a=Math.abs(Math.sin(i)),s=this._getLabelSizes(),l=e.autoSkipPadding||0,o=s?s.widest.width+l:0,r=s?s.highest.height+l:0;return this.isHorizontal()?r*n>o*a?o/n:r/a:r*a<o*n?r/n:o/a}_isVisible(){const e=this.options.display;return e!=="auto"?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const i=this.axis,n=this.chart,a=this.options,{grid:s,position:l,border:o}=a,r=s.offset,c=this.isHorizontal(),f=this.ticks.length+(r?1:0),h=rs(s),g=[],m=o.setContext(this.getContext()),v=m.display?m.width:0,S=v/2,b=function(H){return bn(n,H,v)};let x,y,_,D,A,j,T,M,L,Y,G,mt;if(l==="top")x=b(this.bottom),j=this.bottom-h,M=x-S,Y=b(e.top)+S,mt=e.bottom;else if(l==="bottom")x=b(this.top),Y=e.top,mt=b(e.bottom)-S,j=x+S,M=this.top+h;else if(l==="left")x=b(this.right),A=this.right-h,T=x-S,L=b(e.left)+S,G=e.right;else if(l==="right")x=b(this.left),L=e.left,G=b(e.right)-S,A=x+S,T=this.left+h;else if(i==="x"){if(l==="center")x=b((e.top+e.bottom)/2+.5);else if(et(l)){const H=Object.keys(l)[0],V=l[H];x=b(this.chart.scales[H].getPixelForValue(V))}Y=e.top,mt=e.bottom,j=x+S,M=j+h}else if(i==="y"){if(l==="center")x=b((e.left+e.right)/2);else if(et(l)){const H=Object.keys(l)[0],V=l[H];x=b(this.chart.scales[H].getPixelForValue(V))}A=x-S,T=A-h,L=e.left,G=e.right}const wt=P(a.ticks.maxTicksLimit,f),ot=Math.max(1,Math.ceil(f/wt));for(y=0;y<f;y+=ot){const H=this.getContext(y),V=s.setContext(H),U=o.setContext(H),rt=V.lineWidth,at=V.color,At=U.dash||[],Mt=U.dashOffset,le=V.tickWidth,R=V.tickColor,N=V.tickBorderDash||[],F=V.tickBorderDashOffset;_=W_(this,y,r),_!==void 0&&(D=bn(n,_,rt),c?A=T=L=G=D:j=M=Y=mt=D,g.push({tx1:A,ty1:j,tx2:T,ty2:M,x1:L,y1:Y,x2:G,y2:mt,width:rt,color:at,borderDash:At,borderDashOffset:Mt,tickWidth:le,tickColor:R,tickBorderDash:N,tickBorderDashOffset:F}))}return this._ticksLength=f,this._borderValue=x,g}_computeLabelItems(e){const i=this.axis,n=this.options,{position:a,ticks:s}=n,l=this.isHorizontal(),o=this.ticks,{align:r,crossAlign:c,padding:d,mirror:f}=s,h=rs(n.grid),g=h+d,m=f?-d:g,v=-kn(this.labelRotation),S=[];let b,x,y,_,D,A,j,T,M,L,Y,G,mt="middle";if(a==="top")A=this.bottom-m,j=this._getXAxisLabelAlignment();else if(a==="bottom")A=this.top+m,j=this._getXAxisLabelAlignment();else if(a==="left"){const ot=this._getYAxisLabelAlignment(h);j=ot.textAlign,D=ot.x}else if(a==="right"){const ot=this._getYAxisLabelAlignment(h);j=ot.textAlign,D=ot.x}else if(i==="x"){if(a==="center")A=(e.top+e.bottom)/2+g;else if(et(a)){const ot=Object.keys(a)[0],H=a[ot];A=this.chart.scales[ot].getPixelForValue(H)+g}j=this._getXAxisLabelAlignment()}else if(i==="y"){if(a==="center")D=(e.left+e.right)/2-g;else if(et(a)){const ot=Object.keys(a)[0],H=a[ot];D=this.chart.scales[ot].getPixelForValue(H)}j=this._getYAxisLabelAlignment(h).textAlign}i==="y"&&(r==="start"?mt="top":r==="end"&&(mt="bottom"));const wt=this._getLabelSizes();for(b=0,x=o.length;b<x;++b){y=o[b],_=y.label;const ot=s.setContext(this.getContext(b));T=this.getPixelForTick(b)+s.labelOffset,M=this._resolveTickFontOptions(b),L=M.lineHeight,Y=Lt(_)?_.length:1;const H=Y/2,V=ot.color,U=ot.textStrokeColor,rt=ot.textStrokeWidth;let at=j;l?(D=T,j==="inner"&&(b===x-1?at=this.options.reverse?"left":"right":b===0?at=this.options.reverse?"right":"left":at="center"),a==="top"?c==="near"||v!==0?G=-Y*L+L/2:c==="center"?G=-wt.highest.height/2-H*L+L:G=-wt.highest.height+L/2:c==="near"||v!==0?G=L/2:c==="center"?G=wt.highest.height/2-H*L:G=wt.highest.height-Y*L,f&&(G*=-1),v!==0&&!ot.showLabelBackdrop&&(D+=L/2*Math.sin(v))):(A=T,G=(1-Y)*L/2);let At;if(ot.showLabelBackdrop){const Mt=Ue(ot.backdropPadding),le=wt.heights[b],R=wt.widths[b];let N=G-Mt.top,F=0-Mt.left;switch(mt){case"middle":N-=le/2;break;case"bottom":N-=le;break}switch(j){case"center":F-=R/2;break;case"right":F-=R;break;case"inner":b===x-1?F-=R:b>0&&(F-=R/2);break}At={left:F,top:N,width:R+Mt.width,height:le+Mt.height,color:ot.backdropColor}}S.push({label:_,font:M,textOffset:G,options:{rotation:v,color:V,strokeColor:U,strokeWidth:rt,textAlign:at,textBaseline:mt,translation:[D,A],backdrop:At}})}return S}_getXAxisLabelAlignment(){const{position:e,ticks:i}=this.options;if(-kn(this.labelRotation))return e==="top"?"left":"right";let a="center";return i.align==="start"?a="left":i.align==="end"?a="right":i.align==="inner"&&(a="inner"),a}_getYAxisLabelAlignment(e){const{position:i,ticks:{crossAlign:n,mirror:a,padding:s}}=this.options,l=this._getLabelSizes(),o=e+s,r=l.widest.width;let c,d;return i==="left"?a?(d=this.right+s,n==="near"?c="left":n==="center"?(c="center",d+=r/2):(c="right",d+=r)):(d=this.right-o,n==="near"?c="right":n==="center"?(c="center",d-=r/2):(c="left",d=this.left)):i==="right"?a?(d=this.left+s,n==="near"?c="right":n==="center"?(c="center",d-=r/2):(c="left",d-=r)):(d=this.left+o,n==="near"?c="left":n==="center"?(c="center",d+=r/2):(c="right",d=this.right)):c="right",{textAlign:c,x:d}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,i=this.options.position;if(i==="left"||i==="right")return{top:0,left:this.left,bottom:e.height,right:this.right};if(i==="top"||i==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:e.width}}drawBackground(){const{ctx:e,options:{backgroundColor:i},left:n,top:a,width:s,height:l}=this;i&&(e.save(),e.fillStyle=i,e.fillRect(n,a,s,l),e.restore())}getLineWidthForValue(e){const i=this.options.grid;if(!this._isVisible()||!i.display)return 0;const a=this.ticks.findIndex(s=>s.value===e);return a>=0?i.setContext(this.getContext(a)).lineWidth:0}drawGrid(e){const i=this.options.grid,n=this.ctx,a=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let s,l;const o=(r,c,d)=>{!d.width||!d.color||(n.save(),n.lineWidth=d.width,n.strokeStyle=d.color,n.setLineDash(d.borderDash||[]),n.lineDashOffset=d.borderDashOffset,n.beginPath(),n.moveTo(r.x,r.y),n.lineTo(c.x,c.y),n.stroke(),n.restore())};if(i.display)for(s=0,l=a.length;s<l;++s){const r=a[s];i.drawOnChartArea&&o({x:r.x1,y:r.y1},{x:r.x2,y:r.y2},r),i.drawTicks&&o({x:r.tx1,y:r.ty1},{x:r.tx2,y:r.ty2},{color:r.tickColor,width:r.tickWidth,borderDash:r.tickBorderDash,borderDashOffset:r.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:i,options:{border:n,grid:a}}=this,s=n.setContext(this.getContext()),l=n.display?s.width:0;if(!l)return;const o=a.setContext(this.getContext(0)).lineWidth,r=this._borderValue;let c,d,f,h;this.isHorizontal()?(c=bn(e,this.left,l)-l/2,d=bn(e,this.right,o)+o/2,f=h=r):(f=bn(e,this.top,l)-l/2,h=bn(e,this.bottom,o)+o/2,c=d=r),i.save(),i.lineWidth=s.width,i.strokeStyle=s.color,i.beginPath(),i.moveTo(c,f),i.lineTo(d,h),i.stroke(),i.restore()}drawLabels(e){if(!this.options.ticks.display)return;const n=this.ctx,a=this._computeLabelArea();a&&zr(n,a);const s=this.getLabelItems(e);for(const l of s){const o=l.options,r=l.font,c=l.label,d=l.textOffset;el(n,c,0,d,r,o)}a&&Tr(n)}drawTitle(){const{ctx:e,options:{position:i,title:n,reverse:a}}=this;if(!n.display)return;const s=te(n.font),l=Ue(n.padding),o=n.align;let r=s.lineHeight/2;i==="bottom"||i==="center"||et(i)?(r+=l.bottom,Lt(n.text)&&(r+=s.lineHeight*(n.text.length-1))):r+=l.top;const{titleX:c,titleY:d,maxWidth:f,rotation:h}=I_(this,r,i,o);el(e,n.text,0,0,s,{color:n.color,maxWidth:f,rotation:h,textAlign:J_(o,i,a),textBaseline:"middle",translation:[c,d]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,i=e.ticks&&e.ticks.z||0,n=P(e.grid&&e.grid.z,-1),a=P(e.border&&e.border.z,0);return!this._isVisible()||this.draw!==Qa.prototype.draw?[{z:i,draw:s=>{this.draw(s)}}]:[{z:n,draw:s=>{this.drawBackground(),this.drawGrid(s),this.drawTitle()}},{z:a,draw:()=>{this.drawBorder()}},{z:i,draw:s=>{this.drawLabels(s)}}]}getMatchingVisibleMetas(e){const i=this.chart.getSortedVisibleDatasetMetas(),n=this.axis+"AxisID",a=[];let s,l;for(s=0,l=i.length;s<l;++s){const o=i[s];o[n]===this.id&&(!e||o.type===e)&&a.push(o)}return a}_resolveTickFontOptions(e){const i=this.options.ticks.setContext(this.getContext(e));return te(i.font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class eo{constructor(e,i,n){this.type=e,this.scope=i,this.override=n,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const i=Object.getPrototypeOf(e);let n;ik(i)&&(n=this.register(i));const a=this.items,s=e.id,l=this.scope+"."+s;if(!s)throw new Error("class does not have id: "+e);return s in a||(a[s]=e,tk(e,l,n),this.override&&Et.override(e.id,e.overrides)),l}get(e){return this.items[e]}unregister(e){const i=this.items,n=e.id,a=this.scope;n in i&&delete i[n],a&&n in Et[a]&&(delete Et[a][n],this.override&&delete Hn[n])}}function tk(t,e,i){const n=Ps(Object.create(null),[i?Et.get(i):{},Et.get(e),t.defaults]);Et.set(e,n),t.defaultRoutes&&ek(e,t.defaultRoutes),t.descriptors&&Et.describe(e,t.descriptors)}function ek(t,e){Object.keys(e).forEach(i=>{const n=i.split("."),a=n.pop(),s=[t].concat(n).join("."),l=e[i].split("."),o=l.pop(),r=l.join(".");Et.route(s,a,r,o)})}function ik(t){return"id"in t&&"defaults"in t}class nk{constructor(){this.controllers=new eo(Ca,"datasets",!0),this.elements=new eo(ai,"elements"),this.plugins=new eo(Object,"plugins"),this.scales=new eo(Qa,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...e){this._each("register",e)}remove(...e){this._each("unregister",e)}addControllers(...e){this._each("register",e,this.controllers)}addElements(...e){this._each("register",e,this.elements)}addPlugins(...e){this._each("register",e,this.plugins)}addScales(...e){this._each("register",e,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(...e){this._each("unregister",e,this.controllers)}removeElements(...e){this._each("unregister",e,this.elements)}removePlugins(...e){this._each("unregister",e,this.plugins)}removeScales(...e){this._each("unregister",e,this.scales)}_each(e,i,n){[...i].forEach(a=>{const s=n||this._getRegistryForType(a);n||s.isForType(a)||s===this.plugins&&a.id?this._exec(e,s,a):dt(a,l=>{const o=n||this._getRegistryForType(l);this._exec(e,o,l)})})}_exec(e,i,n){const a=Pd(e);vt(n["before"+a],[],n),i[e](n),vt(n["after"+a],[],n)}_getRegistryForType(e){for(let i=0;i<this._typedRegistries.length;i++){const n=this._typedRegistries[i];if(n.isForType(e))return n}return this.plugins}_get(e,i,n){const a=i.get(e);if(a===void 0)throw new Error('"'+e+'" is not a registered '+n+".");return a}}var $e=new nk;class ak{constructor(){this._init=[]}notify(e,i,n,a){i==="beforeInit"&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const s=a?this._descriptors(e).filter(a):this._descriptors(e),l=this._notify(s,e,i,n);return i==="afterDestroy"&&(this._notify(s,e,"stop"),this._notify(this._init,e,"uninstall")),l}_notify(e,i,n,a){a=a||{};for(const s of e){const l=s.plugin,o=l[n],r=[i,a,s.options];if(vt(o,r,l)===!1&&a.cancelable)return!1}return!0}invalidate(){ct(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const i=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),i}_createDescriptors(e,i){const n=e&&e.config,a=P(n.options&&n.options.plugins,{}),s=sk(n);return a===!1&&!i?[]:ok(e,s,a,i)}_notifyStateChanges(e){const i=this._oldCache||[],n=this._cache,a=(s,l)=>s.filter(o=>!l.some(r=>o.plugin.id===r.plugin.id));this._notify(a(i,n),e,"stop"),this._notify(a(n,i),e,"start")}}function sk(t){const e={},i=[],n=Object.keys($e.plugins.items);for(let s=0;s<n.length;s++)i.push($e.getPlugin(n[s]));const a=t.plugins||[];for(let s=0;s<a.length;s++){const l=a[s];i.indexOf(l)===-1&&(i.push(l),e[l.id]=!0)}return{plugins:i,localIds:e}}function lk(t,e){return!e&&t===!1?null:t===!0?{}:t}function ok(t,{plugins:e,localIds:i},n,a){const s=[],l=t.getContext();for(const o of e){const r=o.id,c=lk(n[r],a);c!==null&&s.push({plugin:o,options:rk(t.config,{plugin:o,local:i[r]},c,l)})}return s}function rk(t,{plugin:e,local:i},n,a){const s=t.pluginScopeKeys(e),l=t.getOptionScopes(n,s);return i&&e.defaults&&l.push(e.defaults),t.createResolver(l,a,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function qu(t,e){const i=Et.datasets[t]||{};return((e.datasets||{})[t]||{}).indexAxis||e.indexAxis||i.indexAxis||"x"}function ck(t,e){let i=t;return t==="_index_"?i=e:t==="_value_"&&(i=e==="x"?"y":"x"),i}function uk(t,e){return t===e?"_index_":"_value_"}function cg(t){if(t==="x"||t==="y"||t==="r")return t}function dk(t){if(t==="top"||t==="bottom")return"x";if(t==="left"||t==="right")return"y"}function Xu(t,...e){if(cg(t))return t;for(const i of e){const n=i.axis||dk(i.position)||t.length>1&&cg(t[0].toLowerCase());if(n)return n}throw new Error(`Cannot determine type of '${t}' axis. Please provide 'axis' or 'position' option.`)}function ug(t,e,i){if(i[e+"AxisID"]===t)return{axis:e}}function fk(t,e){if(e.data&&e.data.datasets){const i=e.data.datasets.filter(n=>n.xAxisID===t||n.yAxisID===t);if(i.length)return ug(t,"x",i[0])||ug(t,"y",i[0])}return{}}function hk(t,e){const i=Hn[t.type]||{scales:{}},n=e.scales||{},a=qu(t.type,e),s=Object.create(null);return Object.keys(n).forEach(l=>{const o=n[l];if(!et(o))return console.error(`Invalid scale configuration for scale: ${l}`);if(o._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${l}`);const r=Xu(l,o,fk(l,t),Et.scales[o.type]),c=uk(r,a),d=i.scales||{};s[l]=Os(Object.create(null),[{axis:r},o,d[r],d[c]])}),t.data.datasets.forEach(l=>{const o=l.type||t.type,r=l.indexAxis||qu(o,e),d=(Hn[o]||{}).scales||{};Object.keys(d).forEach(f=>{const h=ck(f,r),g=l[h+"AxisID"]||h;s[g]=s[g]||Object.create(null),Os(s[g],[{axis:h},n[g],d[f]])})}),Object.keys(s).forEach(l=>{const o=s[l];Os(o,[Et.scales[o.type],Et.scale])}),s}function my(t){const e=t.options||(t.options={});e.plugins=P(e.plugins,{}),e.scales=hk(t,e)}function yy(t){return t=t||{},t.datasets=t.datasets||[],t.labels=t.labels||[],t}function pk(t){return t=t||{},t.data=yy(t.data),my(t),t}const dg=new Map,vy=new Set;function io(t,e){let i=dg.get(t);return i||(i=e(),dg.set(t,i),vy.add(i)),i}const cs=(t,e,i)=>{const n=La(e,i);n!==void 0&&t.add(n)};class gk{constructor(e){this._config=pk(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=yy(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),my(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return io(e,()=>[[`datasets.${e}`,""]])}datasetAnimationScopeKeys(e,i){return io(`${e}.transition.${i}`,()=>[[`datasets.${e}.transitions.${i}`,`transitions.${i}`],[`datasets.${e}`,""]])}datasetElementScopeKeys(e,i){return io(`${e}-${i}`,()=>[[`datasets.${e}.elements.${i}`,`datasets.${e}`,`elements.${i}`,""]])}pluginScopeKeys(e){const i=e.id,n=this.type;return io(`${n}-plugin-${i}`,()=>[[`plugins.${i}`,...e.additionalOptionScopes||[]]])}_cachedScopes(e,i){const n=this._scopeCache;let a=n.get(e);return(!a||i)&&(a=new Map,n.set(e,a)),a}getOptionScopes(e,i,n){const{options:a,type:s}=this,l=this._cachedScopes(e,n),o=l.get(i);if(o)return o;const r=new Set;i.forEach(d=>{e&&(r.add(e),d.forEach(f=>cs(r,e,f))),d.forEach(f=>cs(r,a,f)),d.forEach(f=>cs(r,Hn[s]||{},f)),d.forEach(f=>cs(r,Et,f)),d.forEach(f=>cs(r,Vu,f))});const c=Array.from(r);return c.length===0&&c.push(Object.create(null)),vy.has(i)&&l.set(i,c),c}chartOptionScopes(){const{options:e,type:i}=this;return[e,Hn[i]||{},Et.datasets[i]||{},{type:i},Et,Vu]}resolveNamedOptions(e,i,n,a=[""]){const s={$shared:!0},{resolver:l,subPrefixes:o}=fg(this._resolverCache,e,a);let r=l;if(xk(l,i)){s.$shared=!1,n=cn(n)?n():n;const c=this.createResolver(e,n,o);r=Na(l,n,c)}for(const c of i)s[c]=r[c];return s}createResolver(e,i,n=[""],a){const{resolver:s}=fg(this._resolverCache,e,n);return et(i)?Na(s,i,void 0,a):s}}function fg(t,e,i){let n=t.get(e);n||(n=new Map,t.set(e,n));const a=i.join();let s=n.get(a);return s||(s={resolver:ef(e,i),subPrefixes:i.filter(o=>!o.toLowerCase().includes("hover"))},n.set(a,s)),s}const bk=t=>et(t)&&Object.getOwnPropertyNames(t).some(e=>cn(t[e]));function xk(t,e){const{isScriptable:i,isIndexable:n}=Im(t);for(const a of e){const s=i(a),l=n(a),o=(l||s)&&t[a];if(s&&(cn(o)||bk(o))||l&&Lt(o))return!0}return!1}var mk="4.5.0";const yk=["top","bottom","left","right","chartArea"];function hg(t,e){return t==="top"||t==="bottom"||yk.indexOf(t)===-1&&e==="x"}function pg(t,e){return function(i,n){return i[t]===n[t]?i[e]-n[e]:i[t]-n[t]}}function gg(t){const e=t.chart,i=e.options.animation;e.notifyPlugins("afterRender"),vt(i&&i.onComplete,[t],e)}function vk(t){const e=t.chart,i=e.options.animation;vt(i&&i.onProgress,[t],e)}function Sy(t){return sf()&&typeof t=="string"?t=document.getElementById(t):t&&t.length&&(t=t[0]),t&&t.canvas&&(t=t.canvas),t}const _o={},bg=t=>{const e=Sy(t);return Object.values(_o).filter(i=>i.canvas===e).pop()};function Sk(t,e,i){const n=Object.keys(t);for(const a of n){const s=+a;if(s>=e){const l=t[a];delete t[a],(i>0||s>e)&&(t[s+i]=l)}}}function _k(t,e,i,n){return!i||t.type==="mouseout"?null:n?e:t}var Ei;let vl=(Ei=class{static register(...e){$e.add(...e),xg()}static unregister(...e){$e.remove(...e),xg()}constructor(e,i){const n=this.config=new gk(i),a=Sy(e),s=bg(a);if(s)throw new Error("Canvas is already in use. Chart with ID '"+s.id+"' must be destroyed before the canvas with ID '"+s.canvas.id+"' can be reused.");const l=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||Y_(a)),this.platform.updateConfig(n);const o=this.platform.acquireContext(a,l.aspectRatio),r=o&&o.canvas,c=r&&r.height,d=r&&r.width;if(this.id=vS(),this.ctx=o,this.canvas=r,this.width=d,this.height=c,this._options=l,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new ak,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=YS(f=>this.update(f),l.resizeDelay||0),this._dataChanges=[],_o[this.id]=this,!o||!r){console.error("Failed to create chart: can't acquire context from the given item");return}gi.listen(this,"complete",gg),gi.listen(this,"progress",vk),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:i},width:n,height:a,_aspectRatio:s}=this;return ct(e)?i&&s?s:a?n/a:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return $e}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Up(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Lp(this.canvas,this.ctx),this}stop(){return gi.stop(this),this}resize(e,i){gi.running(this)?this._resizeBeforeDraw={width:e,height:i}:this._resize(e,i)}_resize(e,i){const n=this.options,a=this.canvas,s=n.maintainAspectRatio&&this.aspectRatio,l=this.platform.getMaximumSize(a,e,i,s),o=n.devicePixelRatio||this.platform.getDevicePixelRatio(),r=this.width?"resize":"attach";this.width=l.width,this.height=l.height,this._aspectRatio=this.aspectRatio,Up(this,o,!0)&&(this.notifyPlugins("resize",{size:l}),vt(n.onResize,[this,l],this),this.attached&&this._doResize(r)&&this.render())}ensureScalesHaveIDs(){const i=this.options.scales||{};dt(i,(n,a)=>{n.id=a})}buildOrUpdateScales(){const e=this.options,i=e.scales,n=this.scales,a=Object.keys(n).reduce((l,o)=>(l[o]=!1,l),{});let s=[];i&&(s=s.concat(Object.keys(i).map(l=>{const o=i[l],r=Xu(l,o),c=r==="r",d=r==="x";return{options:o,dposition:c?"chartArea":d?"bottom":"left",dtype:c?"radialLinear":d?"category":"linear"}}))),dt(s,l=>{const o=l.options,r=o.id,c=Xu(r,o),d=P(o.type,l.dtype);(o.position===void 0||hg(o.position,c)!==hg(l.dposition))&&(o.position=l.dposition),a[r]=!0;let f=null;if(r in n&&n[r].type===d)f=n[r];else{const h=$e.getScale(d);f=new h({id:r,type:d,ctx:this.ctx,chart:this}),n[f.id]=f}f.init(o,e)}),dt(a,(l,o)=>{l||delete n[o]}),dt(n,l=>{Le.configure(this,l,l.options),Le.addBox(this,l)})}_updateMetasets(){const e=this._metasets,i=this.data.datasets.length,n=e.length;if(e.sort((a,s)=>a.index-s.index),n>i){for(let a=i;a<n;++a)this._destroyDatasetMeta(a);e.splice(i,n-i)}this._sortedMetasets=e.slice(0).sort(pg("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:i}}=this;e.length>i.length&&delete this._stacks,e.forEach((n,a)=>{i.filter(s=>s===n._dataset).length===0&&this._destroyDatasetMeta(a)})}buildOrUpdateControllers(){const e=[],i=this.data.datasets;let n,a;for(this._removeUnreferencedMetasets(),n=0,a=i.length;n<a;n++){const s=i[n];let l=this.getDatasetMeta(n);const o=s.type||this.config.type;if(l.type&&l.type!==o&&(this._destroyDatasetMeta(n),l=this.getDatasetMeta(n)),l.type=o,l.indexAxis=s.indexAxis||qu(o,this.options),l.order=s.order||0,l.index=n,l.label=""+s.label,l.visible=this.isDatasetVisible(n),l.controller)l.controller.updateIndex(n),l.controller.linkScales();else{const r=$e.getController(o),{datasetElementType:c,dataElementType:d}=Et.datasets[o];Object.assign(r,{dataElementType:$e.getElement(d),datasetElementType:c&&$e.getElement(c)}),l.controller=new r(this,n),e.push(l.controller)}}return this._updateMetasets(),e}_resetElements(){dt(this.data.datasets,(e,i)=>{this.getDatasetMeta(i).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const i=this.config;i.update();const n=this._options=i.createResolver(i.chartOptionScopes(),this.getContext()),a=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0})===!1)return;const s=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let l=0;for(let c=0,d=this.data.datasets.length;c<d;c++){const{controller:f}=this.getDatasetMeta(c),h=!a&&s.indexOf(f)===-1;f.buildOrUpdateElements(h),l=Math.max(+f.getMaxOverflow(),l)}l=this._minPadding=n.layout.autoPadding?l:0,this._updateLayout(l),a||dt(s,c=>{c.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(pg("z","_idx"));const{_active:o,_lastEvent:r}=this;r?this._eventHandler(r,!0):o.length&&this._updateHoverStyles(o,o,!0),this.render()}_updateScales(){dt(this.scales,e=>{Le.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,i=new Set(Object.keys(this._listeners)),n=new Set(e.events);(!wp(i,n)||!!this._responsiveListeners!==e.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,i=this._getUniformDataChanges()||[];for(const{method:n,start:a,count:s}of i){const l=n==="_removeElements"?-s:s;Sk(e,a,l)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const i=this.data.datasets.length,n=s=>new Set(e.filter(l=>l[0]===s).map((l,o)=>o+","+l.splice(1).join(","))),a=n(0);for(let s=1;s<i;s++)if(!wp(a,n(s)))return;return Array.from(a).map(s=>s.split(",")).map(s=>({method:s[1],start:+s[2],count:+s[3]}))}_updateLayout(e){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Le.update(this,this.width,this.height,e);const i=this.chartArea,n=i.width<=0||i.height<=0;this._layers=[],dt(this.boxes,a=>{n&&a.position==="chartArea"||(a.configure&&a.configure(),this._layers.push(...a._layers()))},this),this._layers.forEach((a,s)=>{a._idx=s}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})!==!1){for(let i=0,n=this.data.datasets.length;i<n;++i)this.getDatasetMeta(i).controller.configure();for(let i=0,n=this.data.datasets.length;i<n;++i)this._updateDataset(i,cn(e)?e({datasetIndex:i}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,i){const n=this.getDatasetMeta(e),a={meta:n,index:e,mode:i,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",a)!==!1&&(n.controller._update(i),a.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",a))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(gi.has(this)?this.attached&&!gi.running(this)&&gi.start(this):(this.draw(),gg({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:n,height:a}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(n,a)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const i=this._layers;for(e=0;e<i.length&&i[e].z<=0;++e)i[e].draw(this.chartArea);for(this._drawDatasets();e<i.length;++e)i[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const i=this._sortedMetasets,n=[];let a,s;for(a=0,s=i.length;a<s;++a){const l=i[a];(!e||l.visible)&&n.push(l)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const e=this.getSortedVisibleDatasetMetas();for(let i=e.length-1;i>=0;--i)this._drawDataset(e[i]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const i=this.ctx,n={meta:e,index:e.index,cancelable:!0},a=cy(this,e);this.notifyPlugins("beforeDatasetDraw",n)!==!1&&(a&&zr(i,a),e.controller.draw(),a&&Tr(i),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(e){return tl(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,i,n,a){const s=y_.modes[i];return typeof s=="function"?s(this,e,n,a):[]}getDatasetMeta(e){const i=this.data.datasets[e],n=this._metasets;let a=n.filter(s=>s&&s._dataset===i).pop();return a||(a={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:i&&i.order||0,index:e,_dataset:i,_parsed:[],_sorted:!1},n.push(a)),a}getContext(){return this.$context||(this.$context=Gn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const i=this.data.datasets[e];if(!i)return!1;const n=this.getDatasetMeta(e);return typeof n.hidden=="boolean"?!n.hidden:!i.hidden}setDatasetVisibility(e,i){const n=this.getDatasetMeta(e);n.hidden=!i}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,i,n){const a=n?"show":"hide",s=this.getDatasetMeta(e),l=s.controller._resolveAnimations(void 0,a);Js(i)?(s.data[i].hidden=!n,this.update()):(this.setDatasetVisibility(e,n),l.update(s,{visible:n}),this.update(o=>o.datasetIndex===e?a:void 0))}hide(e,i){this._updateVisibility(e,i,!1)}show(e,i){this._updateVisibility(e,i,!0)}_destroyDatasetMeta(e){const i=this._metasets[e];i&&i.controller&&i.controller._destroy(),delete this._metasets[e]}_stop(){let e,i;for(this.stop(),gi.remove(this),e=0,i=this.data.datasets.length;e<i;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:i}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Lp(e,i),this.platform.releaseContext(i),this.canvas=null,this.ctx=null),delete _o[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...e){return this.canvas.toDataURL(...e)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,i=this.platform,n=(s,l)=>{i.addEventListener(this,s,l),e[s]=l},a=(s,l,o)=>{s.offsetX=l,s.offsetY=o,this._eventHandler(s)};dt(this.options.events,s=>n(s,a))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,i=this.platform,n=(r,c)=>{i.addEventListener(this,r,c),e[r]=c},a=(r,c)=>{e[r]&&(i.removeEventListener(this,r,c),delete e[r])},s=(r,c)=>{this.canvas&&this.resize(r,c)};let l;const o=()=>{a("attach",o),this.attached=!0,this.resize(),n("resize",s),n("detach",l)};l=()=>{this.attached=!1,a("resize",s),this._stop(),this._resize(0,0),n("attach",o)},i.isAttached(this.canvas)?o():l()}unbindEvents(){dt(this._listeners,(e,i)=>{this.platform.removeEventListener(this,i,e)}),this._listeners={},dt(this._responsiveListeners,(e,i)=>{this.platform.removeEventListener(this,i,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,i,n){const a=n?"set":"remove";let s,l,o,r;for(i==="dataset"&&(s=this.getDatasetMeta(e[0].datasetIndex),s.controller["_"+a+"DatasetHoverStyle"]()),o=0,r=e.length;o<r;++o){l=e[o];const c=l&&this.getDatasetMeta(l.datasetIndex).controller;c&&c[a+"HoverStyle"](l.element,l.datasetIndex,l.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const i=this._active||[],n=e.map(({datasetIndex:s,index:l})=>{const o=this.getDatasetMeta(s);if(!o)throw new Error("No dataset found at index "+s);return{datasetIndex:s,element:o.data[l],index:l}});!er(n,i)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,i))}notifyPlugins(e,i,n){return this._plugins.notify(this,e,i,n)}isPluginEnabled(e){return this._plugins._cache.filter(i=>i.plugin.id===e).length===1}_updateHoverStyles(e,i,n){const a=this.options.hover,s=(r,c)=>r.filter(d=>!c.some(f=>d.datasetIndex===f.datasetIndex&&d.index===f.index)),l=s(i,e),o=n?e:s(e,i);l.length&&this.updateHoverStyle(l,a.mode,!1),o.length&&a.mode&&this.updateHoverStyle(o,a.mode,!0)}_eventHandler(e,i){const n={event:e,replay:i,cancelable:!0,inChartArea:this.isPointInArea(e)},a=l=>(l.options.events||this.options.events).includes(e.native.type);if(this.notifyPlugins("beforeEvent",n,a)===!1)return;const s=this._handleEvent(e,i,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,a),(s||n.changed)&&this.render(),this}_handleEvent(e,i,n){const{_active:a=[],options:s}=this,l=i,o=this._getActiveElements(e,a,n,l),r=wS(e),c=_k(e,this._lastEvent,n,r);n&&(this._lastEvent=null,vt(s.onHover,[e,o,this],this),r&&vt(s.onClick,[e,o,this],this));const d=!er(o,a);return(d||i)&&(this._active=o,this._updateHoverStyles(o,a,i)),this._lastEvent=c,d}_getActiveElements(e,i,n,a){if(e.type==="mouseout")return[];if(!n)return i;const s=this.options.hover;return this.getElementsAtEventForMode(e,s.mode,s,a)}},Z(Ei,"defaults",Et),Z(Ei,"instances",_o),Z(Ei,"overrides",Hn),Z(Ei,"registry",$e),Z(Ei,"version",mk),Z(Ei,"getChart",bg),Ei);function xg(){return dt(vl.instances,t=>t._plugins.invalidate())}function _y(t,e,i=e){t.lineCap=P(i.borderCapStyle,e.borderCapStyle),t.setLineDash(P(i.borderDash,e.borderDash)),t.lineDashOffset=P(i.borderDashOffset,e.borderDashOffset),t.lineJoin=P(i.borderJoinStyle,e.borderJoinStyle),t.lineWidth=P(i.borderWidth,e.borderWidth),t.strokeStyle=P(i.borderColor,e.borderColor)}function kk(t,e,i){t.lineTo(i.x,i.y)}function Ck(t){return t.stepped?t2:t.tension||t.cubicInterpolationMode==="monotone"?e2:kk}function ky(t,e,i={}){const n=t.length,{start:a=0,end:s=n-1}=i,{start:l,end:o}=e,r=Math.max(a,l),c=Math.min(s,o),d=a<l&&s<l||a>o&&s>o;return{count:n,start:r,loop:e.loop,ilen:c<r&&!d?n+c-r:c-r}}function Dk(t,e,i,n){const{points:a,options:s}=e,{count:l,start:o,loop:r,ilen:c}=ky(a,i,n),d=Ck(s);let{move:f=!0,reverse:h}=n||{},g,m,v;for(g=0;g<=c;++g)m=a[(o+(h?c-g:g))%l],!m.skip&&(f?(t.moveTo(m.x,m.y),f=!1):d(t,v,m,h,s.stepped),v=m);return r&&(m=a[(o+(h?c:0))%l],d(t,v,m,h,s.stepped)),!!r}function wk(t,e,i,n){const a=e.points,{count:s,start:l,ilen:o}=ky(a,i,n),{move:r=!0,reverse:c}=n||{};let d=0,f=0,h,g,m,v,S,b;const x=_=>(l+(c?o-_:_))%s,y=()=>{v!==S&&(t.lineTo(d,S),t.lineTo(d,v),t.lineTo(d,b))};for(r&&(g=a[x(0)],t.moveTo(g.x,g.y)),h=0;h<=o;++h){if(g=a[x(h)],g.skip)continue;const _=g.x,D=g.y,A=_|0;A===m?(D<v?v=D:D>S&&(S=D),d=(f*d+_)/++f):(y(),t.lineTo(_,D),m=A,f=0,v=S=D),b=D}y()}function Qu(t){const e=t.options,i=e.borderDash&&e.borderDash.length;return!t._decimated&&!t._loop&&!e.tension&&e.cubicInterpolationMode!=="monotone"&&!e.stepped&&!i?wk:Dk}function jk(t){return t.stepped?R2:t.tension||t.cubicInterpolationMode==="monotone"?E2:_n}function zk(t,e,i,n){let a=e._path;a||(a=e._path=new Path2D,e.path(a,i,n)&&a.closePath()),_y(t,e.options),t.stroke(a)}function Tk(t,e,i,n){const{segments:a,options:s}=e,l=Qu(e);for(const o of a)_y(t,s,o.style),t.beginPath(),l(t,e,o,{start:i,end:i+n-1})&&t.closePath(),t.stroke()}const Ak=typeof Path2D=="function";function Mk(t,e,i,n){Ak&&!e.options.segment?zk(t,e,i,n):Tk(t,e,i,n)}class Wi extends ai{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,i){const n=this.options;if((n.tension||n.cubicInterpolationMode==="monotone")&&!n.stepped&&!this._pointsUpdated){const a=n.spanGaps?this._loop:this._fullLoop;D2(this._points,n,e,a,i),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=Y2(this,this.options.segment))}first(){const e=this.segments,i=this.points;return e.length&&i[e[0].start]}last(){const e=this.segments,i=this.points,n=e.length;return n&&i[e[n-1].end]}interpolate(e,i){const n=this.options,a=e[i],s=this.points,l=ry(this,{property:i,start:a,end:a});if(!l.length)return;const o=[],r=jk(n);let c,d;for(c=0,d=l.length;c<d;++c){const{start:f,end:h}=l[c],g=s[f],m=s[h];if(g===m){o.push(g);continue}const v=Math.abs((a-g[i])/(m[i]-g[i])),S=r(g,m,v,n.stepped);S[i]=e[i],o.push(S)}return o.length===1?o[0]:o}pathSegment(e,i,n){return Qu(this)(e,this,i,n)}path(e,i,n){const a=this.segments,s=Qu(this);let l=this._loop;i=i||0,n=n||this.points.length-i;for(const o of a)l&=s(e,this,o,{start:i,end:i+n-1});return!!l}draw(e,i,n,a){const s=this.options||{};(this.points||[]).length&&s.borderWidth&&(e.save(),Mk(e,this,n,a),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}Z(Wi,"id","line"),Z(Wi,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),Z(Wi,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),Z(Wi,"descriptors",{_scriptable:!0,_indexable:e=>e!=="borderDash"&&e!=="fill"});function mg(t,e,i,n){const a=t.options,{[i]:s}=t.getProps([i],n);return Math.abs(e-s)<a.radius+a.hitRadius}class ko extends ai{constructor(i){super();Z(this,"parsed");Z(this,"skip");Z(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,i&&Object.assign(this,i)}inRange(i,n,a){const s=this.options,{x:l,y:o}=this.getProps(["x","y"],a);return Math.pow(i-l,2)+Math.pow(n-o,2)<Math.pow(s.hitRadius+s.radius,2)}inXRange(i,n){return mg(this,i,"x",n)}inYRange(i,n){return mg(this,i,"y",n)}getCenterPoint(i){const{x:n,y:a}=this.getProps(["x","y"],i);return{x:n,y:a}}size(i){i=i||this.options||{};let n=i.radius||0;n=Math.max(n,n&&i.hoverRadius||0);const a=n&&i.borderWidth||0;return(n+a)*2}draw(i,n){const a=this.options;this.skip||a.radius<.1||!tl(this,n,this.size(a)/2)||(i.strokeStyle=a.borderColor,i.lineWidth=a.borderWidth,i.fillStyle=a.backgroundColor,Gu(i,a,this.x,this.y))}getRange(){const i=this.options||{};return i.radius+i.hitRadius}}Z(ko,"id","point"),Z(ko,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),Z(ko,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Cy(t,e){const{x:i,y:n,base:a,width:s,height:l}=t.getProps(["x","y","base","width","height"],e);let o,r,c,d,f;return t.horizontal?(f=l/2,o=Math.min(i,a),r=Math.max(i,a),c=n-f,d=n+f):(f=s/2,o=i-f,r=i+f,c=Math.min(n,a),d=Math.max(n,a)),{left:o,top:c,right:r,bottom:d}}function Ki(t,e,i,n){return t?0:_e(e,i,n)}function Ok(t,e,i){const n=t.options.borderWidth,a=t.borderSkipped,s=Jm(n);return{t:Ki(a.top,s.top,0,i),r:Ki(a.right,s.right,0,e),b:Ki(a.bottom,s.bottom,0,i),l:Ki(a.left,s.left,0,e)}}function Rk(t,e,i){const{enableBorderRadius:n}=t.getProps(["enableBorderRadius"]),a=t.options.borderRadius,s=_a(a),l=Math.min(e,i),o=t.borderSkipped,r=n||et(a);return{topLeft:Ki(!r||o.top||o.left,s.topLeft,0,l),topRight:Ki(!r||o.top||o.right,s.topRight,0,l),bottomLeft:Ki(!r||o.bottom||o.left,s.bottomLeft,0,l),bottomRight:Ki(!r||o.bottom||o.right,s.bottomRight,0,l)}}function Ek(t){const e=Cy(t),i=e.right-e.left,n=e.bottom-e.top,a=Ok(t,i/2,n/2),s=Rk(t,i/2,n/2);return{outer:{x:e.left,y:e.top,w:i,h:n,radius:s},inner:{x:e.left+a.l,y:e.top+a.t,w:i-a.l-a.r,h:n-a.t-a.b,radius:{topLeft:Math.max(0,s.topLeft-Math.max(a.t,a.l)),topRight:Math.max(0,s.topRight-Math.max(a.t,a.r)),bottomLeft:Math.max(0,s.bottomLeft-Math.max(a.b,a.l)),bottomRight:Math.max(0,s.bottomRight-Math.max(a.b,a.r))}}}}function Uc(t,e,i,n){const a=e===null,s=i===null,o=t&&!(a&&s)&&Cy(t,n);return o&&(a||Zi(e,o.left,o.right))&&(s||Zi(i,o.top,o.bottom))}function Bk(t){return t.topLeft||t.topRight||t.bottomLeft||t.bottomRight}function Lk(t,e){t.rect(e.x,e.y,e.w,e.h)}function Yc(t,e,i={}){const n=t.x!==i.x?-e:0,a=t.y!==i.y?-e:0,s=(t.x+t.w!==i.x+i.w?e:0)-n,l=(t.y+t.h!==i.y+i.h?e:0)-a;return{x:t.x+n,y:t.y+a,w:t.w+s,h:t.h+l,radius:t.radius}}class Co extends ai{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:i,options:{borderColor:n,backgroundColor:a}}=this,{inner:s,outer:l}=Ek(this),o=Bk(l.radius)?ar:Lk;e.save(),(l.w!==s.w||l.h!==s.h)&&(e.beginPath(),o(e,Yc(l,i,s)),e.clip(),o(e,Yc(s,-i,l)),e.fillStyle=n,e.fill("evenodd")),e.beginPath(),o(e,Yc(s,i)),e.fillStyle=a,e.fill(),e.restore()}inRange(e,i,n){return Uc(this,e,i,n)}inXRange(e,i){return Uc(this,e,null,i)}inYRange(e,i){return Uc(this,null,e,i)}getCenterPoint(e){const{x:i,y:n,base:a,horizontal:s}=this.getProps(["x","y","base","horizontal"],e);return{x:s?(i+a)/2:i,y:s?n:(n+a)/2}}getRange(e){return e==="x"?this.width/2:this.height/2}}Z(Co,"id","bar"),Z(Co,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),Z(Co,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Nk(t,e,i){const n=t.segments,a=t.points,s=e.points,l=[];for(const o of n){let{start:r,end:c}=o;c=Or(r,c,a);const d=Zu(i,a[r],a[c],o.loop);if(!e.segments){l.push({source:o,target:d,start:a[r],end:a[c]});continue}const f=ry(e,d);for(const h of f){const g=Zu(i,s[h.start],s[h.end],h.loop),m=oy(o,a,g);for(const v of m)l.push({source:v,target:h,start:{[i]:yg(d,g,"start",Math.max)},end:{[i]:yg(d,g,"end",Math.min)}})}}return l}function Zu(t,e,i,n){if(n)return;let a=e[t],s=i[t];return t==="angle"&&(a=Pe(a),s=Pe(s)),{property:t,start:a,end:s}}function Hk(t,e){const{x:i=null,y:n=null}=t||{},a=e.points,s=[];return e.segments.forEach(({start:l,end:o})=>{o=Or(l,o,a);const r=a[l],c=a[o];n!==null?(s.push({x:r.x,y:n}),s.push({x:c.x,y:n})):i!==null&&(s.push({x:i,y:r.y}),s.push({x:i,y:c.y}))}),s}function Or(t,e,i){for(;e>t;e--){const n=i[e];if(!isNaN(n.x)&&!isNaN(n.y))break}return e}function yg(t,e,i,n){return t&&e?n(t[i],e[i]):t?t[i]:e?e[i]:0}function Dy(t,e){let i=[],n=!1;return Lt(t)?(n=!0,i=t):i=Hk(t,e),i.length?new Wi({points:i,options:{tension:0},_loop:n,_fullLoop:n}):null}function vg(t){return t&&t.fill!==!1}function Uk(t,e,i){let a=t[e].fill;const s=[e];let l;if(!i)return a;for(;a!==!1&&s.indexOf(a)===-1;){if(!ee(a))return a;if(l=t[a],!l)return!1;if(l.visible)return a;s.push(a),a=l.fill}return!1}function Yk(t,e,i){const n=qk(t);if(et(n))return isNaN(n.value)?!1:n;let a=parseFloat(n);return ee(a)&&Math.floor(a)===a?Fk(n[0],e,a,i):["origin","start","end","stack","shape"].indexOf(n)>=0&&n}function Fk(t,e,i,n){return(t==="-"||t==="+")&&(i=e+i),i===e||i<0||i>=n?!1:i}function Vk(t,e){let i=null;return t==="start"?i=e.bottom:t==="end"?i=e.top:et(t)?i=e.getPixelForValue(t.value):e.getBasePixel&&(i=e.getBasePixel()),i}function Gk(t,e,i){let n;return t==="start"?n=i:t==="end"?n=e.options.reverse?e.min:e.max:et(t)?n=t.value:n=e.getBaseValue(),n}function qk(t){const e=t.options,i=e.fill;let n=P(i&&i.target,i);return n===void 0&&(n=!!e.backgroundColor),n===!1||n===null?!1:n===!0?"origin":n}function Xk(t){const{scale:e,index:i,line:n}=t,a=[],s=n.segments,l=n.points,o=Qk(e,i);o.push(Dy({x:null,y:e.bottom},n));for(let r=0;r<s.length;r++){const c=s[r];for(let d=c.start;d<=c.end;d++)Zk(a,l[d],o)}return new Wi({points:a,options:{}})}function Qk(t,e){const i=[],n=t.getMatchingVisibleMetas("line");for(let a=0;a<n.length;a++){const s=n[a];if(s.index===e)break;s.hidden||i.unshift(s.dataset)}return i}function Zk(t,e,i){const n=[];for(let a=0;a<i.length;a++){const s=i[a],{first:l,last:o,point:r}=Wk(s,e,"x");if(!(!r||l&&o)){if(l)n.unshift(r);else if(t.push(r),!o)break}}t.push(...n)}function Wk(t,e,i){const n=t.interpolate(e,i);if(!n)return{};const a=n[i],s=t.segments,l=t.points;let o=!1,r=!1;for(let c=0;c<s.length;c++){const d=s[c],f=l[d.start][i],h=l[d.end][i];if(Zi(a,f,h)){o=a===f,r=a===h;break}}return{first:o,last:r,point:n}}class wy{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,i,n){const{x:a,y:s,radius:l}=this;return i=i||{start:0,end:qe},e.arc(a,s,l,i.end,i.start,!0),!n.bounds}interpolate(e){const{x:i,y:n,radius:a}=this,s=e.angle;return{x:i+Math.cos(s)*a,y:n+Math.sin(s)*a,angle:s}}}function Kk(t){const{chart:e,fill:i,line:n}=t;if(ee(i))return $k(e,i);if(i==="stack")return Xk(t);if(i==="shape")return!0;const a=Pk(t);return a instanceof wy?a:Dy(a,n)}function $k(t,e){const i=t.getDatasetMeta(e);return i&&t.isDatasetVisible(e)?i.dataset:null}function Pk(t){return(t.scale||{}).getPointPositionForValue?Ik(t):Jk(t)}function Jk(t){const{scale:e={},fill:i}=t,n=Vk(i,e);if(ee(n)){const a=e.isHorizontal();return{x:a?n:null,y:a?null:n}}return null}function Ik(t){const{scale:e,fill:i}=t,n=e.options,a=e.getLabels().length,s=n.reverse?e.max:e.min,l=Gk(i,e,s),o=[];if(n.grid.circular){const r=e.getPointPositionForValue(0,s);return new wy({x:r.x,y:r.y,radius:e.getDistanceFromCenterForValue(l)})}for(let r=0;r<a;++r)o.push(e.getPointPositionForValue(r,l));return o}function Fc(t,e,i){const n=Kk(e),{chart:a,index:s,line:l,scale:o,axis:r}=e,c=l.options,d=c.fill,f=c.backgroundColor,{above:h=f,below:g=f}=d||{},m=a.getDatasetMeta(s),v=cy(a,m);n&&l.points.length&&(zr(t,i),tC(t,{line:l,target:n,above:h,below:g,area:i,scale:o,axis:r,clip:v}),Tr(t))}function tC(t,e){const{line:i,target:n,above:a,below:s,area:l,scale:o,clip:r}=e,c=i._loop?"angle":e.axis;t.save();let d=s;s!==a&&(c==="x"?(Sg(t,n,l.top),Vc(t,{line:i,target:n,color:a,scale:o,property:c,clip:r}),t.restore(),t.save(),Sg(t,n,l.bottom)):c==="y"&&(_g(t,n,l.left),Vc(t,{line:i,target:n,color:s,scale:o,property:c,clip:r}),t.restore(),t.save(),_g(t,n,l.right),d=a)),Vc(t,{line:i,target:n,color:d,scale:o,property:c,clip:r}),t.restore()}function Sg(t,e,i){const{segments:n,points:a}=e;let s=!0,l=!1;t.beginPath();for(const o of n){const{start:r,end:c}=o,d=a[r],f=a[Or(r,c,a)];s?(t.moveTo(d.x,d.y),s=!1):(t.lineTo(d.x,i),t.lineTo(d.x,d.y)),l=!!e.pathSegment(t,o,{move:l}),l?t.closePath():t.lineTo(f.x,i)}t.lineTo(e.first().x,i),t.closePath(),t.clip()}function _g(t,e,i){const{segments:n,points:a}=e;let s=!0,l=!1;t.beginPath();for(const o of n){const{start:r,end:c}=o,d=a[r],f=a[Or(r,c,a)];s?(t.moveTo(d.x,d.y),s=!1):(t.lineTo(i,d.y),t.lineTo(d.x,d.y)),l=!!e.pathSegment(t,o,{move:l}),l?t.closePath():t.lineTo(i,f.y)}t.lineTo(i,e.first().y),t.closePath(),t.clip()}function Vc(t,e){const{line:i,target:n,property:a,color:s,scale:l,clip:o}=e,r=Nk(i,n,a);for(const{source:c,target:d,start:f,end:h}of r){const{style:{backgroundColor:g=s}={}}=c,m=n!==!0;t.save(),t.fillStyle=g,eC(t,l,o,m&&Zu(a,f,h)),t.beginPath();const v=!!i.pathSegment(t,c);let S;if(m){v?t.closePath():kg(t,n,h,a);const b=!!n.pathSegment(t,d,{move:v,reverse:!0});S=v&&b,S||kg(t,n,f,a)}t.closePath(),t.fill(S?"evenodd":"nonzero"),t.restore()}}function eC(t,e,i,n){const a=e.chart.chartArea,{property:s,start:l,end:o}=n||{};if(s==="x"||s==="y"){let r,c,d,f;s==="x"?(r=l,c=a.top,d=o,f=a.bottom):(r=a.left,c=l,d=a.right,f=o),t.beginPath(),i&&(r=Math.max(r,i.left),d=Math.min(d,i.right),c=Math.max(c,i.top),f=Math.min(f,i.bottom)),t.rect(r,c,d-r,f-c),t.clip()}}function kg(t,e,i,n){const a=e.interpolate(i,n);a&&t.lineTo(a.x,a.y)}var iC={id:"filler",afterDatasetsUpdate(t,e,i){const n=(t.data.datasets||[]).length,a=[];let s,l,o,r;for(l=0;l<n;++l)s=t.getDatasetMeta(l),o=s.dataset,r=null,o&&o.options&&o instanceof Wi&&(r={visible:t.isDatasetVisible(l),index:l,fill:Yk(o,l,n),chart:t,axis:s.controller.options.indexAxis,scale:s.vScale,line:o}),s.$filler=r,a.push(r);for(l=0;l<n;++l)r=a[l],!(!r||r.fill===!1)&&(r.fill=Uk(a,l,i.propagate))},beforeDraw(t,e,i){const n=i.drawTime==="beforeDraw",a=t.getSortedVisibleDatasetMetas(),s=t.chartArea;for(let l=a.length-1;l>=0;--l){const o=a[l].$filler;o&&(o.line.updateControlPoints(s,o.axis),n&&o.fill&&Fc(t.ctx,o,s))}},beforeDatasetsDraw(t,e,i){if(i.drawTime!=="beforeDatasetsDraw")return;const n=t.getSortedVisibleDatasetMetas();for(let a=n.length-1;a>=0;--a){const s=n[a].$filler;vg(s)&&Fc(t.ctx,s,t.chartArea)}},beforeDatasetDraw(t,e,i){const n=e.meta.$filler;!vg(n)||i.drawTime!=="beforeDatasetDraw"||Fc(t.ctx,n,t.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Cg=(t,e)=>{let{boxHeight:i=e,boxWidth:n=e}=t;return t.usePointStyle&&(i=Math.min(i,e),n=t.pointStyleWidth||Math.min(n,e)),{boxWidth:n,boxHeight:i,itemHeight:Math.max(e,i)}},nC=(t,e)=>t!==null&&e!==null&&t.datasetIndex===e.datasetIndex&&t.index===e.index;class Dg extends ai{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,i,n){this.maxWidth=e,this.maxHeight=i,this._margins=n,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let i=vt(e.generateLabels,[this.chart],this)||[];e.filter&&(i=i.filter(n=>e.filter(n,this.chart.data))),e.sort&&(i=i.sort((n,a)=>e.sort(n,a,this.chart.data))),this.options.reverse&&i.reverse(),this.legendItems=i}fit(){const{options:e,ctx:i}=this;if(!e.display){this.width=this.height=0;return}const n=e.labels,a=te(n.font),s=a.size,l=this._computeTitleHeight(),{boxWidth:o,itemHeight:r}=Cg(n,s);let c,d;i.font=a.string,this.isHorizontal()?(c=this.maxWidth,d=this._fitRows(l,s,o,r)+10):(d=this.maxHeight,c=this._fitCols(l,a,o,r)+10),this.width=Math.min(c,e.maxWidth||this.maxWidth),this.height=Math.min(d,e.maxHeight||this.maxHeight)}_fitRows(e,i,n,a){const{ctx:s,maxWidth:l,options:{labels:{padding:o}}}=this,r=this.legendHitBoxes=[],c=this.lineWidths=[0],d=a+o;let f=e;s.textAlign="left",s.textBaseline="middle";let h=-1,g=-d;return this.legendItems.forEach((m,v)=>{const S=n+i/2+s.measureText(m.text).width;(v===0||c[c.length-1]+S+2*o>l)&&(f+=d,c[c.length-(v>0?0:1)]=0,g+=d,h++),r[v]={left:0,top:g,row:h,width:S,height:a},c[c.length-1]+=S+o}),f}_fitCols(e,i,n,a){const{ctx:s,maxHeight:l,options:{labels:{padding:o}}}=this,r=this.legendHitBoxes=[],c=this.columnSizes=[],d=l-e;let f=o,h=0,g=0,m=0,v=0;return this.legendItems.forEach((S,b)=>{const{itemWidth:x,itemHeight:y}=aC(n,i,s,S,a);b>0&&g+y+2*o>d&&(f+=h+o,c.push({width:h,height:g}),m+=h+o,v++,h=g=0),r[b]={left:m,top:g,col:v,width:x,height:y},h=Math.max(h,x),g+=y+o}),f+=h,c.push({width:h,height:g}),f}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:i,options:{align:n,labels:{padding:a},rtl:s}}=this,l=ka(s,this.left,this.width);if(this.isHorizontal()){let o=0,r=Pt(n,this.left+a,this.right-this.lineWidths[o]);for(const c of i)o!==c.row&&(o=c.row,r=Pt(n,this.left+a,this.right-this.lineWidths[o])),c.top+=this.top+e+a,c.left=l.leftForLtr(l.x(r),c.width),r+=c.width+a}else{let o=0,r=Pt(n,this.top+e+a,this.bottom-this.columnSizes[o].height);for(const c of i)c.col!==o&&(o=c.col,r=Pt(n,this.top+e+a,this.bottom-this.columnSizes[o].height)),c.top=r,c.left+=this.left+a,c.left=l.leftForLtr(l.x(c.left),c.width),r+=c.height+a}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const e=this.ctx;zr(e,this),this._draw(),Tr(e)}}_draw(){const{options:e,columnSizes:i,lineWidths:n,ctx:a}=this,{align:s,labels:l}=e,o=Et.color,r=ka(e.rtl,this.left,this.width),c=te(l.font),{padding:d}=l,f=c.size,h=f/2;let g;this.drawTitle(),a.textAlign=r.textAlign("left"),a.textBaseline="middle",a.lineWidth=.5,a.font=c.string;const{boxWidth:m,boxHeight:v,itemHeight:S}=Cg(l,f),b=function(A,j,T){if(isNaN(m)||m<=0||isNaN(v)||v<0)return;a.save();const M=P(T.lineWidth,1);if(a.fillStyle=P(T.fillStyle,o),a.lineCap=P(T.lineCap,"butt"),a.lineDashOffset=P(T.lineDashOffset,0),a.lineJoin=P(T.lineJoin,"miter"),a.lineWidth=M,a.strokeStyle=P(T.strokeStyle,o),a.setLineDash(P(T.lineDash,[])),l.usePointStyle){const L={radius:v*Math.SQRT2/2,pointStyle:T.pointStyle,rotation:T.rotation,borderWidth:M},Y=r.xPlus(A,m/2),G=j+h;$m(a,L,Y,G,l.pointStyleWidth&&m)}else{const L=j+Math.max((f-v)/2,0),Y=r.leftForLtr(A,m),G=_a(T.borderRadius);a.beginPath(),Object.values(G).some(mt=>mt!==0)?ar(a,{x:Y,y:L,w:m,h:v,radius:G}):a.rect(Y,L,m,v),a.fill(),M!==0&&a.stroke()}a.restore()},x=function(A,j,T){el(a,T.text,A,j+S/2,c,{strikethrough:T.hidden,textAlign:r.textAlign(T.textAlign)})},y=this.isHorizontal(),_=this._computeTitleHeight();y?g={x:Pt(s,this.left+d,this.right-n[0]),y:this.top+d+_,line:0}:g={x:this.left+d,y:Pt(s,this.top+_+d,this.bottom-i[0].height),line:0},ay(this.ctx,e.textDirection);const D=S+d;this.legendItems.forEach((A,j)=>{a.strokeStyle=A.fontColor,a.fillStyle=A.fontColor;const T=a.measureText(A.text).width,M=r.textAlign(A.textAlign||(A.textAlign=l.textAlign)),L=m+h+T;let Y=g.x,G=g.y;r.setWidth(this.width),y?j>0&&Y+L+d>this.right&&(G=g.y+=D,g.line++,Y=g.x=Pt(s,this.left+d,this.right-n[g.line])):j>0&&G+D>this.bottom&&(Y=g.x=Y+i[g.line].width+d,g.line++,G=g.y=Pt(s,this.top+_+d,this.bottom-i[g.line].height));const mt=r.x(Y);if(b(mt,G,A),Y=FS(M,Y+m+h,y?Y+L:this.right,e.rtl),x(r.x(Y),G,A),y)g.x+=L+d;else if(typeof A.text!="string"){const wt=c.lineHeight;g.y+=jy(A,wt)+d}else g.y+=D}),sy(this.ctx,e.textDirection)}drawTitle(){const e=this.options,i=e.title,n=te(i.font),a=Ue(i.padding);if(!i.display)return;const s=ka(e.rtl,this.left,this.width),l=this.ctx,o=i.position,r=n.size/2,c=a.top+r;let d,f=this.left,h=this.width;if(this.isHorizontal())h=Math.max(...this.lineWidths),d=this.top+c,f=Pt(e.align,f,this.right-h);else{const m=this.columnSizes.reduce((v,S)=>Math.max(v,S.height),0);d=c+Pt(e.align,this.top,this.bottom-m-e.labels.padding-this._computeTitleHeight())}const g=Pt(o,f,f+h);l.textAlign=s.textAlign(Id(o)),l.textBaseline="middle",l.strokeStyle=i.color,l.fillStyle=i.color,l.font=n.string,el(l,i.text,g,d,n)}_computeTitleHeight(){const e=this.options.title,i=te(e.font),n=Ue(e.padding);return e.display?i.lineHeight+n.height:0}_getLegendItemAt(e,i){let n,a,s;if(Zi(e,this.left,this.right)&&Zi(i,this.top,this.bottom)){for(s=this.legendHitBoxes,n=0;n<s.length;++n)if(a=s[n],Zi(e,a.left,a.left+a.width)&&Zi(i,a.top,a.top+a.height))return this.legendItems[n]}return null}handleEvent(e){const i=this.options;if(!oC(e.type,i))return;const n=this._getLegendItemAt(e.x,e.y);if(e.type==="mousemove"||e.type==="mouseout"){const a=this._hoveredItem,s=nC(a,n);a&&!s&&vt(i.onLeave,[e,a,this],this),this._hoveredItem=n,n&&!s&&vt(i.onHover,[e,n,this],this)}else n&&vt(i.onClick,[e,n,this],this)}}function aC(t,e,i,n,a){const s=sC(n,t,e,i),l=lC(a,n,e.lineHeight);return{itemWidth:s,itemHeight:l}}function sC(t,e,i,n){let a=t.text;return a&&typeof a!="string"&&(a=a.reduce((s,l)=>s.length>l.length?s:l)),e+i.size/2+n.measureText(a).width}function lC(t,e,i){let n=t;return typeof e.text!="string"&&(n=jy(e,i)),n}function jy(t,e){const i=t.text?t.text.length:0;return e*i}function oC(t,e){return!!((t==="mousemove"||t==="mouseout")&&(e.onHover||e.onLeave)||e.onClick&&(t==="click"||t==="mouseup"))}var zy={id:"legend",_element:Dg,start(t,e,i){const n=t.legend=new Dg({ctx:t.ctx,options:i,chart:t});Le.configure(t,n,i),Le.addBox(t,n)},stop(t){Le.removeBox(t,t.legend),delete t.legend},beforeUpdate(t,e,i){const n=t.legend;Le.configure(t,n,i),n.options=i},afterUpdate(t){const e=t.legend;e.buildLabels(),e.adjustHitBoxes()},afterEvent(t,e){e.replay||t.legend.handleEvent(e.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(t,e,i){const n=e.datasetIndex,a=i.chart;a.isDatasetVisible(n)?(a.hide(n),e.hidden=!0):(a.show(n),e.hidden=!1)},onHover:null,onLeave:null,labels:{color:t=>t.chart.options.color,boxWidth:40,padding:10,generateLabels(t){const e=t.data.datasets,{labels:{usePointStyle:i,pointStyle:n,textAlign:a,color:s,useBorderRadius:l,borderRadius:o}}=t.legend.options;return t._getSortedDatasetMetas().map(r=>{const c=r.controller.getStyle(i?0:void 0),d=Ue(c.borderWidth);return{text:e[r.index].label,fillStyle:c.backgroundColor,fontColor:s,hidden:!r.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(d.width+d.height)/4,strokeStyle:c.borderColor,pointStyle:n||c.pointStyle,rotation:c.rotation,textAlign:a||c.textAlign,borderRadius:l&&(o||c.borderRadius),datasetIndex:r.index}},this)}},title:{color:t=>t.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:t=>!t.startsWith("on"),labels:{_scriptable:t=>!["generateLabels","filter","sort"].includes(t)}}};class Ty extends ai{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,i){const n=this.options;if(this.left=0,this.top=0,!n.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=e,this.height=this.bottom=i;const a=Lt(n.text)?n.text.length:1;this._padding=Ue(n.padding);const s=a*te(n.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=s:this.width=s}isHorizontal(){const e=this.options.position;return e==="top"||e==="bottom"}_drawArgs(e){const{top:i,left:n,bottom:a,right:s,options:l}=this,o=l.align;let r=0,c,d,f;return this.isHorizontal()?(d=Pt(o,n,s),f=i+e,c=s-n):(l.position==="left"?(d=n+e,f=Pt(o,a,i),r=Zt*-.5):(d=s-e,f=Pt(o,i,a),r=Zt*.5),c=a-i),{titleX:d,titleY:f,maxWidth:c,rotation:r}}draw(){const e=this.ctx,i=this.options;if(!i.display)return;const n=te(i.font),s=n.lineHeight/2+this._padding.top,{titleX:l,titleY:o,maxWidth:r,rotation:c}=this._drawArgs(s);el(e,i.text,0,0,n,{color:i.color,maxWidth:r,rotation:c,textAlign:Id(i.align),textBaseline:"middle",translation:[l,o]})}}function rC(t,e){const i=new Ty({ctx:t.ctx,options:e,chart:t});Le.configure(t,i,e),Le.addBox(t,i),t.titleBlock=i}var Ay={id:"title",_element:Ty,start(t,e,i){rC(t,i)},stop(t){const e=t.titleBlock;Le.removeBox(t,e),delete t.titleBlock},beforeUpdate(t,e,i){const n=t.titleBlock;Le.configure(t,n,i),n.options=i},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const bs={average(t){if(!t.length)return!1;let e,i,n=new Set,a=0,s=0;for(e=0,i=t.length;e<i;++e){const o=t[e].element;if(o&&o.hasValue()){const r=o.tooltipPosition();n.add(r.x),a+=r.y,++s}}return s===0||n.size===0?!1:{x:[...n].reduce((o,r)=>o+r)/n.size,y:a/s}},nearest(t,e){if(!t.length)return!1;let i=e.x,n=e.y,a=Number.POSITIVE_INFINITY,s,l,o;for(s=0,l=t.length;s<l;++s){const r=t[s].element;if(r&&r.hasValue()){const c=r.getCenterPoint(),d=Fu(e,c);d<a&&(a=d,o=r)}}if(o){const r=o.tooltipPosition();i=r.x,n=r.y}return{x:i,y:n}}};function Ke(t,e){return e&&(Lt(e)?Array.prototype.push.apply(t,e):t.push(e)),t}function bi(t){return(typeof t=="string"||t instanceof String)&&t.indexOf(`
`)>-1?t.split(`
`):t}function cC(t,e){const{element:i,datasetIndex:n,index:a}=e,s=t.getDatasetMeta(n).controller,{label:l,value:o}=s.getLabelAndValue(a);return{chart:t,label:l,parsed:s.getParsed(a),raw:t.data.datasets[n].data[a],formattedValue:o,dataset:s.getDataset(),dataIndex:a,datasetIndex:n,element:i}}function wg(t,e){const i=t.chart.ctx,{body:n,footer:a,title:s}=t,{boxWidth:l,boxHeight:o}=e,r=te(e.bodyFont),c=te(e.titleFont),d=te(e.footerFont),f=s.length,h=a.length,g=n.length,m=Ue(e.padding);let v=m.height,S=0,b=n.reduce((_,D)=>_+D.before.length+D.lines.length+D.after.length,0);if(b+=t.beforeBody.length+t.afterBody.length,f&&(v+=f*c.lineHeight+(f-1)*e.titleSpacing+e.titleMarginBottom),b){const _=e.displayColors?Math.max(o,r.lineHeight):r.lineHeight;v+=g*_+(b-g)*r.lineHeight+(b-1)*e.bodySpacing}h&&(v+=e.footerMarginTop+h*d.lineHeight+(h-1)*e.footerSpacing);let x=0;const y=function(_){S=Math.max(S,i.measureText(_).width+x)};return i.save(),i.font=c.string,dt(t.title,y),i.font=r.string,dt(t.beforeBody.concat(t.afterBody),y),x=e.displayColors?l+2+e.boxPadding:0,dt(n,_=>{dt(_.before,y),dt(_.lines,y),dt(_.after,y)}),x=0,i.font=d.string,dt(t.footer,y),i.restore(),S+=m.width,{width:S,height:v}}function uC(t,e){const{y:i,height:n}=e;return i<n/2?"top":i>t.height-n/2?"bottom":"center"}function dC(t,e,i,n){const{x:a,width:s}=n,l=i.caretSize+i.caretPadding;if(t==="left"&&a+s+l>e.width||t==="right"&&a-s-l<0)return!0}function fC(t,e,i,n){const{x:a,width:s}=i,{width:l,chartArea:{left:o,right:r}}=t;let c="center";return n==="center"?c=a<=(o+r)/2?"left":"right":a<=s/2?c="left":a>=l-s/2&&(c="right"),dC(c,t,e,i)&&(c="center"),c}function jg(t,e,i){const n=i.yAlign||e.yAlign||uC(t,i);return{xAlign:i.xAlign||e.xAlign||fC(t,e,i,n),yAlign:n}}function hC(t,e){let{x:i,width:n}=t;return e==="right"?i-=n:e==="center"&&(i-=n/2),i}function pC(t,e,i){let{y:n,height:a}=t;return e==="top"?n+=i:e==="bottom"?n-=a+i:n-=a/2,n}function zg(t,e,i,n){const{caretSize:a,caretPadding:s,cornerRadius:l}=t,{xAlign:o,yAlign:r}=i,c=a+s,{topLeft:d,topRight:f,bottomLeft:h,bottomRight:g}=_a(l);let m=hC(e,o);const v=pC(e,r,c);return r==="center"?o==="left"?m+=c:o==="right"&&(m-=c):o==="left"?m-=Math.max(d,h)+a:o==="right"&&(m+=Math.max(f,g)+a),{x:_e(m,0,n.width-e.width),y:_e(v,0,n.height-e.height)}}function no(t,e,i){const n=Ue(i.padding);return e==="center"?t.x+t.width/2:e==="right"?t.x+t.width-n.right:t.x+n.left}function Tg(t){return Ke([],bi(t))}function gC(t,e,i){return Gn(t,{tooltip:e,tooltipItems:i,type:"tooltip"})}function Ag(t,e){const i=e&&e.dataset&&e.dataset.tooltip&&e.dataset.tooltip.callbacks;return i?t.override(i):t}const My={beforeTitle:pi,title(t){if(t.length>0){const e=t[0],i=e.chart.data.labels,n=i?i.length:0;if(this&&this.options&&this.options.mode==="dataset")return e.dataset.label||"";if(e.label)return e.label;if(n>0&&e.dataIndex<n)return i[e.dataIndex]}return""},afterTitle:pi,beforeBody:pi,beforeLabel:pi,label(t){if(this&&this.options&&this.options.mode==="dataset")return t.label+": "+t.formattedValue||t.formattedValue;let e=t.dataset.label||"";e&&(e+=": ");const i=t.formattedValue;return ct(i)||(e+=i),e},labelColor(t){const i=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{borderColor:i.borderColor,backgroundColor:i.backgroundColor,borderWidth:i.borderWidth,borderDash:i.borderDash,borderDashOffset:i.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(t){const i=t.chart.getDatasetMeta(t.datasetIndex).controller.getStyle(t.dataIndex);return{pointStyle:i.pointStyle,rotation:i.rotation}},afterLabel:pi,afterBody:pi,beforeFooter:pi,footer:pi,afterFooter:pi};function ce(t,e,i,n){const a=t[e].call(i,n);return typeof a>"u"?My[e].call(i,n):a}class Wu extends ai{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const i=this.chart,n=this.options.setContext(this.getContext()),a=n.enabled&&i.options.animation&&n.animations,s=new uy(this.chart,a);return a._cacheable&&(this._cachedAnimations=Object.freeze(s)),s}getContext(){return this.$context||(this.$context=gC(this.chart.getContext(),this,this._tooltipItems))}getTitle(e,i){const{callbacks:n}=i,a=ce(n,"beforeTitle",this,e),s=ce(n,"title",this,e),l=ce(n,"afterTitle",this,e);let o=[];return o=Ke(o,bi(a)),o=Ke(o,bi(s)),o=Ke(o,bi(l)),o}getBeforeBody(e,i){return Tg(ce(i.callbacks,"beforeBody",this,e))}getBody(e,i){const{callbacks:n}=i,a=[];return dt(e,s=>{const l={before:[],lines:[],after:[]},o=Ag(n,s);Ke(l.before,bi(ce(o,"beforeLabel",this,s))),Ke(l.lines,ce(o,"label",this,s)),Ke(l.after,bi(ce(o,"afterLabel",this,s))),a.push(l)}),a}getAfterBody(e,i){return Tg(ce(i.callbacks,"afterBody",this,e))}getFooter(e,i){const{callbacks:n}=i,a=ce(n,"beforeFooter",this,e),s=ce(n,"footer",this,e),l=ce(n,"afterFooter",this,e);let o=[];return o=Ke(o,bi(a)),o=Ke(o,bi(s)),o=Ke(o,bi(l)),o}_createItems(e){const i=this._active,n=this.chart.data,a=[],s=[],l=[];let o=[],r,c;for(r=0,c=i.length;r<c;++r)o.push(cC(this.chart,i[r]));return e.filter&&(o=o.filter((d,f,h)=>e.filter(d,f,h,n))),e.itemSort&&(o=o.sort((d,f)=>e.itemSort(d,f,n))),dt(o,d=>{const f=Ag(e.callbacks,d);a.push(ce(f,"labelColor",this,d)),s.push(ce(f,"labelPointStyle",this,d)),l.push(ce(f,"labelTextColor",this,d))}),this.labelColors=a,this.labelPointStyles=s,this.labelTextColors=l,this.dataPoints=o,o}update(e,i){const n=this.options.setContext(this.getContext()),a=this._active;let s,l=[];if(!a.length)this.opacity!==0&&(s={opacity:0});else{const o=bs[n.position].call(this,a,this._eventPosition);l=this._createItems(n),this.title=this.getTitle(l,n),this.beforeBody=this.getBeforeBody(l,n),this.body=this.getBody(l,n),this.afterBody=this.getAfterBody(l,n),this.footer=this.getFooter(l,n);const r=this._size=wg(this,n),c=Object.assign({},o,r),d=jg(this.chart,n,c),f=zg(n,c,d,this.chart);this.xAlign=d.xAlign,this.yAlign=d.yAlign,s={opacity:1,x:f.x,y:f.y,width:r.width,height:r.height,caretX:o.x,caretY:o.y}}this._tooltipItems=l,this.$context=void 0,s&&this._resolveAnimations().update(this,s),e&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:i})}drawCaret(e,i,n,a){const s=this.getCaretPosition(e,n,a);i.lineTo(s.x1,s.y1),i.lineTo(s.x2,s.y2),i.lineTo(s.x3,s.y3)}getCaretPosition(e,i,n){const{xAlign:a,yAlign:s}=this,{caretSize:l,cornerRadius:o}=n,{topLeft:r,topRight:c,bottomLeft:d,bottomRight:f}=_a(o),{x:h,y:g}=e,{width:m,height:v}=i;let S,b,x,y,_,D;return s==="center"?(_=g+v/2,a==="left"?(S=h,b=S-l,y=_+l,D=_-l):(S=h+m,b=S+l,y=_-l,D=_+l),x=S):(a==="left"?b=h+Math.max(r,d)+l:a==="right"?b=h+m-Math.max(c,f)-l:b=this.caretX,s==="top"?(y=g,_=y-l,S=b-l,x=b+l):(y=g+v,_=y+l,S=b+l,x=b-l),D=y),{x1:S,x2:b,x3:x,y1:y,y2:_,y3:D}}drawTitle(e,i,n){const a=this.title,s=a.length;let l,o,r;if(s){const c=ka(n.rtl,this.x,this.width);for(e.x=no(this,n.titleAlign,n),i.textAlign=c.textAlign(n.titleAlign),i.textBaseline="middle",l=te(n.titleFont),o=n.titleSpacing,i.fillStyle=n.titleColor,i.font=l.string,r=0;r<s;++r)i.fillText(a[r],c.x(e.x),e.y+l.lineHeight/2),e.y+=l.lineHeight+o,r+1===s&&(e.y+=n.titleMarginBottom-o)}}_drawColorBox(e,i,n,a,s){const l=this.labelColors[n],o=this.labelPointStyles[n],{boxHeight:r,boxWidth:c}=s,d=te(s.bodyFont),f=no(this,"left",s),h=a.x(f),g=r<d.lineHeight?(d.lineHeight-r)/2:0,m=i.y+g;if(s.usePointStyle){const v={radius:Math.min(c,r)/2,pointStyle:o.pointStyle,rotation:o.rotation,borderWidth:1},S=a.leftForLtr(h,c)+c/2,b=m+r/2;e.strokeStyle=s.multiKeyBackground,e.fillStyle=s.multiKeyBackground,Gu(e,v,S,b),e.strokeStyle=l.borderColor,e.fillStyle=l.backgroundColor,Gu(e,v,S,b)}else{e.lineWidth=et(l.borderWidth)?Math.max(...Object.values(l.borderWidth)):l.borderWidth||1,e.strokeStyle=l.borderColor,e.setLineDash(l.borderDash||[]),e.lineDashOffset=l.borderDashOffset||0;const v=a.leftForLtr(h,c),S=a.leftForLtr(a.xPlus(h,1),c-2),b=_a(l.borderRadius);Object.values(b).some(x=>x!==0)?(e.beginPath(),e.fillStyle=s.multiKeyBackground,ar(e,{x:v,y:m,w:c,h:r,radius:b}),e.fill(),e.stroke(),e.fillStyle=l.backgroundColor,e.beginPath(),ar(e,{x:S,y:m+1,w:c-2,h:r-2,radius:b}),e.fill()):(e.fillStyle=s.multiKeyBackground,e.fillRect(v,m,c,r),e.strokeRect(v,m,c,r),e.fillStyle=l.backgroundColor,e.fillRect(S,m+1,c-2,r-2))}e.fillStyle=this.labelTextColors[n]}drawBody(e,i,n){const{body:a}=this,{bodySpacing:s,bodyAlign:l,displayColors:o,boxHeight:r,boxWidth:c,boxPadding:d}=n,f=te(n.bodyFont);let h=f.lineHeight,g=0;const m=ka(n.rtl,this.x,this.width),v=function(T){i.fillText(T,m.x(e.x+g),e.y+h/2),e.y+=h+s},S=m.textAlign(l);let b,x,y,_,D,A,j;for(i.textAlign=l,i.textBaseline="middle",i.font=f.string,e.x=no(this,S,n),i.fillStyle=n.bodyColor,dt(this.beforeBody,v),g=o&&S!=="right"?l==="center"?c/2+d:c+2+d:0,_=0,A=a.length;_<A;++_){for(b=a[_],x=this.labelTextColors[_],i.fillStyle=x,dt(b.before,v),y=b.lines,o&&y.length&&(this._drawColorBox(i,e,_,m,n),h=Math.max(f.lineHeight,r)),D=0,j=y.length;D<j;++D)v(y[D]),h=f.lineHeight;dt(b.after,v)}g=0,h=f.lineHeight,dt(this.afterBody,v),e.y-=s}drawFooter(e,i,n){const a=this.footer,s=a.length;let l,o;if(s){const r=ka(n.rtl,this.x,this.width);for(e.x=no(this,n.footerAlign,n),e.y+=n.footerMarginTop,i.textAlign=r.textAlign(n.footerAlign),i.textBaseline="middle",l=te(n.footerFont),i.fillStyle=n.footerColor,i.font=l.string,o=0;o<s;++o)i.fillText(a[o],r.x(e.x),e.y+l.lineHeight/2),e.y+=l.lineHeight+n.footerSpacing}}drawBackground(e,i,n,a){const{xAlign:s,yAlign:l}=this,{x:o,y:r}=e,{width:c,height:d}=n,{topLeft:f,topRight:h,bottomLeft:g,bottomRight:m}=_a(a.cornerRadius);i.fillStyle=a.backgroundColor,i.strokeStyle=a.borderColor,i.lineWidth=a.borderWidth,i.beginPath(),i.moveTo(o+f,r),l==="top"&&this.drawCaret(e,i,n,a),i.lineTo(o+c-h,r),i.quadraticCurveTo(o+c,r,o+c,r+h),l==="center"&&s==="right"&&this.drawCaret(e,i,n,a),i.lineTo(o+c,r+d-m),i.quadraticCurveTo(o+c,r+d,o+c-m,r+d),l==="bottom"&&this.drawCaret(e,i,n,a),i.lineTo(o+g,r+d),i.quadraticCurveTo(o,r+d,o,r+d-g),l==="center"&&s==="left"&&this.drawCaret(e,i,n,a),i.lineTo(o,r+f),i.quadraticCurveTo(o,r,o+f,r),i.closePath(),i.fill(),a.borderWidth>0&&i.stroke()}_updateAnimationTarget(e){const i=this.chart,n=this.$animations,a=n&&n.x,s=n&&n.y;if(a||s){const l=bs[e.position].call(this,this._active,this._eventPosition);if(!l)return;const o=this._size=wg(this,e),r=Object.assign({},l,this._size),c=jg(i,e,r),d=zg(e,r,c,i);(a._to!==d.x||s._to!==d.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=o.width,this.height=o.height,this.caretX=l.x,this.caretY=l.y,this._resolveAnimations().update(this,d))}}_willRender(){return!!this.opacity}draw(e){const i=this.options.setContext(this.getContext());let n=this.opacity;if(!n)return;this._updateAnimationTarget(i);const a={width:this.width,height:this.height},s={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;const l=Ue(i.padding),o=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;i.enabled&&o&&(e.save(),e.globalAlpha=n,this.drawBackground(s,e,a,i),ay(e,i.textDirection),s.y+=l.top,this.drawTitle(s,e,i),this.drawBody(s,e,i),this.drawFooter(s,e,i),sy(e,i.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,i){const n=this._active,a=e.map(({datasetIndex:o,index:r})=>{const c=this.chart.getDatasetMeta(o);if(!c)throw new Error("Cannot find a dataset at index "+o);return{datasetIndex:o,element:c.data[r],index:r}}),s=!er(n,a),l=this._positionChanged(a,i);(s||l)&&(this._active=a,this._eventPosition=i,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,i,n=!0){if(i&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const a=this.options,s=this._active||[],l=this._getActiveElements(e,s,i,n),o=this._positionChanged(l,e),r=i||!er(l,s)||o;return r&&(this._active=l,(a.enabled||a.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,i))),r}_getActiveElements(e,i,n,a){const s=this.options;if(e.type==="mouseout")return[];if(!a)return i.filter(o=>this.chart.data.datasets[o.datasetIndex]&&this.chart.getDatasetMeta(o.datasetIndex).controller.getParsed(o.index)!==void 0);const l=this.chart.getElementsAtEventForMode(e,s.mode,s,n);return s.reverse&&l.reverse(),l}_positionChanged(e,i){const{caretX:n,caretY:a,options:s}=this,l=bs[s.position].call(this,e,i);return l!==!1&&(n!==l.x||a!==l.y)}}Z(Wu,"positioners",bs);var Oy={id:"tooltip",_element:Wu,positioners:bs,afterInit(t,e,i){i&&(t.tooltip=new Wu({chart:t,options:i}))},beforeUpdate(t,e,i){t.tooltip&&t.tooltip.initialize(i)},reset(t,e,i){t.tooltip&&t.tooltip.initialize(i)},afterDraw(t){const e=t.tooltip;if(e&&e._willRender()){const i={tooltip:e};if(t.notifyPlugins("beforeTooltipDraw",{...i,cancelable:!0})===!1)return;e.draw(t.ctx),t.notifyPlugins("afterTooltipDraw",i)}},afterEvent(t,e){if(t.tooltip){const i=e.replay;t.tooltip.handleEvent(e.event,i,e.inChartArea)&&(e.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(t,e)=>e.bodyFont.size,boxWidth:(t,e)=>e.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:My},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:t=>t!=="filter"&&t!=="itemSort"&&t!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const bC=(t,e,i,n)=>(typeof e=="string"?(i=t.push(e)-1,n.unshift({index:i,label:e})):isNaN(e)&&(i=null),i);function xC(t,e,i,n){const a=t.indexOf(e);if(a===-1)return bC(t,e,i,n);const s=t.lastIndexOf(e);return a!==s?i:a}const mC=(t,e)=>t===null?null:_e(Math.round(t),0,e);function Mg(t){const e=this.getLabels();return t>=0&&t<e.length?e[t]:t}class or extends Qa{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const i=this._addedLabels;if(i.length){const n=this.getLabels();for(const{index:a,label:s}of i)n[a]===s&&n.splice(a,1);this._addedLabels=[]}super.init(e)}parse(e,i){if(ct(e))return null;const n=this.getLabels();return i=isFinite(i)&&n[i]===e?i:xC(n,e,P(i,e),this._addedLabels),mC(i,n.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:i}=this.getUserBounds();let{min:n,max:a}=this.getMinMax(!0);this.options.bounds==="ticks"&&(e||(n=0),i||(a=this.getLabels().length-1)),this.min=n,this.max=a}buildTicks(){const e=this.min,i=this.max,n=this.options.offset,a=[];let s=this.getLabels();s=e===0&&i===s.length-1?s:s.slice(e,i+1),this._valueRange=Math.max(s.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let l=e;l<=i;l++)a.push({value:l});return a}getLabelForValue(e){return Mg.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return typeof e!="number"&&(e=this.parse(e)),e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const i=this.ticks;return e<0||e>i.length-1?null:this.getPixelForValue(i[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}Z(or,"id","category"),Z(or,"defaults",{ticks:{callback:Mg}});function yC(t,e){const i=[],{bounds:a,step:s,min:l,max:o,precision:r,count:c,maxTicks:d,maxDigits:f,includeBounds:h}=t,g=s||1,m=d-1,{min:v,max:S}=e,b=!ct(l),x=!ct(o),y=!ct(c),_=(S-v)/(f+1);let D=zp((S-v)/m/g)*g,A,j,T,M;if(D<1e-14&&!b&&!x)return[{value:v},{value:S}];M=Math.ceil(S/D)-Math.floor(v/D),M>m&&(D=zp(M*D/m/g)*g),ct(r)||(A=Math.pow(10,r),D=Math.ceil(D*A)/A),a==="ticks"?(j=Math.floor(v/D)*D,T=Math.ceil(S/D)*D):(j=v,T=S),b&&x&&s&&MS((o-l)/s,D/1e3)?(M=Math.round(Math.min((o-l)/D,d)),D=(o-l)/M,j=l,T=o):y?(j=b?l:j,T=x?o:T,M=c-1,D=(T-j)/M):(M=(T-j)/D,Rs(M,Math.round(M),D/1e3)?M=Math.round(M):M=Math.ceil(M));const L=Math.max(Tp(D),Tp(j));A=Math.pow(10,ct(r)?L:r),j=Math.round(j*A)/A,T=Math.round(T*A)/A;let Y=0;for(b&&(h&&j!==l?(i.push({value:l}),j<l&&Y++,Rs(Math.round((j+Y*D)*A)/A,l,Og(l,_,t))&&Y++):j<l&&Y++);Y<M;++Y){const G=Math.round((j+Y*D)*A)/A;if(x&&G>o)break;i.push({value:G})}return x&&h&&T!==o?i.length&&Rs(i[i.length-1].value,o,Og(o,_,t))?i[i.length-1].value=o:i.push({value:o}):(!x||T===o)&&i.push({value:T}),i}function Og(t,e,{horizontal:i,minRotation:n}){const a=kn(n),s=(i?Math.sin(a):Math.cos(a))||.001,l=.75*e*(""+t).length;return Math.min(e/s,l)}class vC extends Qa{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,i){return ct(e)||(typeof e=="number"||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:i,maxDefined:n}=this.getUserBounds();let{min:a,max:s}=this;const l=r=>a=i?a:r,o=r=>s=n?s:r;if(e){const r=ni(a),c=ni(s);r<0&&c<0?o(0):r>0&&c>0&&l(0)}if(a===s){let r=s===0?1:Math.abs(s*.05);o(s+r),e||l(a-r)}this.min=a,this.max=s}getTickLimit(){const e=this.options.ticks;let{maxTicksLimit:i,stepSize:n}=e,a;return n?(a=Math.ceil(this.max/n)-Math.floor(this.min/n)+1,a>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${n} would result generating up to ${a} ticks. Limiting to 1000.`),a=1e3)):(a=this.computeTickLimit(),i=i||11),i&&(a=Math.min(i,a)),a}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,i=e.ticks;let n=this.getTickLimit();n=Math.max(2,n);const a={maxTicks:n,bounds:e.bounds,min:e.min,max:e.max,precision:i.precision,step:i.stepSize,count:i.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:i.minRotation||0,includeBounds:i.includeBounds!==!1},s=this._range||this,l=yC(a,s);return e.bounds==="ticks"&&OS(l,this,"value"),e.reverse?(l.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),l}configure(){const e=this.ticks;let i=this.min,n=this.max;if(super.configure(),this.options.offset&&e.length){const a=(n-i)/Math.max(e.length-1,1)/2;i-=a,n+=a}this._startValue=i,this._endValue=n,this._valueRange=n-i}getLabelForValue(e){return Wm(e,this.chart.options.locale,this.options.ticks.format)}}class rr extends vC{determineDataLimits(){const{min:e,max:i}=this.getMinMax(!0);this.min=ee(e)?e:0,this.max=ee(i)?i:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),i=e?this.width:this.height,n=kn(this.options.ticks.minRotation),a=(e?Math.sin(n):Math.cos(n))||.001,s=this._resolveTickFontOptions(0);return Math.ceil(i/Math.min(40,s.lineHeight/a))}getPixelForValue(e){return e===null?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}Z(rr,"id","linear"),Z(rr,"defaults",{ticks:{callback:Km.formatters.numeric}});const Rr={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},he=Object.keys(Rr);function Rg(t,e){return t-e}function Eg(t,e){if(ct(e))return null;const i=t._adapter,{parser:n,round:a,isoWeekday:s}=t._parseOpts;let l=e;return typeof n=="function"&&(l=n(l)),ee(l)||(l=typeof n=="string"?i.parse(l,n):i.parse(l)),l===null?null:(a&&(l=a==="week"&&(Is(s)||s===!0)?i.startOf(l,"isoWeek",s):i.startOf(l,a)),+l)}function Bg(t,e,i,n){const a=he.length;for(let s=he.indexOf(t);s<a-1;++s){const l=Rr[he[s]],o=l.steps?l.steps:Number.MAX_SAFE_INTEGER;if(l.common&&Math.ceil((i-e)/(o*l.size))<=n)return he[s]}return he[a-1]}function SC(t,e,i,n,a){for(let s=he.length-1;s>=he.indexOf(i);s--){const l=he[s];if(Rr[l].common&&t._adapter.diff(a,n,l)>=e-1)return l}return he[i?he.indexOf(i):0]}function _C(t){for(let e=he.indexOf(t)+1,i=he.length;e<i;++e)if(Rr[he[e]].common)return he[e]}function Lg(t,e,i){if(!i)t[e]=!0;else if(i.length){const{lo:n,hi:a}=Jd(i,e),s=i[n]>=e?i[n]:i[a];t[s]=!0}}function kC(t,e,i,n){const a=t._adapter,s=+a.startOf(e[0].value,n),l=e[e.length-1].value;let o,r;for(o=s;o<=l;o=+a.add(o,1,n))r=i[o],r>=0&&(e[r].major=!0);return e}function Ng(t,e,i){const n=[],a={},s=e.length;let l,o;for(l=0;l<s;++l)o=e[l],a[o]=l,n.push({value:o,major:!1});return s===0||!i?n:kC(t,n,a,i)}class cr extends Qa{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e,i={}){const n=e.time||(e.time={}),a=this._adapter=new p_._date(e.adapters.date);a.init(i),Os(n.displayFormats,a.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(e),this._normalized=i.normalized}parse(e,i){return e===void 0?null:Eg(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,i=this._adapter,n=e.time.unit||"day";let{min:a,max:s,minDefined:l,maxDefined:o}=this.getUserBounds();function r(c){!l&&!isNaN(c.min)&&(a=Math.min(a,c.min)),!o&&!isNaN(c.max)&&(s=Math.max(s,c.max))}(!l||!o)&&(r(this._getLabelBounds()),(e.bounds!=="ticks"||e.ticks.source!=="labels")&&r(this.getMinMax(!1))),a=ee(a)&&!isNaN(a)?a:+i.startOf(Date.now(),n),s=ee(s)&&!isNaN(s)?s:+i.endOf(Date.now(),n)+1,this.min=Math.min(a,s-1),this.max=Math.max(a+1,s)}_getLabelBounds(){const e=this.getLabelTimestamps();let i=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return e.length&&(i=e[0],n=e[e.length-1]),{min:i,max:n}}buildTicks(){const e=this.options,i=e.time,n=e.ticks,a=n.source==="labels"?this.getLabelTimestamps():this._generate();e.bounds==="ticks"&&a.length&&(this.min=this._userMin||a[0],this.max=this._userMax||a[a.length-1]);const s=this.min,l=this.max,o=HS(a,s,l);return this._unit=i.unit||(n.autoSkip?Bg(i.minUnit,this.min,this.max,this._getLabelCapacity(s)):SC(this,o.length,i.minUnit,this.min,this.max)),this._majorUnit=!n.major.enabled||this._unit==="year"?void 0:_C(this._unit),this.initOffsets(a),e.reverse&&o.reverse(),Ng(this,o,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(e=[]){let i=0,n=0,a,s;this.options.offset&&e.length&&(a=this.getDecimalForValue(e[0]),e.length===1?i=1-a:i=(this.getDecimalForValue(e[1])-a)/2,s=this.getDecimalForValue(e[e.length-1]),e.length===1?n=s:n=(s-this.getDecimalForValue(e[e.length-2]))/2);const l=e.length<3?.5:.25;i=_e(i,0,l),n=_e(n,0,l),this._offsets={start:i,end:n,factor:1/(i+1+n)}}_generate(){const e=this._adapter,i=this.min,n=this.max,a=this.options,s=a.time,l=s.unit||Bg(s.minUnit,i,n,this._getLabelCapacity(i)),o=P(a.ticks.stepSize,1),r=l==="week"?s.isoWeekday:!1,c=Is(r)||r===!0,d={};let f=i,h,g;if(c&&(f=+e.startOf(f,"isoWeek",r)),f=+e.startOf(f,c?"day":l),e.diff(n,i,l)>1e5*o)throw new Error(i+" and "+n+" are too far apart with stepSize of "+o+" "+l);const m=a.ticks.source==="data"&&this.getDataTimestamps();for(h=f,g=0;h<n;h=+e.add(h,o,l),g++)Lg(d,h,m);return(h===n||a.bounds==="ticks"||g===1)&&Lg(d,h,m),Object.keys(d).sort(Rg).map(v=>+v)}getLabelForValue(e){const i=this._adapter,n=this.options.time;return n.tooltipFormat?i.format(e,n.tooltipFormat):i.format(e,n.displayFormats.datetime)}format(e,i){const a=this.options.time.displayFormats,s=this._unit,l=i||a[s];return this._adapter.format(e,l)}_tickFormatFunction(e,i,n,a){const s=this.options,l=s.ticks.callback;if(l)return vt(l,[e,i,n],this);const o=s.time.displayFormats,r=this._unit,c=this._majorUnit,d=r&&o[r],f=c&&o[c],h=n[i],g=c&&f&&h&&h.major;return this._adapter.format(e,a||(g?f:d))}generateTickLabels(e){let i,n,a;for(i=0,n=e.length;i<n;++i)a=e[i],a.label=this._tickFormatFunction(a.value,i,e)}getDecimalForValue(e){return e===null?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const i=this._offsets,n=this.getDecimalForValue(e);return this.getPixelForDecimal((i.start+n)*i.factor)}getValueForPixel(e){const i=this._offsets,n=this.getDecimalForPixel(e)/i.factor-i.end;return this.min+n*(this.max-this.min)}_getLabelSize(e){const i=this.options.ticks,n=this.ctx.measureText(e).width,a=kn(this.isHorizontal()?i.maxRotation:i.minRotation),s=Math.cos(a),l=Math.sin(a),o=this._resolveTickFontOptions(0).size;return{w:n*s+o*l,h:n*l+o*s}}_getLabelCapacity(e){const i=this.options.time,n=i.displayFormats,a=n[i.unit]||n.millisecond,s=this._tickFormatFunction(e,0,Ng(this,[e],this._majorUnit),a),l=this._getLabelSize(s),o=Math.floor(this.isHorizontal()?this.width/l.w:this.height/l.h)-1;return o>0?o:1}getDataTimestamps(){let e=this._cache.data||[],i,n;if(e.length)return e;const a=this.getMatchingVisibleMetas();if(this._normalized&&a.length)return this._cache.data=a[0].controller.getAllParsedValues(this);for(i=0,n=a.length;i<n;++i)e=e.concat(a[i].controller.getAllParsedValues(this));return this._cache.data=this.normalize(e)}getLabelTimestamps(){const e=this._cache.labels||[];let i,n;if(e.length)return e;const a=this.getLabels();for(i=0,n=a.length;i<n;++i)e.push(Eg(this,a[i]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return Xm(e.sort(Rg))}}Z(cr,"id","time"),Z(cr,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function ao(t,e,i){let n=0,a=t.length-1,s,l,o,r;i?(e>=t[n].pos&&e<=t[a].pos&&({lo:n,hi:a}=Cn(t,"pos",e)),{pos:s,time:o}=t[n],{pos:l,time:r}=t[a]):(e>=t[n].time&&e<=t[a].time&&({lo:n,hi:a}=Cn(t,"time",e)),{time:s,pos:o}=t[n],{time:l,pos:r}=t[a]);const c=l-s;return c?o+(r-o)*(e-s)/c:o}class Hg extends cr{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),i=this._table=this.buildLookupTable(e);this._minPos=ao(i,this.min),this._tableRange=ao(i,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:i,max:n}=this,a=[],s=[];let l,o,r,c,d;for(l=0,o=e.length;l<o;++l)c=e[l],c>=i&&c<=n&&a.push(c);if(a.length<2)return[{time:i,pos:0},{time:n,pos:1}];for(l=0,o=a.length;l<o;++l)d=a[l+1],r=a[l-1],c=a[l],Math.round((d+r)/2)!==c&&s.push({time:c,pos:l/(o-1)});return s}_generate(){const e=this.min,i=this.max;let n=super.getDataTimestamps();return(!n.includes(e)||!n.length)&&n.splice(0,0,e),(!n.includes(i)||n.length===1)&&n.push(i),n.sort((a,s)=>a-s)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const i=this.getDataTimestamps(),n=this.getLabelTimestamps();return i.length&&n.length?e=this.normalize(i.concat(n)):e=i.length?i:n,e=this._cache.all=e,e}getDecimalForValue(e){return(ao(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const i=this._offsets,n=this.getDecimalForPixel(e)/i.factor-i.end;return ao(this._table,n*this._tableRange+this._minPos,!0)}}Z(Hg,"id","timeseries"),Z(Hg,"defaults",cr.defaults);const Ry="label";function Ug(t,e){typeof t=="function"?t(e):t&&(t.current=e)}function CC(t,e){const i=t.options;i&&e&&Object.assign(i,e)}function Ey(t,e){t.labels=e}function By(t,e){let i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Ry;const n=[];t.datasets=e.map(a=>{const s=t.datasets.find(l=>l[i]===a[i]);return!s||!a.data||n.includes(s)?{...a}:(n.push(s),Object.assign(s,a),s)})}function DC(t){let e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Ry;const i={labels:[],datasets:[]};return Ey(i,t.labels),By(i,t.datasets,e),i}function wC(t,e){const{height:i=150,width:n=300,redraw:a=!1,datasetIdKey:s,type:l,data:o,options:r,plugins:c=[],fallbackContent:d,updateMode:f,...h}=t,g=z.useRef(null),m=z.useRef(null),v=()=>{g.current&&(m.current=new vl(g.current,{type:l,data:DC(o,s),options:r&&{...r},plugins:c}),Ug(e,m.current))},S=()=>{Ug(e,null),m.current&&(m.current.destroy(),m.current=null)};return z.useEffect(()=>{!a&&m.current&&r&&CC(m.current,r)},[a,r]),z.useEffect(()=>{!a&&m.current&&Ey(m.current.config.data,o.labels)},[a,o.labels]),z.useEffect(()=>{!a&&m.current&&o.datasets&&By(m.current.config.data,o.datasets,s)},[a,o.datasets]),z.useEffect(()=>{m.current&&(a?(S(),setTimeout(v)):m.current.update(f))},[a,r,o.labels,o.datasets,f]),z.useEffect(()=>{m.current&&(S(),setTimeout(v))},[l]),z.useEffect(()=>(v(),()=>S()),[]),Dn.createElement("canvas",{ref:g,role:"img",height:i,width:n,...h},d)}const jC=z.forwardRef(wC);function Ly(t,e){return vl.register(e),z.forwardRef((i,n)=>Dn.createElement(jC,{...i,ref:n,type:t}))}const zC=Ly("line",vo),TC=Ly("bar",yo);vl.register(or,rr,Co,Ay,Oy,zy);const Yg=({data:t,config:e={}})=>{const i=z.useRef(null),n={blue:{primary:"rgba(59,130,246,0.7)",border:"#2563eb"},green:{primary:"rgba(16,185,129,0.7)",border:"#059669"},red:{primary:"rgba(239,68,68,0.7)",border:"#dc2626"},purple:{primary:"rgba(139,92,246,0.7)",border:"#7c3aed"},orange:{primary:"rgba(245,158,11,0.7)",border:"#d97706"}},a=n[e.colorScheme]||n.blue,s={labels:t.map(o=>`${o.name} - ${o.count}`),datasets:[{label:"Tickets",data:t.map(o=>o.count),backgroundColor:a.primary,borderColor:a.border,borderWidth:0,maxBarThickness:24,minBarLength:2,borderRadius:e.type==="vertical-bar"?{topLeft:4,topRight:4}:{topRight:4,bottomRight:4},borderSkipped:!1}]},l={indexAxis:e.type==="vertical-bar"?"x":"y",responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!1},title:{display:!1},tooltip:{enabled:!0,backgroundColor:"rgba(0, 0, 0, 0.8)",titleColor:"white",bodyColor:"white",borderColor:a.border,borderWidth:1}},layout:{padding:{right:8,left:8,top:8,bottom:8}},scales:{x:{beginAtZero:!0,grid:{display:e.type==="vertical-bar",color:"rgba(148,163,184,0.25)"},ticks:{color:"#64748b",font:{size:11}},border:{display:!1}},y:{grid:{display:!1},ticks:{color:"#374151",font:{size:12,weight:"500"},padding:8},border:{display:!1}}},animation:{duration:300},elements:{bar:{borderRadius:e.type==="vertical-bar"?{topLeft:4,topRight:4}:{topRight:4,bottomRight:4}}}};return u.jsx("div",{ref:i,style:{height:"300px"},children:u.jsx(TC,{data:s,options:l})})};vl.register(or,rr,ko,Wi,iC,Ay,Oy,zy);const Fg=({data:t,config:e={}})=>{const{labels:i=[],created:n=[]}=t||{},a={blue:{primary:"#3b82f6",background:"rgba(59,130,246,0.12)"},green:{primary:"#22c55e",background:"rgba(34,197,94,0.12)"},red:{primary:"#ef4444",background:"rgba(239,68,68,0.12)"},purple:{primary:"#8b5cf6",background:"rgba(139,92,246,0.12)"},orange:{primary:"#f59e0b",background:"rgba(245,158,11,0.12)"}},s=a[e.colorScheme]||a.green,l={labels:i,datasets:[{label:"Created",data:n,tension:.35,borderColor:s.primary,backgroundColor:e.showFill!==!1?s.background:"transparent",pointBackgroundColor:"#ffffff",pointBorderColor:s.primary,pointRadius:4,fill:e.showFill!==!1}]},o={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{display:!0,position:"top"},title:{display:!1},tooltip:{mode:"index",intersect:!1,callbacks:{label:r=>`${r.dataset.label}: ${r.parsed.y.toLocaleString()}`}}},interaction:{mode:"nearest",intersect:!1},scales:{y:{beginAtZero:!0,grid:{color:"rgba(148,163,184,0.25)"},ticks:{color:"#94a3b8",callback:r=>r.toLocaleString()}},x:{grid:{color:"rgba(148,163,184,0.15)"},ticks:{color:"#94a3b8"}}}};return u.jsx("div",{style:{height:"320px"},children:u.jsx(zC,{data:l,options:o})})},AC=({children:t,onLayoutChange:e,gridCols:i=3,gridRows:n=4})=>{const[a,s]=z.useState(null),[l,o]=z.useState(null),r=z.useRef(null),c=z.useCallback((m,v)=>{s(v),m.dataTransfer.effectAllowed="move",m.dataTransfer.setData("text/html",m.target.outerHTML),m.target.style.opacity="0.5"},[]),d=z.useCallback(m=>{m.target.style.opacity="1",s(null),o(null)},[]),f=z.useCallback(m=>{if(m.preventDefault(),m.dataTransfer.dropEffect="move",!r.current||!a)return;const v=r.current.getBoundingClientRect(),S=m.clientX-v.left,b=m.clientY-v.top,x=v.width/i,y=v.height/n,_=Math.floor(S/x),D=Math.floor(b/y);o({row:Math.max(0,Math.min(D,n-1)),col:Math.max(0,Math.min(_,i-1))})},[i,n,a]),h=z.useCallback(m=>{m.preventDefault(),!(!a||!l)&&(e&&e(a.id,l),s(null),o(null))},[a,l,e]),g=()=>{if(!a)return null;const m=[];for(let v=0;v<n;v++)for(let S=0;S<i;S++){const b=l&&l.row===v&&l.col===S;m.push(u.jsx("div",{style:{position:"absolute",left:`${S/i*100}%`,top:`${v/n*100}%`,width:`${100/i}%`,height:`${100/n}%`,border:"2px dashed rgba(59, 130, 246, 0.3)",backgroundColor:b?"rgba(59, 130, 246, 0.1)":"transparent",transition:"background-color 0.2s ease",pointerEvents:"none",zIndex:1e3}},`${v}-${S}`))}return m};return u.jsxs("div",{ref:r,style:{position:"relative",width:"100%",height:"100%",minHeight:"600px"},onDragOver:f,onDrop:h,children:[g(),Dn.Children.map(t,(m,v)=>{var S;return Dn.isValidElement(m)?Dn.cloneElement(m,{draggable:!0,onDragStart:b=>c(b,m.props.item||{id:`item-${v}`}),onDragEnd:d,style:{...m.props.style,cursor:"move",transition:"all 0.3s ease",transform:(a==null?void 0:a.id)===(((S=m.props.item)==null?void 0:S.id)||`item-${v}`)?"scale(0.95)":"scale(1)"}}):m})]})},Ny=({config:t,onConfigChange:e,theme:i,dashboardData:n,getAllAssignees:a})=>{const[s,l]=z.useState(!1),[o,r]=z.useState(null),[c,d]=z.useState("all"),[f,h]=z.useState(""),[g,m]=z.useState(""),[v,S]=z.useState(""),[b,x]=z.useState(""),[y,_]=z.useState([]),[D,A]=z.useState(""),[j,T]=z.useState(!1),[M,L]=z.useState([]);Dn.useEffect(()=>{(async()=>{if(a&&typeof a=="function"){const N=a();if(N&&N.length>0)return}T(!0);try{const N=await fetch("/api/dashboard/assignees");if(N.ok){const F=await N.json();F.success&&F.assignees&&L(F.assignees.map(ht=>({id:ht.id||ht.name.toLowerCase().replace(/\s+/g,"."),name:ht.name,email:ht.email||`${ht.name.toLowerCase().replace(/\s+/g,".")}@company.com`})))}}catch{console.log("Could not fetch assignees from API, using fallback data")}finally{T(!1)}})()},[a]);const Y=Dn.useMemo(()=>{if(a&&typeof a=="function"){const R=a();if(R&&R.length>0)return R.map(N=>({id:N.key||N.displayName.toLowerCase().replace(/\s+/g,"."),name:N.displayName,email:N.emailAddress||`${N.displayName.toLowerCase().replace(/\s+/g,".")}@company.com`}))}return M.length>0?M:[{id:"john.doe",name:"John Doe",email:"<EMAIL>"},{id:"jane.smith",name:"Jane Smith",email:"<EMAIL>"},{id:"mike.johnson",name:"Mike Johnson",email:"<EMAIL>"},{id:"sarah.wilson",name:"Sarah Wilson",email:"<EMAIL>"},{id:"david.brown",name:"David Brown",email:"<EMAIL>"}]},[a,M]),G=Y.filter(R=>R.name.toLowerCase().includes(D.toLowerCase())||R.email.toLowerCase().includes(D.toLowerCase())),mt=()=>{if(!v.trim())return;const R=y.map(F=>{const ht=Y.find(ie=>ie.id===F);return ht?ht.name:F}),N={id:`group-${Date.now()}`,name:v.trim(),assignees:R,assigneeIds:[...y],description:b.trim()};console.log("Creating assignee group:",N),console.log("Current config:",t),e({...t,assigneeGroups:[...t.assigneeGroups||[],N]}),S(""),x(""),_([]),l(!1)},wt=()=>{if(!o||!v.trim())return;const R=y.map(N=>{const F=Y.find(ht=>ht.id===N);return F?F.name:N});e({...t,assigneeGroups:t.assigneeGroups.map(N=>N.id===o.id?{...N,name:v.trim(),description:b.trim(),assignees:R,assigneeIds:[...y]}:N)}),r(null),S(""),x(""),_([])},ot=R=>{confirm("Are you sure you want to delete this assignee group?")&&e({...t,assigneeGroups:t.assigneeGroups.filter(N=>N.id!==R)})},H=R=>{var ht;const N=(ht=t.assigneeGroups)==null?void 0:ht.find(ie=>ie.id===R);if(!N)return;console.log("Applying assignee group to all:",N),console.log("Group assignees:",N.assignees);const F={...t};F.statusCards&&(F.statusCards=F.statusCards.map(ie=>({...ie,assigneeFilter:"specific",selectedAssignees:[...N.assignees]}))),F.priorityCards&&(F.priorityCards=F.priorityCards.map(ie=>({...ie,assigneeFilter:"specific",selectedAssignees:[...N.assignees]}))),F.organizationCards&&(F.organizationCards=F.organizationCards.map(ie=>({...ie,assigneeFilter:"specific",selectedAssignees:[...N.assignees]}))),F.charts&&Object.keys(F.charts).forEach(ie=>{F.charts[ie]={...F.charts[ie],assigneeFilter:"specific",selectedAssignees:[...N.assignees]}}),e(F),alert(`Applied "${N.name}" assignee group to all cards and charts!`)},V=()=>{if(confirm("Are you sure you want to clear all assignee filters from cards and charts?")){const R={...t};R.statusCards&&(R.statusCards=R.statusCards.map(N=>({...N,assigneeFilter:"all",selectedAssignees:[]}))),R.priorityCards&&(R.priorityCards=R.priorityCards.map(N=>({...N,assigneeFilter:"all",selectedAssignees:[]}))),R.organizationCards&&(R.organizationCards=R.organizationCards.map(N=>({...N,assigneeFilter:"all",selectedAssignees:[]}))),R.charts&&Object.keys(R.charts).forEach(N=>{R.charts[N]={...R.charts[N],assigneeFilter:"all",selectedAssignees:[]}}),e(R),alert("Cleared all assignee filters!")}},U=()=>{if(!c)return;if(c==="custom"){if(!f||!g){alert("Please select both start and end dates for custom range.");return}if(new Date(f)>new Date(g)){alert("Start date must be before end date.");return}}const R={dateFilter:c,customDateFrom:c==="custom"?f:"",customDateTo:c==="custom"?g:""},N={...t};N.statusCards&&(N.statusCards=N.statusCards.map(ht=>({...ht,...R}))),N.priorityCards&&(N.priorityCards=N.priorityCards.map(ht=>({...ht,...R}))),N.organizationCards&&(N.organizationCards=N.organizationCards.map(ht=>({...ht,...R}))),N.charts&&Object.keys(N.charts).forEach(ht=>{N.charts[ht]={...N.charts[ht],...R}}),console.log("Applying date range to all:",R),console.log("Updated config:",N),e(N);const F=at(c,f,g);alert(`Applied "${F}" date range to all cards and charts!`)},rt=()=>{if(confirm("Are you sure you want to clear all date filters from cards and charts?")){const R={...t},N={dateFilter:"all",customDateFrom:"",customDateTo:""};R.statusCards&&(R.statusCards=R.statusCards.map(F=>({...F,...N}))),R.priorityCards&&(R.priorityCards=R.priorityCards.map(F=>({...F,...N}))),R.organizationCards&&(R.organizationCards=R.organizationCards.map(F=>({...F,...N}))),R.charts&&Object.keys(R.charts).forEach(F=>{R.charts[F]={...R.charts[F],...N}}),e(R),alert("Cleared all date filters!")}},at=(R,N,F)=>{switch(R){case"today":return"Today";case"yesterday":return"Yesterday";case"last7days":return"Last 7 Days";case"last30days":return"Last 30 Days";case"last90days":return"Last 90 Days";case"thisweek":return"This Week";case"thismonth":return"This Month";case"thisyear":return"This Year";case"lastyear":return"Last Year";case"custom":return N&&F?`${N} to ${F}`:"Custom Range";default:return"All Time"}},At=R=>{r(R),S(R.name),x(R.description||"");const N=R.assigneeIds||R.assignees.map(F=>{const ht=Y.find(ie=>ie.name===F);return ht?ht.id:F});_([...N]),l(!0)},Mt=()=>{r(null),S(""),x(""),_([]),l(!1)},le=R=>{_(N=>N.includes(R)?N.filter(F=>F!==R):[...N,R])};return u.jsxs("div",{style:{marginBottom:"24px"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsx("h4",{style:{margin:0,color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"16px",fontWeight:"600"},children:"👥 Assignee Groups"}),u.jsxs("div",{style:{display:"flex",gap:"8px"},children:[u.jsx("button",{onClick:()=>l(!0),style:{padding:"6px 12px",backgroundColor:"#10b981",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px",fontWeight:"500"},children:"➕ Create Group"}),u.jsx("button",{onClick:V,style:{padding:"6px 12px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px",fontWeight:"500"},children:"🗑️ Clear All Filters"})]})]}),u.jsx("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap",marginBottom:"16px"},children:(t.assigneeGroups||[]).map(R=>{var N;return u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",padding:"8px 12px",backgroundColor:i==="dark"?"#374151":"#f3f4f6",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"8px"},children:[u.jsxs("div",{style:{flex:1},children:[u.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:R.name}),u.jsxs("div",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#6b7280"},children:[((N=R.assignees)==null?void 0:N.length)||0," assignees"]})]}),u.jsx("button",{onClick:()=>H(R.id),style:{padding:"4px 8px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"11px"},title:"Apply to all cards and charts",children:"Apply"}),u.jsx("button",{onClick:()=>At(R),style:{padding:"4px 8px",backgroundColor:"#f59e0b",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"11px"},children:"Edit"}),u.jsx("button",{onClick:()=>ot(R.id),style:{padding:"4px 8px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"11px"},children:"Delete"})]},R.id)})}),u.jsxs("div",{style:{marginTop:"32px",marginBottom:"24px"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsx("h4",{style:{margin:0,color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"16px",fontWeight:"600"},children:"📅 Global Date Range"}),u.jsxs("div",{style:{display:"flex",gap:"8px"},children:[u.jsx("button",{onClick:U,disabled:!c||c==="all"||c==="custom"&&(!f||!g),style:{padding:"6px 12px",backgroundColor:!c||c==="all"||c==="custom"&&(!f||!g)?"#9ca3af":"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:!c||c==="all"||c==="custom"&&(!f||!g)?"not-allowed":"pointer",fontSize:"12px",fontWeight:"500"},children:"📊 Apply to All"}),u.jsx("button",{onClick:rt,style:{padding:"6px 12px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"12px",fontWeight:"500"},children:"🗑️ Clear All Dates"})]})]}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:c==="custom"?"1fr 1fr 1fr":"1fr",gap:"12px",padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`,borderRadius:"8px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{display:"block",marginBottom:"6px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"Select Date Range"}),u.jsxs("select",{value:c,onChange:R=>{d(R.target.value),R.target.value!=="custom"&&(h(""),m(""))},style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[u.jsx("option",{value:"all",children:"All Time"}),u.jsx("option",{value:"today",children:"Today"}),u.jsx("option",{value:"yesterday",children:"Yesterday"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"thisweek",children:"This Week"}),u.jsx("option",{value:"thismonth",children:"This Month"}),u.jsx("option",{value:"thisyear",children:"This Year"}),u.jsx("option",{value:"lastyear",children:"Last Year"}),u.jsx("option",{value:"custom",children:"Custom Range"})]})]}),c==="custom"&&u.jsxs(u.Fragment,{children:[u.jsxs("div",{children:[u.jsx("label",{style:{display:"block",marginBottom:"6px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"From Date"}),u.jsx("input",{type:"date",value:f,onChange:R=>h(R.target.value),style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{display:"block",marginBottom:"6px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"To Date"}),u.jsx("input",{type:"date",value:g,onChange:R=>m(R.target.value),style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]})]})]}),c&&c!=="all"&&u.jsxs("div",{style:{marginTop:"12px",padding:"12px",backgroundColor:i==="dark"?"#1f2937":"#f0f9ff",border:`1px solid ${i==="dark"?"#374151":"#bae6fd"}`,borderRadius:"6px"},children:[u.jsxs("div",{style:{fontSize:"14px",color:i==="dark"?"#e5e7eb":"#1f2937",fontWeight:"500"},children:["📅 Selected Range: ",at(c,f,g)]}),u.jsx("div",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#6b7280",marginTop:"4px"},children:"This will be applied to all status cards, priority cards, organization cards, and charts"})]})]}),s&&u.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e4},children:u.jsxs("div",{style:{backgroundColor:i==="dark"?"#1f2937":"white",padding:"24px",borderRadius:"12px",maxWidth:"600px",width:"90%",maxHeight:"80vh",overflow:"auto"},children:[u.jsx("h3",{style:{margin:"0 0 20px 0",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"18px"},children:o?"Edit Assignee Group":"Create Assignee Group"}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{display:"block",marginBottom:"4px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"Group Name *"}),u.jsx("input",{type:"text",value:v,onChange:R=>S(R.target.value),placeholder:"Enter group name...",style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{display:"block",marginBottom:"4px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"Description"}),u.jsx("textarea",{value:b,onChange:R=>x(R.target.value),placeholder:"Enter group description...",rows:3,style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",resize:"vertical"}})]}),u.jsxs("div",{style:{marginBottom:"20px"},children:[u.jsxs("label",{style:{display:"block",marginBottom:"8px",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:["Select Assignees (",y.length," selected)"]}),u.jsx("input",{type:"text",value:D,onChange:R=>A(R.target.value),placeholder:"Search assignees...",style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",marginBottom:"12px"}}),u.jsx("div",{style:{maxHeight:"200px",overflow:"auto",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",padding:"8px"},children:j?u.jsxs("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"40px",color:i==="dark"?"#9ca3af":"#6b7280"},children:[u.jsx("div",{style:{marginRight:"8px"},children:"🔄"}),"Loading assignees..."]}):G.length===0?u.jsx("div",{style:{display:"flex",alignItems:"center",justifyContent:"center",padding:"40px",color:i==="dark"?"#9ca3af":"#6b7280"},children:D?"No assignees match your search":"No assignees available"}):G.map(R=>u.jsxs("label",{style:{display:"flex",alignItems:"center",padding:"8px",cursor:"pointer",borderRadius:"4px",backgroundColor:y.includes(R.id)?i==="dark"?"#374151":"#f3f4f6":"transparent"},children:[u.jsx("input",{type:"checkbox",checked:y.includes(R.id),onChange:()=>le(R.id),style:{marginRight:"8px"}}),u.jsxs("div",{children:[u.jsx("div",{style:{fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1f2937"},children:R.name}),u.jsx("div",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#6b7280"},children:R.email})]})]},R.id))})]}),u.jsxs("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end"},children:[u.jsx("button",{onClick:Mt,style:{padding:"8px 16px",backgroundColor:i==="dark"?"#4b5563":"#f3f4f6",color:i==="dark"?"#e5e7eb":"#1f2937",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px"},children:"Cancel"}),u.jsx("button",{onClick:o?wt:mt,disabled:!v.trim(),style:{padding:"8px 16px",backgroundColor:v.trim()?"#10b981":"#9ca3af",color:"white",border:"none",borderRadius:"6px",cursor:v.trim()?"pointer":"not-allowed",fontSize:"14px"},children:o?"Update Group":"Create Group"})]})]})})]})},MC=({config:t,onConfigChange:e,theme:i="light",dashboardData:n,getAllAssignees:a})=>{const[s,l]=z.useState(""),[o,r]=z.useState(null),[c,d]=z.useState(!1),[f,h]=z.useState(""),g=z.useCallback(()=>{const b={version:"1.0",timestamp:new Date().toISOString(),config:t},x=new Blob([JSON.stringify(b,null,2)],{type:"application/json"}),y=URL.createObjectURL(x),_=document.createElement("a");_.href=y,_.download=`jira-dashboard-config-${new Date().toISOString().slice(0,10)}.json`,document.body.appendChild(_),_.click(),document.body.removeChild(_),URL.revokeObjectURL(y)},[t]),m=z.useCallback(()=>{try{const b=JSON.parse(f);b.config?(e(b.config),d(!1),h(""),alert("Configuration imported successfully!")):alert("Invalid configuration format")}catch(b){alert("Error parsing configuration: "+b.message)}},[f,e]),v=z.useCallback(b=>{var y;const x=(y=t.presets)==null?void 0:y.find(_=>_.id===b);x&&x.config&&(e({...t,...x.config}),r(b))},[t,e]);z.useCallback((b,x,y="")=>{const _={id:`group-${Date.now()}`,name:b,assignees:x,description:y},D={...t,assigneeGroups:[...t.assigneeGroups||[],_]};e(D)},[t,e]),z.useCallback(b=>{var _;const x=(_=t.assigneeGroups)==null?void 0:_.find(D=>D.id===b);if(!x)return;const y={...t};y.statusCards&&(y.statusCards=y.statusCards.map(D=>({...D,assigneeFilter:"specific",selectedAssignees:x.assignees}))),y.priorityCards&&(y.priorityCards=y.priorityCards.map(D=>({...D,assigneeFilter:"specific",selectedAssignees:x.assignees}))),y.organizationCards&&(y.organizationCards=y.organizationCards.map(D=>({...D,assigneeFilter:"specific",selectedAssignees:x.assignees}))),y.charts&&Object.keys(y.charts).forEach(D=>{y.charts[D]={...y.charts[D],assigneeFilter:"specific",selectedAssignees:x.assignees}}),e(y)},[t,e]);const S=z.useCallback(()=>{if(confirm("Are you sure you want to reset all configurations to defaults? This cannot be undone.")){const b={charts:{organizationChart:{id:"organizationChart",title:"Tickets by Restaurant",type:"horizontal-bar",visible:!0,position:{row:1,col:1},size:{width:1,height:1},dataSource:"organizations",limit:10,colorScheme:"blue",showValues:!0,dateFilter:"all",assigneeFilter:"all",selectedAssignees:[]},weeklyChart:{id:"weeklyChart",title:"Tickets by Day (Last 7 Days)",type:"line",visible:!0,position:{row:1,col:2},size:{width:1,height:1},dataSource:"created",period:7,colorScheme:"green",showFill:!0,dateFilter:"all",assigneeFilter:"all",selectedAssignees:[]}},assigneeGroups:[],presets:t.presets||[],statusCards:t.statusCards||[],priorityCards:t.priorityCards||[],organizationCards:t.organizationCards||[]};e(b),r(null)}},[t,e]);return(t.statusCards||[]).filter(b=>{var x;return b.title.toLowerCase().includes(s.toLowerCase())||((x=b.jiraStatuses)==null?void 0:x.some(y=>y.toLowerCase().includes(s.toLowerCase())))}),(t.priorityCards||[]).filter(b=>{var x;return b.title.toLowerCase().includes(s.toLowerCase())||((x=b.jiraPriorities)==null?void 0:x.some(y=>y.toLowerCase().includes(s.toLowerCase())))}),u.jsxs("div",{style:{padding:"20px",backgroundColor:i==="dark"?"#1f2937":"#ffffff",borderRadius:"12px",border:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`},children:[u.jsx("h3",{style:{margin:"0 0 20px 0",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"18px",fontWeight:"600"},children:"🚀 Intelligent Configuration Manager"}),u.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"24px",flexWrap:"wrap"},children:[u.jsx("button",{onClick:g,style:{padding:"8px 16px",backgroundColor:"#3b82f6",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px"},children:"📤 Export Config"}),u.jsx("button",{onClick:()=>d(!0),style:{padding:"8px 16px",backgroundColor:"#10b981",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px"},children:"📥 Import Config"}),u.jsx("button",{onClick:S,style:{padding:"8px 16px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px"},children:"🔄 Reset to Defaults"})]}),u.jsx("div",{style:{marginBottom:"20px"},children:u.jsx("input",{type:"text",placeholder:"🔍 Search statuses, priorities, organizations...",value:s,onChange:b=>l(b.target.value),style:{width:"100%",padding:"10px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})}),u.jsxs("div",{style:{marginBottom:"24px"},children:[u.jsx("h4",{style:{margin:"0 0 12px 0",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"16px"},children:"📋 Quick Presets"}),u.jsx("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:(t.presets||[]).map(b=>u.jsx("button",{onClick:()=>v(b.id),style:{padding:"6px 12px",backgroundColor:o===b.id?"#3b82f6":i==="dark"?"#4b5563":"#f3f4f6",color:o===b.id?"white":i==="dark"?"#e5e7eb":"#1f2937",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:b.name},b.id))})]}),u.jsx(Ny,{config:t,onConfigChange:e,theme:i,dashboardData:n,getAllAssignees:a}),c&&u.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e4},children:u.jsxs("div",{style:{backgroundColor:i==="dark"?"#1f2937":"white",padding:"24px",borderRadius:"12px",maxWidth:"500px",width:"90%"},children:[u.jsx("h3",{style:{margin:"0 0 16px 0",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"Import Configuration"}),u.jsx("textarea",{value:f,onChange:b=>h(b.target.value),placeholder:"Paste your configuration JSON here...",style:{width:"100%",height:"200px",padding:"12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px",fontFamily:"monospace",resize:"vertical"}}),u.jsxs("div",{style:{display:"flex",gap:"12px",marginTop:"16px",justifyContent:"flex-end"},children:[u.jsx("button",{onClick:()=>d(!1),style:{padding:"8px 16px",backgroundColor:i==="dark"?"#4b5563":"#f3f4f6",color:i==="dark"?"#e5e7eb":"#1f2937",border:"none",borderRadius:"6px",cursor:"pointer"},children:"Cancel"}),u.jsx("button",{onClick:m,style:{padding:"8px 16px",backgroundColor:"#10b981",color:"white",border:"none",borderRadius:"6px",cursor:"pointer"},children:"Import"})]})]})})]})},Gc=z.memo(({card:t,cardType:e,theme:i,updateCardConfig:n,getAllAssignees:a,availableStatuses:s,availablePriorities:l,availableOrganizations:o})=>{const r=z.useCallback(c=>{n(e,t.id,c)},[n,e,t.id]);return u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`,marginBottom:"12px"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[u.jsx("input",{type:"checkbox",checked:t.visible!==!1,onChange:c=>r({visible:c.target.checked}),style:{transform:"scale(1.2)"}}),u.jsx("input",{type:"text",placeholder:"Card Title",value:t.title||"",onChange:c=>r({title:c.target.value}),style:{flex:1,padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}}),u.jsx("input",{type:"color",value:t.color||"#3b82f6",onChange:c=>r({color:c.target.value}),style:{width:"40px",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]}),u.jsxs("div",{style:{display:"grid",gap:"12px",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))"},children:[e==="status"&&u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Jira Statuses"}),u.jsx("select",{multiple:!0,value:t.jiraStatuses||[],onChange:c=>r({jiraStatuses:Array.from(c.target.selectedOptions,d=>d.value)}),style:{width:"100%",minHeight:"80px",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:s.map(c=>u.jsx("option",{value:c,children:c},c))})]}),e==="priority"&&u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Jira Priorities"}),u.jsx("select",{multiple:!0,value:t.jiraPriorities||[],onChange:c=>r({jiraPriorities:Array.from(c.target.selectedOptions,d=>d.value)}),style:{width:"100%",minHeight:"80px",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:l.map(c=>u.jsx("option",{value:c,children:c},c))})]}),e==="organization"&&u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Organizations"}),u.jsx("select",{multiple:!0,value:t.organizationNames||[],onChange:c=>r({organizationNames:Array.from(c.target.selectedOptions,d=>d.value)}),style:{width:"100%",minHeight:"80px",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:o.map(c=>u.jsx("option",{value:c,children:c},c))})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Assignee Filter"}),u.jsxs("select",{value:t.assigneeFilter||"all",onChange:c=>r({assigneeFilter:c.target.value,selectedAssignees:c.target.value!=="specific"?[]:t.selectedAssignees}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Assignees"}),u.jsx("option",{value:"unassigned",children:"Unassigned Only"}),u.jsx("option",{value:"specific",children:"Specific Assignees"})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Date Filter"}),u.jsxs("select",{value:t.dateFilter||"all",onChange:c=>r({dateFilter:c.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Time"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"thisyear",children:"This Year"}),u.jsx("option",{value:"lastyear",children:"Last Year"}),u.jsx("option",{value:"custom",children:"Custom Range"})]})]})]}),t.assigneeFilter==="specific"&&u.jsxs("div",{style:{marginTop:"12px"},children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Select Assignees"}),u.jsx("div",{style:{maxHeight:"120px",overflowY:"auto",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",padding:"4px"},children:a().map(c=>u.jsxs("label",{style:{display:"flex",alignItems:"center",padding:"4px 8px",cursor:"pointer",fontSize:"11px",color:i==="dark"?"#e5e7eb":"#1f2937",borderRadius:"2px"},children:[u.jsx("input",{type:"checkbox",checked:(t.selectedAssignees||[]).includes(c.displayName),onChange:d=>{const f=t.selectedAssignees||[],h=d.target.checked?[...f,c.displayName]:f.filter(g=>g!==c.displayName);r({selectedAssignees:h})},style:{marginRight:"8px",transform:"scale(0.9)"}}),u.jsxs("div",{children:[u.jsx("div",{style:{fontWeight:"500"},children:c.displayName}),c.emailAddress&&u.jsx("div",{style:{fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:c.emailAddress})]})]},c.key))})]}),t.dateFilter==="custom"&&u.jsxs("div",{style:{marginTop:"12px",display:"grid",gap:"8px",gridTemplateColumns:"1fr 1fr"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"From Date"}),u.jsx("input",{type:"date",value:t.customDateFrom||"",onChange:c=>r({customDateFrom:c.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"To Date"}),u.jsx("input",{type:"date",value:t.customDateTo||"",onChange:c=>r({customDateTo:c.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]})]})]})}),OC=({config:t,onConfigChange:e,theme:i="light",dashboardData:n,activeConfigTab:a,setActiveConfigTab:s,updateCardConfig:l,addNewCard:o,getAllAssignees:r})=>{const[c,d]=z.useState(""),{availableStatuses:f,availablePriorities:h,availableOrganizations:g}=z.useMemo(()=>{const b=n.tickets||[],x=[...new Set(b.map(D=>{var A,j;return D.status||((j=(A=D.fields)==null?void 0:A.status)==null?void 0:j.name)||""}).filter(Boolean))].sort(),y=[...new Set(b.map(D=>{var A,j;return D.priority||((j=(A=D.fields)==null?void 0:A.priority)==null?void 0:j.name)||""}).filter(Boolean))].sort(),_=[...new Set(b.map(D=>{var j,T,M;const A=((T=(j=D.fields)==null?void 0:j.customfield_10002)==null?void 0:T.value)||((M=D.fields)==null?void 0:M.customfield_10002)||D.organization||"Unknown";return typeof A=="string"?A:(A==null?void 0:A.value)||"Unknown"}).filter(D=>D&&D!=="Unknown"))].sort();return{availableStatuses:x,availablePriorities:y,availableOrganizations:_}},[n.tickets]),m=z.useMemo(()=>(t.statusCards||[]).filter(b=>{var x,y;return((x=b.title)==null?void 0:x.toLowerCase().includes(c.toLowerCase()))||((y=b.jiraStatuses)==null?void 0:y.some(_=>_.toLowerCase().includes(c.toLowerCase())))}),[t.statusCards,c]),v=z.useMemo(()=>(t.priorityCards||[]).filter(b=>{var x,y;return((x=b.title)==null?void 0:x.toLowerCase().includes(c.toLowerCase()))||((y=b.jiraPriorities)==null?void 0:y.some(_=>_.toLowerCase().includes(c.toLowerCase())))}),[t.priorityCards,c]),S=z.useMemo(()=>(t.organizationCards||[]).filter(b=>{var x,y;return((x=b.title)==null?void 0:x.toLowerCase().includes(c.toLowerCase()))||((y=b.organizationNames)==null?void 0:y.some(_=>_.toLowerCase().includes(c.toLowerCase())))}),[t.organizationCards,c]);return u.jsxs("div",{style:{padding:"20px",backgroundColor:i==="dark"?"#1f2937":"#ffffff",borderRadius:"12px",border:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`},children:[u.jsx("h3",{style:{margin:"0 0 20px 0",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"18px",fontWeight:"600"},children:"⚙️ Optimized Configuration Manager"}),u.jsx("div",{style:{marginBottom:"20px"},children:u.jsx("input",{type:"text",placeholder:"🔍 Search cards...",value:c,onChange:b=>d(b.target.value),style:{width:"100%",padding:"10px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})}),u.jsx("div",{style:{display:"flex",gap:"4px",marginBottom:"20px",borderBottom:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`},children:[{id:"status",label:"📊 Status Cards",icon:"📊"},{id:"priority",label:"⚡ Priority Cards",icon:"⚡"},{id:"organization",label:"🏢 Organization Cards",icon:"🏢"},{id:"general",label:"⚙️ General Settings",icon:"⚙️"}].map(b=>u.jsx("button",{onClick:()=>s(b.id),style:{background:"none",border:"none",padding:"12px 16px",cursor:"pointer",fontSize:"14px",fontWeight:a===b.id?"600":"400",color:a===b.id?i==="dark"?"#60a5fa":"#2563eb":i==="dark"?"#9ca3af":"#64748b",borderBottom:a===b.id?`2px solid ${i==="dark"?"#60a5fa":"#2563eb"}`:"2px solid transparent",transition:"all 0.2s ease"},children:b.label},b.id))}),u.jsxs("div",{style:{maxHeight:"60vh",overflowY:"auto",paddingRight:"8px"},children:[a==="status"&&u.jsxs("div",{children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsxs("h4",{style:{margin:0,fontSize:"16px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1e293b"},children:["Status Cards (",m.length,")"]}),u.jsx("button",{onClick:()=>o("status"),style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px",borderRadius:"4px",border:"none",cursor:"pointer"},children:"+ Add Card"})]}),m.map(b=>u.jsx(Gc,{card:b,cardType:"status",theme:i,updateCardConfig:l,getAllAssignees:r,availableStatuses:f},b.id))]}),a==="priority"&&u.jsxs("div",{children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsxs("h4",{style:{margin:0,fontSize:"16px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1e293b"},children:["Priority Cards (",v.length,")"]}),u.jsx("button",{onClick:()=>o("priority"),style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px",borderRadius:"4px",border:"none",cursor:"pointer"},children:"+ Add Card"})]}),v.map(b=>u.jsx(Gc,{card:b,cardType:"priority",theme:i,updateCardConfig:l,getAllAssignees:r,availablePriorities:h},b.id))]}),a==="organization"&&u.jsxs("div",{children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsxs("h4",{style:{margin:0,fontSize:"16px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#1e293b"},children:["Organization Cards (",S.length,")"]}),u.jsx("button",{onClick:()=>o("organization"),style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px",borderRadius:"4px",border:"none",cursor:"pointer"},children:"+ Add Card"})]}),S.map(b=>u.jsx(Gc,{card:b,cardType:"organization",theme:i,updateCardConfig:l,getAllAssignees:r,availableOrganizations:g},b.id))]}),a==="general"&&u.jsx(Ny,{config:t,onConfigChange:e,theme:i,dashboardData:n,getAllAssignees:r})]})]})},RC=({config:t,onConfigChange:e,theme:i="light",dashboardData:n,autoApply:a=!0,onAutoApplyChange:s})=>{var x,y,_,D,A,j,T;const[l,o]=z.useState(t),[r,c]=z.useState("global"),[d,f]=z.useState("");z.useEffect(()=>{a&&e(l)},[l,a,e]),z.useEffect(()=>{o(t)},[t]);const h=z.useCallback((M,L)=>{o(Y=>({...Y,charts:{...Y.charts,[M]:{...Y.charts[M],...L}}}))},[]),g=z.useCallback(M=>{o(L=>({...L,globalChartSettings:{...L.globalChartSettings,...M}}))},[]),m=z.useCallback(M=>{o(L=>({...L,charts:Object.keys(L.charts||{}).reduce((Y,G)=>(Y[G]={...L.charts[G],...M},Y),{})}))},[]),v=z.useCallback(M=>{confirm("Reset this chart to default settings?")&&h(M,{visible:!0,colorScheme:"blue",showValues:!0,dateFilter:"all",assigneeFilter:"all",selectedAssignees:[]})},[h]),S=l.charts||{},b=Object.values(S).filter(M=>M.title.toLowerCase().includes(d.toLowerCase()));return u.jsxs("div",{style:{padding:"20px",backgroundColor:i==="dark"?"#1f2937":"#ffffff",borderRadius:"12px",border:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`,maxHeight:"80vh",overflow:"hidden",display:"flex",flexDirection:"column"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",paddingBottom:"16px",borderBottom:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`},children:[u.jsx("h3",{style:{margin:0,color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"20px",fontWeight:"600"},children:"📊 Enhanced Chart Configuration"}),u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:[u.jsxs("label",{style:{display:"flex",alignItems:"center",gap:"8px",fontSize:"14px"},children:[u.jsx("input",{type:"checkbox",checked:a,onChange:M=>s==null?void 0:s(M.target.checked),style:{transform:"scale(1.1)"}}),u.jsx("span",{style:{color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Auto-Apply"})]}),!a&&u.jsx("button",{onClick:()=>e(l),style:{backgroundColor:"#10b981",color:"white",padding:"8px 16px",borderRadius:"6px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Apply Changes"})]})]}),u.jsx("div",{style:{marginBottom:"16px"},children:u.jsx("input",{type:"text",placeholder:"🔍 Search charts...",value:d,onChange:M=>f(M.target.value),style:{width:"100%",padding:"10px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})}),u.jsx("div",{style:{display:"flex",gap:"4px",marginBottom:"20px",borderBottom:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`},children:[{id:"global",label:"🌐 Global Settings",icon:"🌐"},{id:"individual",label:"📊 Individual Charts",icon:"📊"},{id:"presets",label:"⚡ Quick Presets",icon:"⚡"}].map(M=>u.jsx("button",{onClick:()=>c(M.id),style:{background:"none",border:"none",padding:"12px 16px",cursor:"pointer",fontSize:"14px",fontWeight:r===M.id?"600":"400",color:r===M.id?i==="dark"?"#60a5fa":"#2563eb":i==="dark"?"#9ca3af":"#64748b",borderBottom:r===M.id?`2px solid ${i==="dark"?"#60a5fa":"#2563eb"}`:"2px solid transparent",transition:"all 0.2s ease"},children:M.label},M.id))}),u.jsxs("div",{style:{flex:1,overflow:"auto"},children:[r==="global"&&u.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"20px"},children:u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsx("h4",{style:{margin:"0 0 12px 0",color:i==="dark"?"#e5e7eb":"#1e293b",fontSize:"16px",fontWeight:"500"},children:"📅 Global Date Filter"}),u.jsxs("div",{style:{display:"grid",gap:"12px",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Default Time Period"}),u.jsxs("select",{value:((x=l.globalChartSettings)==null?void 0:x.defaultDateFilter)||"all",onChange:M=>g({defaultDateFilter:M.target.value}),style:{width:"100%",padding:"8px 12px",borderRadius:"6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[u.jsx("option",{value:"all",children:"All Time"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"thisyear",children:"This Year"}),u.jsx("option",{value:"lastyear",children:"Last Year"}),u.jsx("option",{value:"custom",children:"Custom Date Range"})]})]}),((y=l.globalChartSettings)==null?void 0:y.defaultDateFilter)==="custom"&&u.jsxs(u.Fragment,{children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Start Date"}),u.jsx("input",{type:"date",value:((_=l.globalChartSettings)==null?void 0:_.customStartDate)||"",onChange:M=>g({customStartDate:M.target.value}),style:{width:"100%",padding:"8px 12px",borderRadius:"6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"End Date"}),u.jsx("input",{type:"date",value:((D=l.globalChartSettings)==null?void 0:D.customEndDate)||"",onChange:M=>g({customEndDate:M.target.value}),style:{width:"100%",padding:"8px 12px",borderRadius:"6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Default Color Scheme"}),u.jsxs("select",{value:((A=l.globalChartSettings)==null?void 0:A.defaultColorScheme)||"blue",onChange:M=>g({defaultColorScheme:M.target.value}),style:{width:"100%",padding:"8px 12px",borderRadius:"6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[u.jsx("option",{value:"blue",children:"Blue"}),u.jsx("option",{value:"green",children:"Green"}),u.jsx("option",{value:"purple",children:"Purple"}),u.jsx("option",{value:"orange",children:"Orange"}),u.jsx("option",{value:"red",children:"Red"})]})]})]}),u.jsxs("div",{style:{marginTop:"12px",display:"flex",gap:"8px",flexWrap:"wrap"},children:[u.jsx("button",{onClick:()=>{var M,L,Y,G,mt;return m({dateFilter:((M=l.globalChartSettings)==null?void 0:M.defaultDateFilter)||"all",colorScheme:((L=l.globalChartSettings)==null?void 0:L.defaultColorScheme)||"blue",...((Y=l.globalChartSettings)==null?void 0:Y.defaultDateFilter)==="custom"&&{customStartDate:(G=l.globalChartSettings)==null?void 0:G.customStartDate,customEndDate:(mt=l.globalChartSettings)==null?void 0:mt.customEndDate}})},style:{backgroundColor:"#3b82f6",color:"white",padding:"8px 16px",borderRadius:"6px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Apply to All Charts"}),u.jsx("button",{onClick:()=>m({assigneeFilter:"all",selectedAssignees:[]}),style:{backgroundColor:"#ef4444",color:"white",padding:"8px 16px",borderRadius:"6px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Clear All Filters"}),u.jsx("button",{onClick:()=>g({defaultDateFilter:"all",customStartDate:"",customEndDate:""}),style:{backgroundColor:"#6b7280",color:"white",padding:"8px 16px",borderRadius:"6px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500"},children:"Reset Date Filter"})]}),((j=l.globalChartSettings)==null?void 0:j.defaultDateFilter)&&((T=l.globalChartSettings)==null?void 0:T.defaultDateFilter)!=="all"&&u.jsxs("div",{style:{marginTop:"12px",padding:"12px 16px",backgroundColor:i==="dark"?"#065f46":"#d1fae5",color:i==="dark"?"#10b981":"#065f46",borderRadius:"8px",fontSize:"14px",fontWeight:"500"},children:["✓ Global Date Filter Active: ",l.globalChartSettings.defaultDateFilter.replace(/([A-Z])/g," $1").replace(/^./,M=>M.toUpperCase()),l.globalChartSettings.defaultDateFilter==="custom"&&l.globalChartSettings.customStartDate&&l.globalChartSettings.customEndDate&&u.jsxs("span",{style:{fontSize:"12px",opacity:.8,marginLeft:"8px"},children:["(",new Date(l.globalChartSettings.customStartDate).toLocaleDateString()," - ",new Date(l.globalChartSettings.customEndDate).toLocaleDateString(),")"]})]})]})}),r==="individual"&&u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:[b.map(M=>u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"12px"},children:[u.jsx("h4",{style:{margin:0,color:i==="dark"?"#e5e7eb":"#1e293b",fontSize:"16px",fontWeight:"500"},children:M.title}),u.jsxs("div",{style:{display:"flex",gap:"8px",alignItems:"center"},children:[u.jsxs("label",{style:{display:"flex",alignItems:"center",gap:"6px"},children:[u.jsx("input",{type:"checkbox",checked:M.visible!==!1,onChange:L=>h(M.id,{visible:L.target.checked})}),u.jsx("span",{style:{fontSize:"12px",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Visible"})]}),u.jsx("button",{onClick:()=>v(M.id),style:{backgroundColor:"#f59e0b",color:"white",padding:"4px 8px",borderRadius:"4px",border:"none",cursor:"pointer",fontSize:"12px"},children:"Reset"})]})]}),u.jsxs("div",{style:{display:"grid",gap:"12px",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Chart Type"}),u.jsxs("select",{value:M.type||"bar",onChange:L=>h(M.id,{type:L.target.value}),style:{width:"100%",padding:"6px 8px",borderRadius:"4px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"bar",children:"Bar Chart"}),u.jsx("option",{value:"horizontal-bar",children:"Horizontal Bar"}),u.jsx("option",{value:"line",children:"Line Chart"}),u.jsx("option",{value:"area",children:"Area Chart"}),u.jsx("option",{value:"pie",children:"Pie Chart"}),u.jsx("option",{value:"doughnut",children:"Doughnut Chart"})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Color Scheme"}),u.jsxs("select",{value:M.colorScheme||"blue",onChange:L=>h(M.id,{colorScheme:L.target.value}),style:{width:"100%",padding:"6px 8px",borderRadius:"4px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"blue",children:"🔵 Blue"}),u.jsx("option",{value:"green",children:"🟢 Green"}),u.jsx("option",{value:"purple",children:"🟣 Purple"}),u.jsx("option",{value:"orange",children:"🟠 Orange"}),u.jsx("option",{value:"red",children:"🔴 Red"}),u.jsx("option",{value:"rainbow",children:"🌈 Rainbow"})]})]}),(M.type==="bar"||M.type==="horizontal-bar")&&u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Data Limit"}),u.jsx("input",{type:"number",min:"5",max:"50",value:M.limit||10,onChange:L=>h(M.id,{limit:parseInt(L.target.value)}),style:{width:"100%",padding:"6px 8px",borderRadius:"4px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]}),u.jsx("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:u.jsxs("label",{style:{display:"flex",alignItems:"center",gap:"6px",fontSize:"12px"},children:[u.jsx("input",{type:"checkbox",checked:M.showValues!==!1,onChange:L=>h(M.id,{showValues:L.target.checked})}),u.jsx("span",{style:{color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Show Values"})]})})]})]},M.id)),b.length===0&&u.jsx("div",{style:{textAlign:"center",padding:"40px",color:i==="dark"?"#9ca3af":"#64748b",fontSize:"14px"},children:d?"No charts match your search.":"No charts configured."})]}),r==="presets"&&u.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"16px"},children:u.jsxs("div",{style:{display:"grid",gap:"12px",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))"},children:[u.jsxs("button",{onClick:()=>m({colorScheme:"blue",showValues:!0,visible:!0}),style:{padding:"16px",backgroundColor:"#3b82f6",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500",textAlign:"left"},children:[u.jsx("div",{style:{fontWeight:"600",marginBottom:"4px"},children:"🔵 Professional Blue"}),u.jsx("div",{style:{fontSize:"12px",opacity:.9},children:"Blue theme with values shown"})]}),u.jsxs("button",{onClick:()=>m({colorScheme:"green",showValues:!0,visible:!0}),style:{padding:"16px",backgroundColor:"#10b981",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500",textAlign:"left"},children:[u.jsx("div",{style:{fontWeight:"600",marginBottom:"4px"},children:"🟢 Success Green"}),u.jsx("div",{style:{fontSize:"12px",opacity:.9},children:"Green theme with values shown"})]}),u.jsxs("button",{onClick:()=>m({colorScheme:"rainbow",showValues:!1,visible:!0}),style:{padding:"16px",backgroundColor:"#8b5cf6",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500",textAlign:"left"},children:[u.jsx("div",{style:{fontWeight:"600",marginBottom:"4px"},children:"🌈 Rainbow Theme"}),u.jsx("div",{style:{fontSize:"12px",opacity:.9},children:"Colorful charts without values"})]}),u.jsxs("button",{onClick:()=>m({visible:!1}),style:{padding:"16px",backgroundColor:"#6b7280",color:"white",borderRadius:"8px",border:"none",cursor:"pointer",fontSize:"14px",fontWeight:"500",textAlign:"left"},children:[u.jsx("div",{style:{fontWeight:"600",marginBottom:"4px"},children:"👁️ Hide All Charts"}),u.jsx("div",{style:{fontSize:"12px",opacity:.9},children:"Hide all charts from dashboard"})]})]})})]})]})},EC=`
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;if(typeof document<"u"){const t=document.createElement("style");t.type="text/css",t.innerText=EC,document.head.appendChild(t)}const qc=z.memo(({card:t,theme:e,isMobile:i})=>u.jsxs("div",{style:{backgroundColor:t.color,borderRadius:i?"12px":"16px",padding:i?"16px":"24px",color:"white",boxShadow:"0 4px 6px rgba(0, 0, 0, 0.1)",transition:"transform 0.2s ease, box-shadow 0.2s ease",cursor:"pointer",minHeight:i?"100px":"120px",display:"flex",flexDirection:"column",justifyContent:"space-between"},onMouseEnter:n=>{n.target.style.transform="translateY(-2px)",n.target.style.boxShadow="0 8px 15px rgba(0, 0, 0, 0.2)"},onMouseLeave:n=>{n.target.style.transform="translateY(0)",n.target.style.boxShadow="0 4px 6px rgba(0, 0, 0, 0.1)"},children:[u.jsx("div",{children:u.jsx("h3",{style:{margin:"0 0 8px 0",fontSize:i?"14px":"16px",fontWeight:"600",opacity:.9},children:t.title})}),u.jsx("div",{style:{fontSize:i?"24px":"32px",fontWeight:"bold"},children:t.count.toLocaleString()})]})),BC=()=>{var wf,jf,zf,Tf,Af,Mf,Of,Rf,Ef,Bf,Lf,Nf,Hf,Uf,Yf,Ff,Vf,Gf,qf,Xf,Qf,Zf,Wf;const[t,e]=z.useState({tickets:[],stats:null,totalCount:0,loading:!0,error:null}),[i,n]=z.useState("light"),[a,s]=z.useState(null),[l,o]=z.useState(null),[r,c]=z.useState(""),[d,f]=z.useState(""),[h,g]=z.useState(""),[m,v]=z.useState(""),[S,b]=z.useState(""),[x,y]=z.useState(""),[_,D]=z.useState(""),[A,j]=z.useState(""),[T,M]=z.useState(!0),[L,Y]=z.useState(!1),[G,mt]=z.useState("connected"),[wt,ot]=z.useState("status"),[H,V]=z.useState(null),[U,rt]=z.useState(!1),[at,At]=z.useState(!1),[Mt,le]=z.useState(!1),[R,N]=z.useState(!1),[F,ht]=z.useState(!1),[ie,NC]=z.useState(!1),[HC,UC]=z.useState(""),[YC,FC]=z.useState(""),[VC,GC]=z.useState([]),[qC,XC]=z.useState(null),[Er,Br]=z.useState(!1),[Za,rf]=z.useState(!1),[Wa,Hy]=z.useState(!1),[Ka,cf]=z.useState(!1),[$a,Lr]=z.useState(!1),[fn,Uy]=z.useState({enabled:!1,startDate:"",endDate:"",preset:"last30days"}),[Nr,Yy]=z.useState(!0),[uf,df]=z.useState(null),[Sl,ff]=z.useState(!1),[QC,hf]=z.useState(!1),[hn,pf]=z.useState("grid"),[_l,Fy]=z.useState([]),[oi,kl]=z.useState(1),[Cl,Vy]=z.useState(50),[oe,gf]=z.useState("updated"),[Qe,Hr]=z.useState("desc"),[q,be]=z.useState({globalChartSettings:{defaultDateFilter:"all",defaultColorScheme:"blue",autoRefresh:!0,refreshInterval:1e4,customStartDate:"",customEndDate:""},charts:{organizationChart:{id:"organizationChart",title:"Tickets by Restaurant",type:"horizontal-bar",visible:!0,position:{row:1,col:1},size:{width:1,height:1},dataSource:"organizations",limit:10,colorScheme:"blue",showValues:!0,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},weeklyChart:{id:"weeklyChart",title:"Tickets by Day (Last 7 Days)",type:"line",visible:!0,position:{row:1,col:2},size:{width:1,height:1},dataSource:"created",period:7,colorScheme:"green",showFill:!0,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]}},assigneeGroups:[{id:"team-leads",name:"Team Leads",assignees:[],description:"Team leadership members"},{id:"qa-team",name:"QA Team",assignees:[],description:"Quality assurance team"},{id:"dev-team",name:"Development Team",assignees:[],description:"Development team members"}],presets:[{id:"team-lead-view",name:"Team Lead Dashboard",description:"Optimized for team leads and managers",config:{}},{id:"ops-live-view",name:"Operations Live View",description:"Real-time operations monitoring",config:{}},{id:"qa-review-view",name:"QA Review Dashboard",description:"Quality assurance focused view",config:{}}],statusCards:[{id:"open",title:"Non traités",color:"#5e72e4",textColor:"#ffffff",jiraStatuses:["Non traités"],visible:!0,icon:"📋",description:"Tickets not yet started",order:1,position:{row:2,col:1},size:{width:1,height:1},dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"progress",title:"En cours",color:"#11cdef",textColor:"#ffffff",jiraStatuses:["En cours"],visible:!0,icon:"⚡",description:"Tickets currently being worked on",order:2,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"resolved",title:"Résolu",color:"#2dce89",textColor:"#ffffff",jiraStatuses:["Résolu"],visible:!0,icon:"✅",description:"Tickets that have been resolved",order:3,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"closed",title:"Fermé",color:"#6b7280",textColor:"#ffffff",jiraStatuses:["Fermé"],visible:!0,icon:"🔒",description:"Tickets that are closed",order:4,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]}],priorityCards:[{id:"urgent",title:"Très Urgent",color:"#dc3545",textColor:"#ffffff",jiraPriorities:["Très Urgent"],visible:!0,icon:"🚨",description:"Critical priority tickets",order:1,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"high",title:"Urgent",color:"#fd7e14",textColor:"#ffffff",jiraPriorities:["Urgent"],visible:!0,icon:"🔥",description:"High priority tickets",order:2,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"medium",title:"Moyen",color:"#ca8a04",textColor:"#ffffff",jiraPriorities:["Moyen"],visible:!0,icon:"⚠️",description:"Medium priority tickets",order:3,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]},{id:"low",title:"Faible",color:"#65a30d",textColor:"#ffffff",jiraPriorities:["Faible"],visible:!0,icon:"📝",description:"Low priority tickets",order:4,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]}],organizationCards:[{id:"org1",title:"Organization A",color:"#8b5cf6",textColor:"#ffffff",organizationNames:[],visible:!1,icon:"🏢",description:"Custom organization filter",order:1,dateFilter:"all",customDateFrom:"",customDateTo:"",assigneeFilter:"all",selectedAssignees:[]}]}),bf=z.useRef(null),Ur=z.useRef(null);z.useEffect(()=>{const p=setTimeout(()=>{f(r)},300);return()=>clearTimeout(p)},[r]);const qn=z.useCallback(p=>{var k;const C=((k=p.fields)==null?void 0:k.customfield_10002)||p.customfield_10002;if(C){if(typeof C=="string"&&C.trim())return C.trim();if(C.value&&typeof C.value=="string")return C.value.trim();if(C.displayName&&typeof C.displayName=="string")return C.displayName.trim();if(C.name&&typeof C.name=="string")return C.name.trim();if(Array.isArray(C)&&C.length>0){const w=C[0];if(w&&typeof w=="object"){if(w.name)return w.name;if(w.value)return w.value;if(w.displayName)return w.displayName;if(w.id)return w.id}if(typeof w=="string")return w}}return p.organization?p.organization:"N/A"},[]),tt=z.useCallback((p,C)=>{var k,w,E,B,Q,W,_t,pt,Ht,re,xe,ui,Mi,Ye;switch(C){case"key":return p.key||"";case"summary":{const Vt=p.summary||((k=p.fields)==null?void 0:k.summary);if(typeof Vt=="string")return Vt;if(Vt&&typeof Vt=="object"){if(typeof Vt.text=="string")return Vt.text;if(Array.isArray(Vt.content)&&((B=(E=(w=Vt.content[0])==null?void 0:w.content)==null?void 0:E[0])!=null&&B.text))return Vt.content[0].content[0].text}return""}case"status":return p.status||((W=(Q=p.fields)==null?void 0:Q.status)==null?void 0:W.name)||"";case"priority":return p.priority||((pt=(_t=p.fields)==null?void 0:_t.priority)==null?void 0:pt.name)||"";case"assignee":return p.assignee||((re=(Ht=p.fields)==null?void 0:Ht.assignee)==null?void 0:re.displayName)||"";case"project":return p.project||((ui=(xe=p.fields)==null?void 0:xe.project)==null?void 0:ui.name)||"";case"organization":return qn(p);case"updated":return p.updated||((Mi=p.fields)==null?void 0:Mi.updated)||"";case"created":return p.created||((Ye=p.fields)==null?void 0:Ye.created)||"";default:return""}},[qn]),ri=z.useCallback(p=>{var k;const C=((k=p.fields)==null?void 0:k.assignee)||p.assignee;return C?typeof C=="string"?{displayName:C,emailAddress:C,key:C.toLowerCase()}:{displayName:C.displayName||C.name||"Unknown",emailAddress:C.emailAddress||C.email||null,key:(C.key||C.accountId||C.displayName||"unknown").toLowerCase()}:{displayName:"Unassigned",emailAddress:null,key:"unassigned"}},[]),Yr=z.useCallback((p,C,k)=>!C||C==="all"?p:p.filter(w=>{const E=ri(w);switch(C){case"unassigned":return E.key==="unassigned";case"specific":return!k||k.length===0?!0:k.some(B=>E.key===B.toLowerCase()||E.displayName.toLowerCase().includes(B.toLowerCase())||E.emailAddress&&E.emailAddress.toLowerCase().includes(B.toLowerCase()));default:return!0}}),[ri]),Fr=z.useCallback((p,C,k,w)=>!C||C==="all"?p:p.filter(E=>{const B=new Date(tt(E,"updated"));switch(C){case"today":const Q=new Date;return Q.setHours(0,0,0,0),B>=Q;case"yesterday":const W=new Date;W.setDate(W.getDate()-1),W.setHours(0,0,0,0);const _t=new Date(W);return _t.setHours(23,59,59,999),B>=W&&B<=_t;case"last7days":const pt=new Date;return pt.setDate(pt.getDate()-7),B>=pt;case"last30days":const Ht=new Date;return Ht.setDate(Ht.getDate()-30),B>=Ht;case"last90days":const re=new Date;return re.setDate(re.getDate()-90),B>=re;case"thisweek":const xe=new Date,ui=xe.getDay(),Mi=xe.getDate()-ui+(ui===0?-6:1);return xe.setDate(Mi),xe.setHours(0,0,0,0),B>=xe;case"thismonth":const Ye=new Date;return Ye.setDate(1),Ye.setHours(0,0,0,0),B>=Ye;case"thisyear":const Vt=new Date;return Vt.setMonth(0,1),Vt.setHours(0,0,0,0),B>=Vt;case"lastyear":const zl=new Date;zl.setFullYear(zl.getFullYear()-1,0,1),zl.setHours(0,0,0,0);const Tl=new Date;return Tl.setFullYear(Tl.getFullYear()-1,11,31),Tl.setHours(23,59,59,999),B>=zl&&B<=Tl;case"custom":if(k||w){const Jy=k?new Date(k):new Date("1900-01-01"),Kf=w?new Date(w):new Date("2100-12-31");return Kf.setHours(23,59,59,999),B>=Jy&&B<=Kf}return!0;default:return!0}}),[tt]),ci=z.useCallback(()=>{const p=new Map;return(t.tickets||[]).forEach(C=>{const k=ri(C);k.key!=="unassigned"&&p.set(k.key,{displayName:k.displayName,emailAddress:k.emailAddress,key:k.key})}),Array.from(p.values()).sort((C,k)=>C.displayName.localeCompare(k.displayName))},[t.tickets,ri]);z.useEffect(()=>{if(!t.tickets)return;Y(!0);const p=setTimeout(()=>{let C=t.tickets;if(d){const k=d.toLowerCase();C=C.filter(w=>[tt(w,"key"),tt(w,"summary"),tt(w,"status"),tt(w,"priority"),tt(w,"assignee"),tt(w,"project"),tt(w,"organization")].some(B=>B.toLowerCase().includes(k)))}h&&(C=C.filter(k=>tt(k,"status").toLowerCase()===h.toLowerCase())),m&&(C=C.filter(k=>{const w=ri(k);return m==="unassigned"?w.key==="unassigned":w.displayName.toLowerCase()===m.toLowerCase()||w.emailAddress&&w.emailAddress.toLowerCase()===m.toLowerCase()})),S&&(C=C.filter(k=>tt(k,"priority").toLowerCase()===S.toLowerCase())),(x||_||A)&&(C=C.filter(k=>{const w=new Date(tt(k,"updated"));if(x)switch(x){case"today":const E=new Date;return E.setHours(0,0,0,0),w>=E;case"yesterday":const B=new Date;B.setDate(B.getDate()-1),B.setHours(0,0,0,0);const Q=new Date(B);return Q.setHours(23,59,59,999),w>=B&&w<=Q;case"last7days":const W=new Date;return W.setDate(W.getDate()-7),w>=W;case"last30days":const _t=new Date;return _t.setDate(_t.getDate()-30),w>=_t;case"last90days":const pt=new Date;return pt.setDate(pt.getDate()-90),w>=pt;case"thisweek":const Ht=new Date,re=Ht.getDay(),xe=Ht.getDate()-re+(re===0?-6:1);return Ht.setDate(xe),Ht.setHours(0,0,0,0),w>=Ht;case"thismonth":const ui=new Date;return ui.setDate(1),ui.setHours(0,0,0,0),w>=ui;case"thisyear":const Mi=new Date;return Mi.setMonth(0,1),Mi.setHours(0,0,0,0),w>=Mi;case"lastyear":const Ye=new Date;Ye.setFullYear(Ye.getFullYear()-1,0,1),Ye.setHours(0,0,0,0);const Vt=new Date;return Vt.setFullYear(Vt.getFullYear()-1,11,31),Vt.setHours(23,59,59,999),w>=Ye&&w<=Vt;default:return!0}if(_||A){const E=_?new Date(_):new Date("1900-01-01"),B=A?new Date(A):new Date("2100-12-31");return B.setHours(23,59,59,999),w>=E&&w<=B}return!0})),C.sort((k,w)=>{if(T&&oe==="updated"){const Q=new Date(tt(k,"updated")||0);return new Date(tt(w,"updated")||0)-Q}let E=tt(k,oe),B=tt(w,oe);return(oe==="updated"||oe==="created")&&(E=new Date(E||0),B=new Date(B||0)),typeof E=="string"&&typeof B=="string"&&(E=E.toLowerCase(),B=B.toLowerCase()),Qe==="asc"?E>B?1:E<B?-1:0:E<B?1:E>B?-1:0}),Fy(C),kl(1),Y(!1)},0);return()=>clearTimeout(p)},[t.tickets,d,h,m,S,x,_,A,oe,Qe,T,tt]);const Vr=()=>{rt(window.innerWidth<768)},Gy=async()=>{try{e(p=>({...p,loading:!0})),await Xn(),Xy()}catch(p){console.error("Error initializing dashboard:",p)}},Xn=async()=>{try{console.log("Loading dashboard data...");const p=Date.now();try{const w=await(await fetch("/api/mongodb/dashboard-tickets?stats=true")).json();if(w.success){const E=w.count||0,B=t.totalCount,Q=(w.tickets||[]).sort((W,_t)=>{var re,xe;const pt=new Date(W.updated||((re=W.fields)==null?void 0:re.updated)||0);return new Date(_t.updated||((xe=_t.fields)==null?void 0:xe.updated)||0)-pt});if(e({tickets:Q,stats:w.stats,totalCount:E,loading:!1,error:null}),V(w.performance),s(new Date),mt("connected"),console.log("Dashboard data loaded:",w.count,"tickets"),B>0&&E>B){const W=E-B;o(`🔄 ${W} new ticket${W>1?"s":""} synced!`),setTimeout(()=>o(null),5e3)}return}}catch{console.log("API not available, using mock data")}e({tickets:[],stats:{statusCounts:{"Non traités":103,"En cours":44,Résolu:591,Fermé:75464},priorityCounts:{"Très Urgent":21037,Urgent:0,Moyen:0,Faible:0},assigneeCounts:{"John Doe":150,"Jane Smith":200,"Bob Johnson":175,"Alice Brown":125,"Charlie Wilson":100},organizationCounts:{"Marrakech drive":2800,"Hay riad":2600,"Marrakech gueliz":2400,"Tanger plage":2200,"Agadir Marjane":2e3,"Tanger marjane":1800,Skhirat:1600,Arnolab:1400,Hermitage:1200,Tetouan:1e3},dailyTickets:[{date:"2025-08-04",created:65},{date:"2025-08-05",created:78},{date:"2025-08-06",created:52},{date:"2025-08-07",created:48},{date:"2025-08-08",created:45},{date:"2025-08-09",created:52},{date:"2025-08-10",created:15}]},totalCount:78171,loading:!1,error:null}),V({queryTime:"12ms",totalRecords:78171}),s(new Date),mt("connected"),console.log("Using mock data - 78,171 tickets")}catch(p){console.error("Error loading dashboard data:",p),e(C=>({...C,loading:!1,error:p.message})),mt("error")}},qy=()=>{console.log("WebSocket connection disabled - using mock data mode")},Xy=()=>{Ur.current=setInterval(Xn,5e3)},xf=()=>{n(p=>p==="light"?"dark":"light")},mf=()=>{document.fullscreenElement?document.exitFullscreen().then(()=>{Br(!1)}).catch(p=>{console.error("Error attempting to exit fullscreen:",p)}):document.documentElement.requestFullscreen().then(()=>{Br(!0)}).catch(p=>{console.error("Error attempting to enable fullscreen:",p)})},Qy=async()=>{try{const p=await fetch("/api/system/auto-start",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:!Za,url:window.location.origin,fullScreen:!0})});if(p.ok){const C=await p.json();rf(!Za),alert(C.message)}else throw new Error("Failed to toggle auto-start")}catch(p){console.error("Error toggling auto-start:",p),alert("Error configuring auto-start. This feature requires server support.")}};z.useEffect(()=>{(async()=>{try{const C=await fetch("/api/system/auto-start-status");if(C.ok){const k=await C.json();rf(k.enabled)}}catch(C){console.log("Auto-start status check failed:",C)}})()},[]),z.useEffect(()=>{const p=new URLSearchParams(window.location.search);(p.get("autostart")==="true"||p.get("fullscreen")==="true")&&setTimeout(()=>{mf()},2e3)},[]),z.useEffect(()=>{const p=()=>{Br(!!document.fullscreenElement)};return document.addEventListener("fullscreenchange",p),()=>document.removeEventListener("fullscreenchange",p)},[]);const yf=()=>q.statusCards.filter(p=>p.visible).sort((p,C)=>p.order-C.order).map(p=>{var C;if(t.tickets&&t.tickets.length>0){let k=Fr(t.tickets||[],p.dateFilter,p.customDateFrom,p.customDateTo);k=Yr(k,p.assigneeFilter,p.selectedAssignees);const w=k.filter(E=>{const B=tt(E,"status");return p.jiraStatuses.some(Q=>B.toLowerCase()===Q.toLowerCase())}).length;return{...p,count:w}}else{const k=((C=t.stats)==null?void 0:C.statusCounts)||{};let w=0;return p.jiraStatuses.forEach(E=>{w+=k[E]||0}),{...p,count:w}}}),vf=()=>q.priorityCards.filter(p=>p.visible).sort((p,C)=>p.order-C.order).map(p=>{var C;if(t.tickets&&t.tickets.length>0){let k=Fr(t.tickets||[],p.dateFilter,p.customDateFrom,p.customDateTo);k=Yr(k,p.assigneeFilter,p.selectedAssignees);const w=k.filter(E=>{const B=tt(E,"priority");return p.jiraPriorities.some(Q=>B.toLowerCase()===Q.toLowerCase())}).length;return{...p,count:w}}else{const k=((C=t.stats)==null?void 0:C.priorityCounts)||{};let w=0;return p.jiraPriorities.forEach(E=>{w+=k[E]||0}),{...p,count:w}}}),Gr=()=>q.organizationCards.filter(p=>p.visible).sort((p,C)=>p.order-C.order).map(p=>{let C=Fr(t.tickets||[],p.dateFilter,p.customDateFrom,p.customDateTo);C=Yr(C,p.assigneeFilter,p.selectedAssignees);const k=C.filter(w=>{const E=qn(w);return p.organizationNames.some(B=>E.toLowerCase()===B.toLowerCase())}).length;return{...p,count:k}}),Sf=()=>{switch(G){case"connected":return"#10b981";case"disconnected":return"#f59e0b";case"error":case"failed":return"#ef4444";default:return"#6b7280"}},Zy=()=>{switch(G){case"connected":return"🟢";case"disconnected":return"🟡";case"error":case"failed":return"🔴";default:return"⚪"}},Qn=p=>{oe===p?Hr(Qe==="asc"?"desc":"asc"):(gf(p),Hr("desc"))},Wy=()=>{const p=(oi-1)*Cl,C=p+Cl;return _l.slice(p,C)},Zn=()=>Math.ceil(_l.length/Cl),Ky=()=>{localStorage.setItem("jiraDashboardConfig",JSON.stringify(q)),N(!1),alert("Configuration saved successfully!")},Dl=z.useCallback(()=>{var B,Q,W;let p=t.tickets||[];const C=(B=q.globalChartSettings)==null?void 0:B.defaultDateFilter;if(!C||C==="all")return p;const k=new Date;let w,E;switch(C){case"last7days":w=new Date(k.getTime()-7*24*60*60*1e3),E=k;break;case"last30days":w=new Date(k.getTime()-30*24*60*60*1e3),E=k;break;case"last90days":w=new Date(k.getTime()-90*24*60*60*1e3),E=k;break;case"thisyear":w=new Date(k.getFullYear(),0,1),E=k;break;case"lastyear":w=new Date(k.getFullYear()-1,0,1),E=new Date(k.getFullYear()-1,11,31,23,59,59);break;case"custom":if((Q=q.globalChartSettings)!=null&&Q.customStartDate&&((W=q.globalChartSettings)!=null&&W.customEndDate))w=new Date(q.globalChartSettings.customStartDate),E=new Date(q.globalChartSettings.customEndDate);else return p;break;default:return p}return p.filter(_t=>{const pt=new Date(_t.created||_t.updated);return pt>=w&&pt<=E})},[t.tickets,q.globalChartSettings]),_f=z.useMemo(()=>{var k,w;const p=new Map;return Dl().forEach(E=>{const B=(qn(E)||"N/A").trim();B&&p.set(B,(p.get(B)||0)+1)}),Array.from(p.entries()).sort((E,B)=>B[1]-E[1]).slice(0,((w=(k=q.charts)==null?void 0:k.organizationChart)==null?void 0:w.limit)||10).map(([E,B])=>({name:E,count:B}))},[Dl,qn,(jf=(wf=q.charts)==null?void 0:wf.organizationChart)==null?void 0:jf.limit]),kf=z.useMemo(()=>{const p=[],C=Array(7).fill(0),k=new Date;for(let Q=6;Q>=0;Q--){const W=new Date(k);W.setDate(k.getDate()-Q),p.push(W.toLocaleDateString(void 0,{day:"2-digit",month:"short",year:"numeric"}))}const w=[];for(let Q=6;Q>=0;Q--){const W=new Date(k);W.setDate(k.getDate()-Q),w.push(W.toISOString().slice(0,10))}const E=Object.fromEntries(w.map((Q,W)=>[Q,W]));return Dl().forEach(Q=>{var pt;const W=new Date(Q.created||((pt=Q.fields)==null?void 0:pt.created)||0),_t=isNaN(W)?null:W.toISOString().slice(0,10);_t&&E[_t]!==void 0&&(C[E[_t]]+=1)}),{labels:p,created:C}},[Dl]),Cf=async()=>{try{const C=await fetch("/api/config/dashboard");if(C.ok){const k=await C.json();if(k.success&&k.config){console.log("✅ Configuration loaded from server"),be(k.config),localStorage.setItem("jiraDashboardConfig",JSON.stringify(k.config));return}}}catch(C){console.log("Server config not available, trying localStorage:",C.message)}const p=localStorage.getItem("jiraDashboardConfig");if(p)try{const C=JSON.parse(p);console.log("✅ Configuration loaded from localStorage"),be(C)}catch(C){console.error("Error loading saved config:",C)}else console.log("ℹ️ Using default configuration")},nt=(p,C,k)=>{be(w=>({...w,[`${p}Cards`]:w[`${p}Cards`].map(E=>E.id===C?{...E,...k}:E)}))},wl=p=>{const C={id:`${p}_${Date.now()}`,title:`New ${p} Card`,color:"#6b7280",textColor:"#ffffff",visible:!0,icon:"📊",description:`Custom ${p} card`,order:q[`${p}Cards`].length+1,...p==="status"&&{jiraStatuses:[]},...p==="priority"&&{jiraPriorities:[]},...p==="organization"&&{organizationNames:[]}};be(k=>({...k,[`${p}Cards`]:[...k[`${p}Cards`],C]}))};z.useEffect(()=>{localStorage.setItem("jiraDashboardConfig",JSON.stringify(q));const p=setTimeout(async()=>{try{await fetch("/api/config/dashboard",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:q})}),console.log("✅ Configuration saved to server")}catch(C){console.error("❌ Failed to save configuration to server:",C)}},300);return()=>clearTimeout(p)},[q]);const $y=z.useCallback((p,C)=>{be(k=>{const w={...k};return w.statusCards&&(w.statusCards=w.statusCards.map(E=>E.id===p?{...E,position:C}:E)),w.priorityCards&&(w.priorityCards=w.priorityCards.map(E=>E.id===p?{...E,position:C}:E)),w.organizationCards&&(w.organizationCards=w.organizationCards.map(E=>E.id===p?{...E,position:C}:E)),w.charts&&Object.keys(w.charts).forEach(E=>{E===p&&(w.charts[E]={...w.charts[E],position:C})}),w})},[]);z.useCallback((p,C)=>{be(k=>({...k,charts:{...k.charts,[p]:C}}))},[]);const Df=z.useCallback(()=>{var B,Q,W,_t;const p=((B=q.globalChartSettings)==null?void 0:B.defaultDateFilter)||fn.preset||"all";if(!fn.enabled&&p==="all"){e(pt=>({...pt,filteredTickets:null,dateFilterApplied:!1,dateFilterRange:null}));return}let C,k;const w=new Date;switch(p){case"last7days":C=new Date(w.getTime()-7*24*60*60*1e3),k=w;break;case"last30days":C=new Date(w.getTime()-30*24*60*60*1e3),k=w;break;case"last90days":C=new Date(w.getTime()-90*24*60*60*1e3),k=w;break;case"thisyear":C=new Date(w.getFullYear(),0,1),k=w;break;case"lastyear":C=new Date(w.getFullYear()-1,0,1),k=new Date(w.getFullYear()-1,11,31,23,59,59);break;case"custom":const pt=((Q=q.globalChartSettings)==null?void 0:Q.customStartDate)||fn.startDate,Ht=((W=q.globalChartSettings)==null?void 0:W.customEndDate)||fn.endDate;if(pt&&Ht)C=new Date(pt),k=new Date(Ht);else return;break;case"all":default:e(re=>({...re,filteredTickets:null,dateFilterApplied:!1,dateFilterRange:null}));return}const E=((_t=t.tickets)==null?void 0:_t.filter(pt=>{const Ht=new Date(pt.created||pt.updated);return Ht>=C&&Ht<=k}))||[];e(pt=>({...pt,filteredTickets:E,dateFilterApplied:!0,dateFilterRange:{startDate:C,endDate:k,preset:fn.preset}})),console.log(`📊 Date filter applied: ${E.length} tickets from ${C.toLocaleDateString()} to ${k.toLocaleDateString()}`)},[fn,t.tickets]);z.useEffect(()=>{Nr&&Df()},[fn,Nr,Df]),z.useCallback(()=>{Uy(p=>({...p,enabled:!1}))},[]);const qr=z.useCallback(async()=>{try{const p=await fetch("/api/sync/status");if(p.ok){const C=await p.json();return df(C),C}}catch(p){console.error("❌ Failed to check service status:",p)}return null},[]),jl=z.useCallback(async()=>{try{hf(!0);const p=await fetch("/api/dashboard/tickets");if(p.ok){const C=await p.json();e(C),s(new Date)}else console.error("Failed to fetch dashboard data")}catch(p){console.error("Error fetching dashboard data:",p)}finally{hf(!1)}},[]),Py=z.useCallback(async()=>{ff(!0);try{const p=await fetch("/api/sync/force-recovery",{method:"POST",headers:{"Content-Type":"application/json"}});if(p.ok){const C=await p.json();return C.success?(console.log("✅ Service recovery successful"),df(C.status),await jl(),!0):(console.error("❌ Service recovery failed:",C.message),!1)}else return console.error("❌ Service recovery request failed"),!1}catch(p){return console.error("❌ Service recovery error:",p),!1}finally{ff(!1)}},[jl]);z.useEffect(()=>{qr();const p=setInterval(qr,3e4);return()=>clearInterval(p)},[qr]),z.useEffect(()=>{jl()},[jl]);const Xr=(p,C)=>{be(k=>({...k,[`${p}Cards`]:k[`${p}Cards`].filter(w=>w.id!==C)}))},Wn=(p,C,k)=>{be(w=>{const E=[...w[`${p}Cards`]],B=E.findIndex(W=>W.id===C);if(B===-1)return w;const Q=k==="up"?B-1:B+1;return Q<0||Q>=E.length?w:([E[B],E[Q]]=[E[Q],E[B]],E.forEach((W,_t)=>{W.order=_t+1}),{...w,[`${p}Cards`]:E})})},Qr=(p,C,k,w)=>{nt(p,C,{[p==="status"?"jiraStatuses":p==="priority"?"jiraPriorities":"organizationNames"]:w})},Zr=p=>{var C,k,w;return p==="status"?Object.keys(((C=t.stats)==null?void 0:C.statusCounts)||{}):p==="priority"?Object.keys(((k=t.stats)==null?void 0:k.priorityCounts)||{}):p==="organization"?Object.keys(((w=t.stats)==null?void 0:w.organizationCounts)||{}):[]};return z.useEffect(()=>((async()=>{const C=localStorage.getItem("dashboard-theme");C&&n(C),await Cf(),Gy(),qy(),Vr()})(),window.addEventListener("resize",Vr),()=>{bf.current&&bf.current.close(),Ur.current&&clearInterval(Ur.current),window.removeEventListener("resize",Vr)}),[]),z.useEffect(()=>{document.documentElement.classList.toggle("dark",i==="dark"),localStorage.setItem("dashboard-theme",i)},[i]),t.loading?u.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",backgroundColor:i==="dark"?"#111827":"#f8fafc",color:i==="dark"?"#e5e7eb":"#1e293b"},children:u.jsxs("div",{style:{textAlign:"center"},children:[u.jsx("div",{className:"animate-spin",style:{width:"50px",height:"50px",border:"3px solid #e2e8f0",borderTop:"3px solid #3b82f6",borderRadius:"50%",margin:"0 auto 20px"}}),u.jsx("p",{style:{fontSize:"18px"},children:"Loading Modern Jira Dashboard..."})]})}):t.error?u.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",backgroundColor:i==="dark"?"#111827":"#f8fafc"},children:u.jsxs("div",{style:{textAlign:"center",padding:"40px",backgroundColor:i==="dark"?"#1f2937":"white",borderRadius:"12px",boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)",maxWidth:"500px",color:i==="dark"?"#e5e7eb":"#1e293b"},children:[u.jsx("h2",{style:{color:"#ef4444",marginBottom:"16px"},children:"Error Loading Dashboard"}),u.jsx("p",{style:{marginBottom:"24px",opacity:.8},children:t.error}),u.jsx("button",{onClick:Xn,className:"btn btn-primary",children:"Retry"})]})}):u.jsxs("div",{style:{minHeight:"100vh",backgroundColor:i==="dark"?"#111827":"#f8fafc",color:i==="dark"?"#e5e7eb":"#1e293b",fontFamily:"system-ui, -apple-system, sans-serif",transition:"all 0.3s ease"},children:[l&&u.jsx("div",{style:{position:"fixed",top:"20px",right:"20px",backgroundColor:"#10b981",color:"white",padding:"12px 20px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",zIndex:1e3,fontSize:"14px",fontWeight:"500",animation:"slideIn 0.3s ease-out"},children:l}),u.jsx("button",{onClick:()=>Hy(!Wa),style:{position:"fixed",top:"20px",left:"20px",width:"40px",height:"40px",borderRadius:"50%",backgroundColor:i==="dark"?"#374151":"#f3f4f6",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,color:i==="dark"?"#e5e7eb":"#1f2937",cursor:"pointer",zIndex:1e3,display:"flex",alignItems:"center",justifyContent:"center",fontSize:"18px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",transition:"all 0.3s ease",opacity:Wa?1:.7},title:Wa?"Hide Controls":"Show Controls",children:Wa?"×":"☰"}),Wa&&u.jsx("header",{style:{borderBottom:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,padding:"16px 24px",position:"sticky",top:0,zIndex:50,backdropFilter:"blur(8px)",backgroundColor:i==="dark"?"rgba(31, 41, 55, 0.95)":"rgba(255, 255, 255, 0.95)",animation:"slideDown 0.3s ease-out"},children:u.jsxs("div",{style:{maxWidth:"1400px",margin:"0 auto"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"16px"},children:[u.jsx("h1",{className:"gradient-text",style:{margin:0,fontSize:U?"20px":"32px",fontWeight:"bold"},children:U?"Jira Pro":"Jira Dashboard Pro"}),!U&&u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",color:Sf(),fontSize:"14px",fontWeight:"500"},children:[u.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:Sf(),borderRadius:"50%"},className:"animate-pulse"}),u.jsx("span",{style:{textTransform:"capitalize"},children:G})]})]}),u.jsx("div",{style:{display:"flex",alignItems:"center",gap:"12px"},children:U?u.jsx("button",{onClick:()=>At(!at),style:{padding:"8px",backgroundColor:i==="dark"?"#374151":"#f3f4f6",border:"none",borderRadius:"8px",cursor:"pointer",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"☰"}):u.jsxs(u.Fragment,{children:[u.jsxs("div",{style:{position:"relative"},children:[u.jsx("input",{type:"text",placeholder:"Search tickets...",value:r,onChange:p=>c(p.target.value),style:{width:"300px",padding:"8px 12px 8px 40px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"8px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}}),u.jsx("div",{style:{position:"absolute",left:"12px",top:"50%",transform:"translateY(-50%)",color:i==="dark"?"#9ca3af":"#6b7280"},children:"⌕"})]}),u.jsxs("button",{onClick:()=>le(!Mt),className:"btn",style:{backgroundColor:Mt?"#10b981":"#6b7280",color:"white"},children:[Mt?"Hide":"Show"," Table"]}),u.jsx("button",{onClick:()=>cf(!Ka),className:"btn",style:{backgroundColor:Ka?"#8b5cf6":"#6b7280",color:"white"},children:"Charts"}),u.jsx("button",{onClick:()=>Lr(!$a),className:"btn",style:{backgroundColor:$a?"#10b981":"#6b7280",color:"white"},children:"Cards"}),u.jsx("button",{onClick:()=>ht(!F),className:"btn",style:{backgroundColor:F?"#10b981":"#6b7280",color:"white"},children:"Advanced Config"}),uf&&!uf.healthy&&u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px",padding:"6px 10px",backgroundColor:"#fef2f2",border:"1px solid #fecaca",borderRadius:"6px",fontSize:"12px"},children:[u.jsx("span",{style:{color:"#dc2626"},children:"⚠️ Service Issue"}),u.jsx("button",{onClick:Py,disabled:Sl,style:{backgroundColor:"#dc2626",color:"white",border:"none",padding:"3px 6px",borderRadius:"3px",fontSize:"10px",cursor:Sl?"not-allowed":"pointer",opacity:Sl?.6:1},children:Sl?"🔄":"🔧 Fix"})]}),u.jsx("button",{onClick:()=>pf(hn==="grid"?"drag":"grid"),className:"btn",style:{backgroundColor:hn==="drag"?"#f59e0b":"#6b7280",color:"white"},children:hn==="drag"?"Lock Layout":"Drag Mode"}),u.jsx("button",{onClick:()=>{const p={version:"1.0",timestamp:new Date().toISOString(),theme:i,config:q},C=new Blob([JSON.stringify(p,null,2)],{type:"application/json"}),k=URL.createObjectURL(C),w=document.createElement("a");w.href=k,w.download=`jira-dashboard-config-${new Date().toISOString().slice(0,10)}.json`,w.click(),URL.revokeObjectURL(k),alert("Configuration exported successfully!")},className:"btn",style:{backgroundColor:"#8b5cf6",color:"white"},children:"Export Config"}),u.jsx("button",{onClick:()=>{const p=document.createElement("input");p.type="file",p.accept=".json",p.onchange=C=>{const k=C.target.files[0];if(k){const w=new FileReader;w.onload=E=>{try{const B=JSON.parse(E.target.result);B.theme&&n(B.theme),B.config?be(B.config):B.version&&be(B),alert("Configuration imported successfully!")}catch(B){alert("Error importing configuration: "+B.message)}},w.readAsText(k)}},p.click()},className:"btn",style:{backgroundColor:"#06b6d4",color:"white"},children:"Import Config"}),u.jsx("button",{onClick:Xn,className:"btn btn-primary",children:"Refresh"}),u.jsx("button",{onClick:async()=>{try{const C=await(await fetch("/api/sync/incremental",{method:"POST"})).json();C.success&&(o(`✅ Manual sync completed! ${C.updatedTickets||0} tickets updated`),setTimeout(()=>o(null),5e3),setTimeout(()=>Xn(),1e3))}catch(p){console.error("Manual sync failed:",p)}},className:"btn",style:{backgroundColor:"#f59e0b",color:"white"},children:"⚡ Force Sync"}),u.jsx("button",{onClick:mf,className:"btn",style:{backgroundColor:Er?"#10b981":"#6b7280",color:"white"},title:Er?"Exit Full Screen":"Enter Full Screen",children:Er?"Exit Full Screen":"Full Screen"}),u.jsx("button",{onClick:Qy,className:"btn",style:{backgroundColor:Za?"#10b981":"#6b7280",color:"white"},title:Za?"Disable Auto-Start":"Enable Auto-Start",children:Za?"Auto-Start ON":"Auto-Start OFF"}),u.jsx("button",{onClick:xf,style:{padding:"8px 12px",backgroundColor:i==="dark"?"#374151":"#f3f4f6",border:"none",borderRadius:"8px",cursor:"pointer",fontSize:"16px"},children:i==="dark"?"☀️":"🌙"})]})})]}),U&&at&&u.jsxs("div",{style:{marginTop:"16px",padding:"16px",backgroundColor:i==="dark"?"#374151":"#f9fafb",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e5e7eb"}`},children:[u.jsx("div",{style:{marginBottom:"12px"},children:u.jsx("input",{type:"text",placeholder:"Search tickets...",value:r,onChange:p=>c(p.target.value),style:{width:"100%",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937"}})}),u.jsxs("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:[u.jsxs("button",{onClick:()=>{le(!Mt),At(!1)},className:"btn",style:{flex:1,backgroundColor:Mt?"#10b981":"#6b7280",color:"white"},children:[Mt?"Hide":"Show"," Table"]}),u.jsx("button",{onClick:()=>{cf(!Ka),At(!1)},className:"btn",style:{flex:1,backgroundColor:Ka?"#8b5cf6":"#6b7280",color:"white"},children:"Charts"}),u.jsx("button",{onClick:()=>{Lr(!$a),At(!1)},className:"btn",style:{flex:1,backgroundColor:$a?"#10b981":"#6b7280",color:"white"},children:"Cards"}),u.jsx("button",{onClick:()=>{ht(!F),At(!1)},className:"btn",style:{flex:1,backgroundColor:F?"#10b981":"#6b7280",color:"white"},children:"Advanced"}),u.jsx("button",{onClick:()=>{pf(hn==="grid"?"drag":"grid"),At(!1)},className:"btn",style:{flex:1,backgroundColor:hn==="drag"?"#f59e0b":"#6b7280",color:"white"},children:hn==="drag"?"Lock":"Drag"}),u.jsx("button",{onClick:()=>{const p={theme:i,chartConfigs,assigneeGroups,selectedAssigneeGroup,layoutPositions:{}},C=new Blob([JSON.stringify(p,null,2)],{type:"application/json"}),k=URL.createObjectURL(C),w=document.createElement("a");w.href=k,w.download="dashboard-config.json",w.click(),URL.revokeObjectURL(k),At(!1)},className:"btn",style:{flex:1,backgroundColor:"#8b5cf6",color:"white"},children:"Export"}),u.jsx("button",{onClick:()=>{const p=document.createElement("input");p.type="file",p.accept=".json",p.onchange=C=>{const k=C.target.files[0];if(k){const w=new FileReader;w.onload=E=>{try{const B=JSON.parse(E.target.result);B.theme&&n(B.theme),B.chartConfigs&&setChartConfigs(B.chartConfigs),B.assigneeGroups&&setAssigneeGroups(B.assigneeGroups),B.selectedAssigneeGroup&&setSelectedAssigneeGroup(B.selectedAssigneeGroup),alert("Configuration imported successfully!")}catch(B){alert("Error importing configuration: "+B.message)}},w.readAsText(k)}},p.click(),At(!1)},className:"btn",style:{flex:1,backgroundColor:"#06b6d4",color:"white"},children:"Import"})]}),u.jsxs("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap",marginTop:"8px"},children:[u.jsx("button",{onClick:()=>{Xn(),At(!1)},className:"btn btn-primary",style:{flex:1},children:"Refresh"}),u.jsx("button",{onClick:xf,className:"btn btn-secondary",style:{flex:1},children:i==="dark"?"☀️ Light":"🌙 Dark"})]}),u.jsxs("div",{style:{marginTop:"12px",padding:"8px",backgroundColor:i==="dark"?"#1f2937":"#f3f4f6",borderRadius:"6px",fontSize:"12px",display:"flex",alignItems:"center",gap:"8px"},children:[u.jsx("span",{children:Zy()}),u.jsxs("span",{children:["Status: ",G]})]})]})]})}),u.jsxs("main",{style:{maxWidth:"1400px",margin:"0 auto",padding:U?"16px":"32px"},children:[u.jsxs("div",{style:{position:"fixed",bottom:"10px",left:"50%",transform:"translateX(-50%)",display:"flex",gap:"16px",padding:"6px 16px",backgroundColor:i==="dark"?"rgba(31, 41, 55, 0.9)":"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(8px)",borderRadius:"20px",border:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",zIndex:100,boxShadow:"0 4px 12px rgba(0, 0, 0, 0.1)"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[u.jsx("span",{children:"Total Tickets"}),u.jsx("span",{style:{fontWeight:"bold",color:"#3b82f6"},children:t.totalCount.toLocaleString()})]}),u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[u.jsx("span",{children:"Performance"}),u.jsx("span",{style:{fontWeight:"bold",color:"#10b981"},children:H?`${H.loadTimeMs}ms`:"N/A"})]}),u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[u.jsx("span",{children:"Last Update"}),u.jsx("span",{style:{fontWeight:"bold",color:"#8b5cf6"},children:a?a.toLocaleTimeString():"Never"})]})]}),u.jsxs("div",{style:{marginBottom:U?"24px":"32px"},children:[u.jsx("h2",{style:{marginBottom:"16px",fontSize:U?"20px":"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Status Overview"}),u.jsx("div",{className:"grid grid-auto-fit",children:yf().map((p,C)=>u.jsx(qc,{card:p,theme:i,isMobile:U},`status-${p.id||C}`))})]}),u.jsxs("div",{style:{marginBottom:U?"24px":"32px"},children:[u.jsx("h2",{style:{marginBottom:"16px",fontSize:U?"20px":"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Priority Overview"}),u.jsx("div",{className:"grid grid-auto-fit",children:vf().map((p,C)=>u.jsx(qc,{card:p,theme:i,isMobile:U},`priority-${p.id||C}`))})]}),Gr().length>0&&u.jsxs("div",{style:{marginBottom:U?"24px":"32px"},children:[u.jsx("h2",{style:{marginBottom:"16px",fontSize:U?"20px":"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Organization Overview"}),u.jsx("div",{className:"grid grid-auto-fit",children:Gr().map((p,C)=>u.jsx(qc,{card:{...p,color:p.color||"#6b7280"},theme:i,isMobile:U},`organization-${p.id||C}`))})]}),hn==="drag"?u.jsxs(AC,{onLayoutChange:$y,gridCols:4,gridRows:6,children:[yf().map((p,C)=>{var k,w;return u.jsxs("div",{className:"status-card card-hover",style:{backgroundColor:p.color,color:"white",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.15)",gridColumn:((k=p.position)==null?void 0:k.col)||C%4+1,gridRow:((w=p.position)==null?void 0:w.row)||1},item:{id:p.id,type:"status"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[u.jsx("span",{style:{fontSize:"20px"},children:p.icon}),u.jsx("h3",{style:{margin:0,fontSize:"16px",opacity:.9},children:p.title})]}),u.jsx("div",{style:{fontSize:U?"24px":"32px",fontWeight:"bold"},children:p.count.toLocaleString()}),p.description&&u.jsx("div",{style:{fontSize:"12px",opacity:.8,marginTop:"4px"},children:p.description})]},`status-${p.id}`)}),vf().map((p,C)=>{var k,w;return u.jsxs("div",{className:"priority-card card-hover",style:{backgroundColor:p.color,color:"white",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.15)",gridColumn:((k=p.position)==null?void 0:k.col)||C%4+1,gridRow:((w=p.position)==null?void 0:w.row)||2},item:{id:p.id,type:"priority"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[u.jsx("span",{style:{fontSize:"20px"},children:p.icon}),u.jsx("h3",{style:{margin:0,fontSize:"16px",opacity:.9},children:p.title})]}),u.jsx("div",{style:{fontSize:U?"24px":"32px",fontWeight:"bold"},children:p.count.toLocaleString()}),p.description&&u.jsx("div",{style:{fontSize:"12px",opacity:.8,marginTop:"4px"},children:p.description})]},`priority-${p.id}`)}),Gr().map((p,C)=>{var k,w;return u.jsxs("div",{className:"organization-card card-hover",style:{backgroundColor:p.color,color:"white",boxShadow:"0 8px 25px rgba(0, 0, 0, 0.15)",gridColumn:((k=p.position)==null?void 0:k.col)||C%4+1,gridRow:((w=p.position)==null?void 0:w.row)||3},item:{id:p.id,type:"organization"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[u.jsx("span",{style:{fontSize:"20px"},children:p.icon}),u.jsx("h3",{style:{margin:0,fontSize:"16px",opacity:.9},children:p.title})]}),u.jsx("div",{style:{fontSize:U?"24px":"32px",fontWeight:"bold"},children:p.count.toLocaleString()}),p.description&&u.jsx("div",{style:{fontSize:"12px",opacity:.8,marginTop:"4px"},children:p.description})]},`organization-${p.id}`)}),((Tf=(zf=q.charts)==null?void 0:zf.organizationChart)==null?void 0:Tf.visible)&&u.jsxs("div",{className:"card",style:{padding:"16px",gridColumn:((Af=q.charts.organizationChart.position)==null?void 0:Af.col)||1,gridRow:((Mf=q.charts.organizationChart.position)==null?void 0:Mf.row)||4},item:{id:"organizationChart",type:"chart"},children:[u.jsx("h2",{style:{margin:"0 0 12px 0",fontSize:U?"18px":"20px"},children:q.charts.organizationChart.title}),u.jsx(Yg,{data:_f,config:q.charts.organizationChart})]}),((Rf=(Of=q.charts)==null?void 0:Of.weeklyChart)==null?void 0:Rf.visible)&&u.jsxs("div",{className:"card",style:{padding:"16px",gridColumn:((Ef=q.charts.weeklyChart.position)==null?void 0:Ef.col)||2,gridRow:((Bf=q.charts.weeklyChart.position)==null?void 0:Bf.row)||4},item:{id:"weeklyChart",type:"chart"},children:[u.jsx("h2",{style:{margin:"0 0 12px 0",fontSize:U?"18px":"20px"},children:q.charts.weeklyChart.title}),u.jsx(Fg,{data:kf,config:q.charts.weeklyChart})]})]}):u.jsxs("div",{className:"grid grid-auto-fit",style:{marginBottom:U?"24px":"32px"},children:[((Nf=(Lf=q.charts)==null?void 0:Lf.organizationChart)==null?void 0:Nf.visible)!==!1&&u.jsxs("div",{className:"card",style:{padding:"16px"},children:[u.jsx("h2",{style:{margin:"0 0 12px 0",fontSize:U?"18px":"20px"},children:((Uf=(Hf=q.charts)==null?void 0:Hf.organizationChart)==null?void 0:Uf.title)||"Tickets by Restaurant (Top 10)"}),u.jsx(Yg,{data:_f,config:((Yf=q.charts)==null?void 0:Yf.organizationChart)||{}})]}),((Vf=(Ff=q.charts)==null?void 0:Ff.weeklyChart)==null?void 0:Vf.visible)!==!1&&u.jsxs("div",{className:"card",style:{padding:"16px"},children:[u.jsx("h2",{style:{margin:"0 0 12px 0",fontSize:U?"18px":"20px"},children:((qf=(Gf=q.charts)==null?void 0:Gf.weeklyChart)==null?void 0:qf.title)||"Tickets by Day (Last 7 Days)"}),u.jsx(Fg,{data:kf,config:((Xf=q.charts)==null?void 0:Xf.weeklyChart)||{}})]})]}),((Qf=t.stats)==null?void 0:Qf.recentActivity)&&u.jsxs("div",{className:"card",style:{background:`linear-gradient(135deg, ${i==="dark"?"#1f2937":"#ffffff"}, ${i==="dark"?"#374151":"#f8fafc"})`,border:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"},children:[u.jsx("h2",{style:{marginBottom:"20px",fontSize:U?"20px":"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"📋 Recent Activity"}),u.jsx("div",{style:{display:"flex",flexDirection:"column",gap:"12px"},children:t.stats.recentActivity.slice(0,U?3:8).map((p,C)=>u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:U?"flex-start":"center",flexDirection:U?"column":"row",padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",borderLeft:`4px solid ${C<3?"#10b981":"#64748b"}`,transition:"all 0.2s ease"},className:"card-hover",children:[u.jsxs("div",{style:{flex:1},children:[u.jsx("span",{style:{fontWeight:"bold",color:i==="dark"?"#e5e7eb":"#1e293b",fontSize:"14px"},children:p.key}),u.jsx("span",{style:{marginLeft:U?"0":"12px",color:i==="dark"?"#d1d5db":"#64748b",display:U?"block":"inline",fontSize:"13px"},children:p.summary})]}),u.jsxs("div",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",marginTop:U?"8px":"0"},children:[p.status," • ",new Date(p.updated).toLocaleTimeString()]})]},p.key||C))})]}),Mt&&u.jsxs("div",{className:"card",style:{marginTop:"32px",background:`linear-gradient(135deg, ${i==="dark"?"#1f2937":"#ffffff"}, ${i==="dark"?"#374151":"#f8fafc"})`,border:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px",flexWrap:"wrap",gap:"12px"},children:[u.jsxs("h2",{style:{margin:0,fontSize:U?"20px":"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b",display:"flex",alignItems:"center",gap:"8px"},children:["📋 All Tickets (",_l.length.toLocaleString(),")",L&&u.jsx("div",{style:{width:"16px",height:"16px",border:"2px solid #e2e8f0",borderTop:"2px solid #3b82f6",borderRadius:"50%",animation:"spin 1s linear infinite"}})]}),u.jsxs("div",{style:{display:"flex",gap:"8px",alignItems:"center",flexWrap:"wrap"},children:[(h||m||S||x||_||A||d)&&u.jsxs("div",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",padding:"4px 8px",backgroundColor:i==="dark"?"#374151":"#f3f4f6",borderRadius:"4px"},children:["Filtered: ",_l.length," of ",t.totalCount," tickets"]}),(h||m||S||x||_||A||d)&&u.jsx("button",{onClick:()=>{g(""),v(""),b(""),y(""),D(""),j(""),c("")},style:{padding:"6px 12px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"6px",fontSize:"12px",cursor:"pointer"},children:"🗑️ Clear Filters"})]})]}),u.jsxs("div",{style:{display:"flex",gap:"12px",marginBottom:"20px",flexWrap:"wrap",alignItems:"center"},children:[u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"Status"}),u.jsxs("select",{value:h,onChange:p=>g(p.target.value),style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"120px"},children:[u.jsx("option",{value:"",children:"All Statuses"}),Object.keys(((Zf=t.stats)==null?void 0:Zf.statusCounts)||{}).map(p=>u.jsx("option",{value:p,children:p},p))]})]}),u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"Priority"}),u.jsxs("select",{value:S,onChange:p=>b(p.target.value),style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"120px"},children:[u.jsx("option",{value:"",children:"All Priorities"}),Object.keys(((Wf=t.stats)==null?void 0:Wf.priorityCounts)||{}).map(p=>u.jsx("option",{value:p,children:p},p))]})]}),u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"Assignee"}),u.jsxs("select",{value:m,onChange:p=>v(p.target.value),style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"180px"},children:[u.jsx("option",{value:"",children:"All Assignees"}),u.jsx("option",{value:"unassigned",children:"🚫 Unassigned"}),ci().map(p=>u.jsxs("option",{value:p.displayName,children:["👤 ",p.displayName]},p.key))]})]}),u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"Date Range"}),u.jsxs("select",{value:x,onChange:p=>{y(p.target.value),p.target.value!=="custom"&&(D(""),j(""))},style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"140px"},children:[u.jsx("option",{value:"",children:"All Time"}),u.jsx("option",{value:"today",children:"Today"}),u.jsx("option",{value:"yesterday",children:"Yesterday"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"custom",children:"Custom Range"})]})]}),x==="custom"&&u.jsxs(u.Fragment,{children:[u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"From Date"}),u.jsx("input",{type:"date",value:_,onChange:p=>D(p.target.value),style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"140px"}})]}),u.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"To Date"}),u.jsx("input",{type:"date",value:A,onChange:p=>j(p.target.value),style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px",minWidth:"140px"}})]})]}),u.jsxs("div",{style:{display:"flex",gap:"8px",alignItems:"center",flexWrap:"wrap"},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b"},children:"Show:"}),u.jsxs("select",{value:Cl,onChange:p=>{Vy(Number(p.target.value)),kl(1)},style:{padding:"4px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:25,children:"25"}),u.jsx("option",{value:50,children:"50"}),u.jsx("option",{value:100,children:"100"}),u.jsx("option",{value:200,children:"200"})]})]}),u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[u.jsx("input",{type:"checkbox",id:"showLatestFirst",checked:T,onChange:p=>M(p.target.checked),style:{transform:"scale(1.2)"}}),u.jsx("label",{htmlFor:"showLatestFirst",style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",cursor:"pointer",userSelect:"none"},children:"📅 Latest First"})]}),u.jsxs("select",{value:`${oe}-${Qe}`,onChange:p=>{const[C,k]=p.target.value.split("-");gf(C),Hr(k)},style:{padding:"6px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[u.jsx("option",{value:"updated-desc",children:"Updated (Newest)"}),u.jsx("option",{value:"updated-asc",children:"Updated (Oldest)"}),u.jsx("option",{value:"created-desc",children:"Created (Newest)"}),u.jsx("option",{value:"created-asc",children:"Created (Oldest)"}),u.jsx("option",{value:"key-asc",children:"Key (A-Z)"}),u.jsx("option",{value:"key-desc",children:"Key (Z-A)"}),u.jsx("option",{value:"status-asc",children:"Status (A-Z)"}),u.jsx("option",{value:"priority-desc",children:"Priority"})]}),u.jsxs("span",{style:{fontSize:"14px",color:i==="dark"?"#9ca3af":"#64748b"},children:["Page ",oi," of ",Zn()]})]})]}),u.jsx("div",{style:{overflowX:"auto"},children:u.jsxs("table",{style:{width:"100%",borderCollapse:"collapse",fontSize:U?"12px":"14px"},children:[u.jsx("thead",{children:u.jsxs("tr",{style:{backgroundColor:i==="dark"?"#374151":"#f8fafc",borderBottom:`2px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("key"),children:["Key ",oe==="key"&&(Qe==="asc"?"↑":"↓")]}),u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("summary"),children:["Summary ",oe==="summary"&&(Qe==="asc"?"↑":"↓")]}),u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("status"),children:["Status ",oe==="status"&&(Qe==="asc"?"↑":"↓")]}),u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("priority"),children:["Priority ",oe==="priority"&&(Qe==="asc"?"↑":"↓")]}),u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("assignee"),children:["Assignee ",oe==="assignee"&&(Qe==="asc"?"↑":"↓")]}),u.jsx("th",{style:{padding:"12px 8px",textAlign:"left",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},children:"Organization"}),u.jsxs("th",{style:{padding:"12px 8px",textAlign:"left",cursor:"pointer",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1f2937"},onClick:()=>Qn("updated"),children:["Updated ",oe==="updated"&&(Qe==="asc"?"↑":"↓")]})]})}),u.jsx("tbody",{children:Wy().map((p,C)=>u.jsxs("tr",{style:{borderBottom:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,backgroundColor:C%2===0?i==="dark"?"#1f2937":"#ffffff":i==="dark"?"#374151":"#f8fafc"},children:[u.jsx("td",{style:{padding:"12px 8px",fontWeight:"600",color:"#3b82f6"},children:p.key}),u.jsx("td",{style:{padding:"12px 8px",maxWidth:U?"150px":"300px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:i==="dark"?"#e5e7eb":"#1f2937"},children:tt(p,"summary")}),u.jsx("td",{style:{padding:"12px 8px"},children:u.jsx("span",{style:{padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:tt(p,"status")==="Open"?"#fef3c7":tt(p,"status")==="In Progress"?"#dbeafe":tt(p,"status")==="Resolved"?"#d1fae5":"#f3f4f6",color:tt(p,"status")==="Open"?"#92400e":tt(p,"status")==="In Progress"?"#1e40af":tt(p,"status")==="Resolved"?"#065f46":"#374151"},children:tt(p,"status")})}),u.jsx("td",{style:{padding:"12px 8px"},children:u.jsx("span",{style:{padding:"4px 8px",borderRadius:"4px",fontSize:"12px",fontWeight:"500",backgroundColor:tt(p,"priority")==="Urgent"?"#fee2e2":tt(p,"priority")==="High"?"#fed7aa":tt(p,"priority")==="Medium"?"#fef3c7":"#f0f9ff",color:tt(p,"priority")==="Urgent"?"#991b1b":tt(p,"priority")==="High"?"#9a3412":tt(p,"priority")==="Medium"?"#92400e":"#0369a1"},children:tt(p,"priority")})}),u.jsx("td",{style:{padding:"12px 8px",color:i==="dark"?"#d1d5db":"#64748b"},children:u.jsxs("div",{children:[u.jsx("div",{style:{fontWeight:"500"},children:ri(p).displayName}),ri(p).emailAddress&&ri(p).key!=="unassigned"&&u.jsx("div",{style:{fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b",marginTop:"2px"},children:ri(p).emailAddress})]})}),u.jsx("td",{style:{padding:"12px 8px",color:i==="dark"?"#d1d5db":"#64748b",fontWeight:"500"},children:qn(p)}),u.jsx("td",{style:{padding:"12px 8px",color:i==="dark"?"#9ca3af":"#64748b",fontSize:"12px"},children:tt(p,"updated")?new Date(tt(p,"updated")).toLocaleDateString():"N/A"})]},p.key||C))})]})}),Zn()>1&&u.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",gap:"8px",marginTop:"20px",flexWrap:"wrap"},children:[u.jsx("button",{onClick:()=>kl(Math.max(1,oi-1)),disabled:oi===1,className:"btn",style:{backgroundColor:oi===1?"#9ca3af":"#6b7280",color:"white",padding:"6px 12px",fontSize:"14px"},children:"← Previous"}),u.jsxs("span",{style:{padding:"6px 12px",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[oi," / ",Zn()]}),u.jsx("button",{onClick:()=>kl(Math.min(Zn(),oi+1)),disabled:oi===Zn(),className:"btn",style:{backgroundColor:oi===Zn()?"#9ca3af":"#6b7280",color:"white",padding:"6px 12px",fontSize:"14px"},children:"Next →"})]})]}),F&&u.jsx("div",{className:"card",style:{marginBottom:"24px",background:`linear-gradient(135deg, ${i==="dark"?"#1f2937":"#ffffff"}, ${i==="dark"?"#374151":"#f8fafc"})`,border:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"},children:u.jsx(MC,{config:q,onConfigChange:be,theme:i,dashboardData:t,getAllAssignees:ci})}),Ka&&u.jsx("div",{className:"card",style:{marginBottom:"24px",background:`linear-gradient(135deg, ${i==="dark"?"#1f2937":"#ffffff"}, ${i==="dark"?"#374151":"#f8fafc"})`,border:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,boxShadow:"0 8px 25px rgba(0, 0, 0, 0.1)"},children:u.jsx("div",{style:{padding:"20px"},children:u.jsx(RC,{config:q,onConfigChange:be,theme:i,dashboardData:t,autoApply:Nr,onAutoApplyChange:Yy})})}),$a&&u.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e3,display:"flex",justifyContent:"center",alignItems:"center",padding:"20px"},children:u.jsxs("div",{style:{backgroundColor:i==="dark"?"#1f2937":"white",borderRadius:"12px",padding:"24px",maxWidth:"900px",width:"100%",maxHeight:"80vh",overflowY:"auto",boxShadow:"0 20px 50px rgba(0, 0, 0, 0.3)"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[u.jsx("h2",{style:{margin:0,fontSize:"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Cards Configuration"}),u.jsx("button",{onClick:()=>Lr(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:i==="dark"?"#9ca3af":"#64748b"},children:"✕"})]}),u.jsx(OC,{config:q,onConfigChange:be,theme:i,dashboardData:t,activeConfigTab:wt,setActiveConfigTab:ot,updateCardConfig:nt,addNewCard:wl,getAllAssignees:ci})]})}),R&&u.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",zIndex:1e3,display:"flex",justifyContent:"center",alignItems:"center",padding:"20px"},children:u.jsxs("div",{style:{backgroundColor:i==="dark"?"#1f2937":"white",borderRadius:"12px",padding:"24px",maxWidth:"800px",width:"100%",maxHeight:"80vh",overflowY:"auto",boxShadow:"0 20px 50px rgba(0, 0, 0, 0.3)"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"24px"},children:[u.jsx("h2",{style:{margin:0,fontSize:"24px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"Dashboard Configuration"}),u.jsx("button",{onClick:()=>N(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:i==="dark"?"#9ca3af":"#64748b"},children:"✕"})]}),u.jsx("div",{style:{display:"flex",borderBottom:`1px solid ${i==="dark"?"#374151":"#e5e7eb"}`,marginBottom:"24px"},children:[{id:"status",label:"📊 Status Cards",icon:"📊"},{id:"priority",label:"🔥 Priority Cards",icon:"🔥"},{id:"organization",label:"🏢 Organization Cards",icon:"🏢"},{id:"general",label:"⚙️ General Settings",icon:"⚙️"}].map(p=>u.jsx("button",{onClick:()=>ot(p.id),style:{background:"none",border:"none",padding:"12px 16px",cursor:"pointer",fontSize:"14px",fontWeight:wt===p.id?"600":"400",color:wt===p.id?i==="dark"?"#60a5fa":"#2563eb":i==="dark"?"#9ca3af":"#64748b",borderBottom:wt===p.id?`2px solid ${i==="dark"?"#60a5fa":"#2563eb"}`:"2px solid transparent",transition:"all 0.2s ease"},children:p.label},p.id))}),wt==="status"&&u.jsxs("div",{style:{marginBottom:"32px"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"📊 Status Cards"}),u.jsx("button",{onClick:()=>wl("status"),className:"btn",style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px"},children:"+ Add Card"})]}),u.jsx("div",{style:{display:"grid",gap:"16px",maxHeight:"60vh",overflowY:"auto",paddingRight:"8px"},children:q.statusCards.map((p,C)=>u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[u.jsx("input",{type:"checkbox",checked:p.visible,onChange:k=>nt("status",p.id,{visible:k.target.checked}),style:{transform:"scale(1.2)"}}),u.jsx("input",{type:"text",placeholder:"Card Title",value:p.title,onChange:k=>nt("status",p.id,{title:k.target.value}),style:{flex:1,padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937"}}),u.jsxs("div",{style:{display:"flex",gap:"4px"},children:[u.jsx("button",{onClick:()=>Wn("status",p.id,"up"),disabled:C===0,style:{padding:"4px 8px",backgroundColor:C===0?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===0?"not-allowed":"pointer",fontSize:"12px"},children:"↑"}),u.jsx("button",{onClick:()=>Wn("status",p.id,"down"),disabled:C===q.statusCards.length-1,style:{padding:"4px 8px",backgroundColor:C===q.statusCards.length-1?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===q.statusCards.length-1?"not-allowed":"pointer",fontSize:"12px"},children:"↓"}),u.jsx("button",{onClick:()=>Xr("status",p.id),style:{padding:"4px 8px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:"✕"})]})]}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"8px",marginBottom:"12px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Icon"}),u.jsx("input",{type:"text",placeholder:"📊",value:p.icon,onChange:k=>nt("status",p.id,{icon:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Background Color"}),u.jsx("input",{type:"color",value:p.color,onChange:k=>nt("status",p.id,{color:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Text Color"}),u.jsx("input",{type:"color",value:p.textColor,onChange:k=>nt("status",p.id,{textColor:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]})]}),u.jsxs("div",{style:{marginBottom:"12px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Description"}),u.jsx("input",{type:"text",placeholder:"Card description",value:p.description,onChange:k=>nt("status",p.id,{description:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Date Filter"}),u.jsxs("select",{value:p.dateFilter||"all",onChange:k=>nt("status",p.id,{dateFilter:k.target.value,customDateFrom:k.target.value!=="custom"?"":p.customDateFrom,customDateTo:k.target.value!=="custom"?"":p.customDateTo}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Time"}),u.jsx("option",{value:"today",children:"Today"}),u.jsx("option",{value:"yesterday",children:"Yesterday"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"custom",children:"Custom Range"})]}),p.dateFilter==="custom"&&u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",marginTop:"8px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"2px"},children:"From Date"}),u.jsx("input",{type:"date",value:p.customDateFrom||"",onChange:k=>nt("status",p.id,{customDateFrom:k.target.value}),style:{width:"100%",padding:"4px 6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"11px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"2px"},children:"To Date"}),u.jsx("input",{type:"date",value:p.customDateTo||"",onChange:k=>nt("status",p.id,{customDateTo:k.target.value}),style:{width:"100%",padding:"4px 6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"11px"}})]})]})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Assignee Filter"}),u.jsxs("select",{value:p.assigneeFilter||"all",onChange:k=>nt("status",p.id,{assigneeFilter:k.target.value,selectedAssignees:k.target.value!=="specific"?[]:p.selectedAssignees}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Assignees"}),u.jsx("option",{value:"unassigned",children:"Unassigned Only"}),u.jsx("option",{value:"specific",children:"Specific Assignees"})]}),p.assigneeFilter==="specific"&&u.jsxs("div",{style:{marginTop:"8px"},children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Select Assignees"}),u.jsxs("div",{style:{maxHeight:"120px",overflowY:"auto",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",padding:"4px"},children:[ci().map(k=>u.jsxs("label",{style:{display:"flex",alignItems:"center",padding:"4px 8px",cursor:"pointer",fontSize:"11px",color:i==="dark"?"#e5e7eb":"#1f2937",borderRadius:"2px",":hover":{backgroundColor:i==="dark"?"#374151":"#f3f4f6"}},children:[u.jsx("input",{type:"checkbox",checked:(p.selectedAssignees||[]).includes(k.displayName),onChange:w=>{const E=p.selectedAssignees||[],B=w.target.checked?[...E,k.displayName]:E.filter(Q=>Q!==k.displayName);nt("status",p.id,{selectedAssignees:B})},style:{marginRight:"8px",transform:"scale(0.9)"}}),u.jsxs("div",{children:[u.jsx("div",{style:{fontWeight:"500"},children:k.displayName}),k.emailAddress&&u.jsx("div",{style:{fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:k.emailAddress})]})]},k.key)),ci().length===0&&u.jsx("div",{style:{padding:"8px",textAlign:"center",fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b"},children:"No assignees found"})]}),(p.selectedAssignees||[]).length>0&&u.jsxs("div",{style:{marginTop:"4px",fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:["Selected: ",(p.selectedAssignees||[]).join(", ")]})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Jira Statuses (comma-separated)"}),u.jsx("input",{type:"text",placeholder:"Open, To Do, New",value:p.jiraStatuses.join(", "),onChange:k=>Qr("status",p.id,"statuses",k.target.value.split(",").map(w=>w.trim()).filter(w=>w)),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}}),u.jsxs("div",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",marginTop:"4px"},children:["Available: ",Zr("status").join(", ")]})]})]},p.id))})]}),wt==="priority"&&u.jsxs("div",{style:{marginBottom:"32px"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"🚨 Priority Cards"}),u.jsx("button",{onClick:()=>wl("priority"),className:"btn",style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px"},children:"+ Add Card"})]}),u.jsx("div",{style:{display:"grid",gap:"16px",maxHeight:"60vh",overflowY:"auto",paddingRight:"8px"},children:q.priorityCards.map((p,C)=>u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[u.jsx("input",{type:"checkbox",checked:p.visible,onChange:k=>nt("priority",p.id,{visible:k.target.checked}),style:{transform:"scale(1.2)"}}),u.jsx("input",{type:"text",placeholder:"Card Title",value:p.title,onChange:k=>nt("priority",p.id,{title:k.target.value}),style:{flex:1,padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937"}}),u.jsxs("div",{style:{display:"flex",gap:"4px"},children:[u.jsx("button",{onClick:()=>Wn("priority",p.id,"up"),disabled:C===0,style:{padding:"4px 8px",backgroundColor:C===0?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===0?"not-allowed":"pointer",fontSize:"12px"},children:"↑"}),u.jsx("button",{onClick:()=>Wn("priority",p.id,"down"),disabled:C===q.priorityCards.length-1,style:{padding:"4px 8px",backgroundColor:C===q.priorityCards.length-1?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===q.priorityCards.length-1?"not-allowed":"pointer",fontSize:"12px"},children:"↓"}),u.jsx("button",{onClick:()=>Xr("priority",p.id),style:{padding:"4px 8px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:"✕"})]})]}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"8px",marginBottom:"12px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Icon"}),u.jsx("input",{type:"text",placeholder:"🚨",value:p.icon,onChange:k=>nt("priority",p.id,{icon:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Background Color"}),u.jsx("input",{type:"color",value:p.color,onChange:k=>nt("priority",p.id,{color:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Text Color"}),u.jsx("input",{type:"color",value:p.textColor,onChange:k=>nt("priority",p.id,{textColor:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]})]}),u.jsxs("div",{style:{marginBottom:"12px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Description"}),u.jsx("input",{type:"text",placeholder:"Card description",value:p.description,onChange:k=>nt("priority",p.id,{description:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Date Filter"}),u.jsxs("select",{value:p.dateFilter||"all",onChange:k=>nt("priority",p.id,{dateFilter:k.target.value,customDateFrom:k.target.value!=="custom"?"":p.customDateFrom,customDateTo:k.target.value!=="custom"?"":p.customDateTo}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Time"}),u.jsx("option",{value:"today",children:"Today"}),u.jsx("option",{value:"yesterday",children:"Yesterday"}),u.jsx("option",{value:"last7days",children:"Last 7 Days"}),u.jsx("option",{value:"last30days",children:"Last 30 Days"}),u.jsx("option",{value:"last90days",children:"Last 90 Days"}),u.jsx("option",{value:"custom",children:"Custom Range"})]}),p.dateFilter==="custom"&&u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"8px",marginTop:"8px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"2px"},children:"From Date"}),u.jsx("input",{type:"date",value:p.customDateFrom||"",onChange:k=>nt("priority",p.id,{customDateFrom:k.target.value}),style:{width:"100%",padding:"4px 6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"11px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"2px"},children:"To Date"}),u.jsx("input",{type:"date",value:p.customDateTo||"",onChange:k=>nt("priority",p.id,{customDateTo:k.target.value}),style:{width:"100%",padding:"4px 6px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"11px"}})]})]})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Assignee Filter"}),u.jsxs("select",{value:p.assigneeFilter||"all",onChange:k=>nt("priority",p.id,{assigneeFilter:k.target.value,selectedAssignees:k.target.value!=="specific"?[]:p.selectedAssignees}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Assignees"}),u.jsx("option",{value:"unassigned",children:"Unassigned Only"}),u.jsx("option",{value:"specific",children:"Specific Assignees"})]}),p.assigneeFilter==="specific"&&u.jsxs("div",{style:{marginTop:"8px"},children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Select Assignees"}),u.jsxs("div",{style:{maxHeight:"120px",overflowY:"auto",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",padding:"4px"},children:[ci().map(k=>u.jsxs("label",{style:{display:"flex",alignItems:"center",padding:"4px 8px",cursor:"pointer",fontSize:"11px",color:i==="dark"?"#e5e7eb":"#1f2937",borderRadius:"2px"},children:[u.jsx("input",{type:"checkbox",checked:(p.selectedAssignees||[]).includes(k.displayName),onChange:w=>{const E=p.selectedAssignees||[],B=w.target.checked?[...E,k.displayName]:E.filter(Q=>Q!==k.displayName);nt("priority",p.id,{selectedAssignees:B})},style:{marginRight:"8px",transform:"scale(0.9)"}}),u.jsxs("div",{children:[u.jsx("div",{style:{fontWeight:"500"},children:k.displayName}),k.emailAddress&&u.jsx("div",{style:{fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:k.emailAddress})]})]},k.key)),ci().length===0&&u.jsx("div",{style:{padding:"8px",textAlign:"center",fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b"},children:"No assignees found"})]}),(p.selectedAssignees||[]).length>0&&u.jsxs("div",{style:{marginTop:"4px",fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:["Selected: ",(p.selectedAssignees||[]).join(", ")]})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Jira Priorities (comma-separated)"}),u.jsx("input",{type:"text",placeholder:"Urgent, High, Critical",value:p.jiraPriorities.join(", "),onChange:k=>Qr("priority",p.id,"priorities",k.target.value.split(",").map(w=>w.trim()).filter(w=>w)),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}}),u.jsxs("div",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",marginTop:"4px"},children:["Available: ",Zr("priority").join(", ")]})]})]},p.id))})]}),wt==="organization"&&u.jsxs("div",{style:{marginBottom:"32px"},children:[u.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"16px"},children:[u.jsx("h3",{style:{margin:0,fontSize:"18px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"🏢 Organization Cards"}),u.jsx("button",{onClick:()=>wl("organization"),className:"btn",style:{backgroundColor:"#10b981",color:"white",padding:"6px 12px",fontSize:"12px"},children:"+ Add Card"})]}),u.jsx("div",{style:{display:"grid",gap:"16px",maxHeight:"60vh",overflowY:"auto",paddingRight:"8px"},children:q.organizationCards.map((p,C)=>u.jsxs("div",{style:{padding:"16px",backgroundColor:i==="dark"?"#374151":"#f8fafc",borderRadius:"8px",border:`1px solid ${i==="dark"?"#4b5563":"#e2e8f0"}`},children:[u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"12px"},children:[u.jsx("input",{type:"checkbox",checked:p.visible,onChange:k=>nt("organization",p.id,{visible:k.target.checked}),style:{transform:"scale(1.2)"}}),u.jsx("input",{type:"text",placeholder:"Card Title",value:p.title,onChange:k=>nt("organization",p.id,{title:k.target.value}),style:{flex:1,padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937"}}),u.jsxs("div",{style:{display:"flex",gap:"4px"},children:[u.jsx("button",{onClick:()=>Wn("organization",p.id,"up"),disabled:C===0,style:{padding:"4px 8px",backgroundColor:C===0?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===0?"not-allowed":"pointer",fontSize:"12px"},children:"↑"}),u.jsx("button",{onClick:()=>Wn("organization",p.id,"down"),disabled:C===q.organizationCards.length-1,style:{padding:"4px 8px",backgroundColor:C===q.organizationCards.length-1?"#9ca3af":"#6b7280",color:"white",border:"none",borderRadius:"4px",cursor:C===q.organizationCards.length-1?"not-allowed":"pointer",fontSize:"12px"},children:"↓"}),u.jsx("button",{onClick:()=>Xr("organization",p.id),style:{padding:"4px 8px",backgroundColor:"#ef4444",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"12px"},children:"✕"})]})]}),u.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"8px",marginBottom:"12px"},children:[u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Icon"}),u.jsx("input",{type:"text",placeholder:"🏢",value:p.icon,onChange:k=>nt("organization",p.id,{icon:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Background Color"}),u.jsx("input",{type:"color",value:p.color,onChange:k=>nt("organization",p.id,{color:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Text Color"}),u.jsx("input",{type:"color",value:p.textColor,onChange:k=>nt("organization",p.id,{textColor:k.target.value}),style:{width:"100%",height:"32px",border:"none",borderRadius:"4px",cursor:"pointer"}})]})]}),u.jsxs("div",{style:{marginBottom:"12px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Description"}),u.jsx("input",{type:"text",placeholder:"Card description",value:p.description,onChange:k=>nt("organization",p.id,{description:k.target.value}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}})]}),u.jsxs("div",{style:{marginBottom:"16px"},children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Assignee Filter"}),u.jsxs("select",{value:p.assigneeFilter||"all",onChange:k=>nt("organization",p.id,{assigneeFilter:k.target.value,selectedAssignees:k.target.value!=="specific"?[]:p.selectedAssignees}),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"},children:[u.jsx("option",{value:"all",children:"All Assignees"}),u.jsx("option",{value:"unassigned",children:"Unassigned Only"}),u.jsx("option",{value:"specific",children:"Specific Assignees"})]}),p.assigneeFilter==="specific"&&u.jsxs("div",{style:{marginTop:"8px"},children:[u.jsx("label",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Select Assignees"}),u.jsxs("div",{style:{maxHeight:"120px",overflowY:"auto",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",padding:"4px"},children:[ci().map(k=>u.jsxs("label",{style:{display:"flex",alignItems:"center",padding:"4px 8px",cursor:"pointer",fontSize:"11px",color:i==="dark"?"#e5e7eb":"#1f2937",borderRadius:"2px"},children:[u.jsx("input",{type:"checkbox",checked:(p.selectedAssignees||[]).includes(k.displayName),onChange:w=>{const E=p.selectedAssignees||[],B=w.target.checked?[...E,k.displayName]:E.filter(Q=>Q!==k.displayName);nt("organization",p.id,{selectedAssignees:B})},style:{marginRight:"8px",transform:"scale(0.9)"}}),u.jsxs("div",{children:[u.jsx("div",{style:{fontWeight:"500"},children:k.displayName}),k.emailAddress&&u.jsx("div",{style:{fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:k.emailAddress})]})]},k.key)),ci().length===0&&u.jsx("div",{style:{padding:"8px",textAlign:"center",fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b"},children:"No assignees found"})]}),(p.selectedAssignees||[]).length>0&&u.jsxs("div",{style:{marginTop:"4px",fontSize:"10px",color:i==="dark"?"#9ca3af":"#64748b"},children:["Selected: ",(p.selectedAssignees||[]).join(", ")]})]})]}),u.jsxs("div",{children:[u.jsx("label",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",display:"block",marginBottom:"4px"},children:"Organization Names (comma-separated)"}),u.jsx("input",{type:"text",placeholder:"Company A, Organization B, Department C",value:p.organizationNames.join(", "),onChange:k=>Qr("organization",p.id,"organizations",k.target.value.split(",").map(w=>w.trim()).filter(w=>w)),style:{width:"100%",padding:"6px 8px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"4px",backgroundColor:i==="dark"?"#1f2937":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"12px"}}),u.jsxs("div",{style:{fontSize:"11px",color:i==="dark"?"#9ca3af":"#64748b",marginTop:"4px"},children:["Available: ",Zr("organization").join(", ")]})]})]},p.id))})]}),wt==="general"&&u.jsxs("div",{style:{marginBottom:"32px"},children:[u.jsx("h3",{style:{margin:"0 0 16px 0",fontSize:"18px",fontWeight:"600",color:i==="dark"?"#e5e7eb":"#1e293b"},children:"⚙️ General Settings"}),u.jsxs("div",{style:{marginBottom:"24px"},children:[u.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#374151",marginBottom:"8px"},children:"🎨 Theme"}),u.jsxs("select",{value:i,onChange:p=>n(p.target.value),style:{width:"200px",padding:"8px 12px",border:`1px solid ${i==="dark"?"#4b5563":"#d1d5db"}`,borderRadius:"6px",backgroundColor:i==="dark"?"#374151":"white",color:i==="dark"?"#e5e7eb":"#1f2937",fontSize:"14px"},children:[u.jsx("option",{value:"light",children:"☀️ Light"}),u.jsx("option",{value:"dark",children:"🌙 Dark"})]})]}),u.jsxs("div",{style:{marginBottom:"24px"},children:[u.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#374151",marginBottom:"8px"},children:"Auto Refresh Interval"}),u.jsx("p",{style:{fontSize:"12px",color:i==="dark"?"#9ca3af":"#64748b",margin:"0 0 8px 0"},children:"Dashboard refreshes every 5 seconds, server syncs every 10 seconds"})]}),u.jsxs("div",{style:{marginBottom:"24px"},children:[u.jsx("label",{style:{display:"block",fontSize:"14px",fontWeight:"500",color:i==="dark"?"#e5e7eb":"#374151",marginBottom:"8px"},children:"⚡ Performance"}),u.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px",marginBottom:"8px"},children:[u.jsx("input",{type:"checkbox",id:"showLatestFirstConfig",checked:T,onChange:p=>M(p.target.checked)}),u.jsx("label",{htmlFor:"showLatestFirstConfig",style:{fontSize:"14px",color:i==="dark"?"#e5e7eb":"#374151"},children:"📅 Show latest tickets first by default"})]})]})]}),u.jsxs("div",{style:{display:"flex",gap:"12px",justifyContent:"flex-end",borderTop:`1px solid ${i==="dark"?"#374151":"#e2e8f0"}`,paddingTop:"20px"},children:[u.jsx("button",{onClick:Cf,className:"btn btn-secondary",children:"📂 Load Saved"}),u.jsx("button",{onClick:Ky,className:"btn btn-primary",children:"💾 Save Configuration"}),u.jsx("button",{onClick:()=>N(!1),className:"btn",style:{backgroundColor:"#6b7280",color:"white"},children:"Cancel"})]})]})})]})]})};function LC(){return u.jsx(BC,{})}Pv.createRoot(document.getElementById("root")).render(u.jsx(z.StrictMode,{children:u.jsx(LC,{})}));
