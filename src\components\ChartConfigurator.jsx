import React, { useState } from 'react';

const ChartConfigurator = ({ chart, onChartUpdate, theme = 'light', availableAssignees = [] }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const updateChart = (updates) => {
    onChartUpdate(chart.id, { ...chart, ...updates });
  };

  const colorSchemes = {
    blue: { primary: '#3b82f6', secondary: 'rgba(59, 130, 246, 0.1)' },
    green: { primary: '#10b981', secondary: 'rgba(16, 185, 129, 0.1)' },
    red: { primary: '#ef4444', secondary: 'rgba(239, 68, 68, 0.1)' },
    purple: { primary: '#8b5cf6', secondary: 'rgba(139, 92, 246, 0.1)' },
    orange: { primary: '#f59e0b', secondary: 'rgba(245, 158, 11, 0.1)' }
  };

  const chartTypes = [
    { value: 'horizontal-bar', label: 'Horizontal Bar' },
    { value: 'vertical-bar', label: 'Vertical Bar' },
    { value: 'line', label: 'Line Chart' },
    { value: 'area', label: 'Area Chart' },
    { value: 'pie', label: 'Pie Chart' },
    { value: 'doughnut', label: 'Doughnut Chart' }
  ];

  const dataSourceOptions = chart.id === 'organizationChart' 
    ? [
        { value: 'organizations', label: 'Organizations' },
        { value: 'projects', label: 'Projects' },
        { value: 'assignees', label: 'Assignees' }
      ]
    : [
        { value: 'created', label: 'Created Tickets' },
        { value: 'updated', label: 'Updated Tickets' },
        { value: 'resolved', label: 'Resolved Tickets' }
      ];

  return (
    <div style={{
      border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      borderRadius: '8px',
      marginBottom: '16px',
      backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff'
    }}>
      {/* Header */}
      <div
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          padding: '12px 16px',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: isExpanded ? `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}` : 'none'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <input
            type="checkbox"
            checked={chart.visible}
            onChange={(e) => updateChart({ visible: e.target.checked })}
            onClick={(e) => e.stopPropagation()}
            style={{ cursor: 'pointer' }}
          />
          <span style={{
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontWeight: '500'
          }}>
            📊 {chart.title}
          </span>
          <span style={{
            fontSize: '12px',
            color: theme === 'dark' ? '#9ca3af' : '#6b7280',
            backgroundColor: theme === 'dark' ? '#374151' : '#f3f4f6',
            padding: '2px 6px',
            borderRadius: '4px'
          }}>
            {chart.type}
          </span>
        </div>
        <span style={{
          color: theme === 'dark' ? '#9ca3af' : '#6b7280',
          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)',
          transition: 'transform 0.2s ease'
        }}>
          ▼
        </span>
      </div>

      {/* Configuration Panel */}
      {isExpanded && (
        <div style={{ padding: '16px' }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '16px'
          }}>
            {/* Basic Settings */}
            <div>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '12px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Chart Title
              </label>
              <input
                type="text"
                value={chart.title}
                onChange={(e) => updateChart({ title: e.target.value })}
                style={{
                  width: '100%',
                  padding: '6px 8px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '4px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}
              />
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '12px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Chart Type
              </label>
              <select
                value={chart.type}
                onChange={(e) => updateChart({ type: e.target.value })}
                style={{
                  width: '100%',
                  padding: '6px 8px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '4px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}
              >
                {chartTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '12px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Data Source
              </label>
              <select
                value={chart.dataSource}
                onChange={(e) => updateChart({ dataSource: e.target.value })}
                style={{
                  width: '100%',
                  padding: '6px 8px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '4px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}
              >
                {dataSourceOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '12px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Color Scheme
              </label>
              <select
                value={chart.colorScheme}
                onChange={(e) => updateChart({ colorScheme: e.target.value })}
                style={{
                  width: '100%',
                  padding: '6px 8px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '4px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}
              >
                {Object.keys(colorSchemes).map(scheme => (
                  <option key={scheme} value={scheme}>
                    {scheme.charAt(0).toUpperCase() + scheme.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            {/* Chart-specific settings */}
            {chart.id === 'organizationChart' && (
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  Limit (Top N)
                </label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={chart.limit || 10}
                  onChange={(e) => updateChart({ limit: parseInt(e.target.value) })}
                  style={{
                    width: '100%',
                    padding: '6px 8px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '4px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                />
              </div>
            )}

            {chart.id === 'weeklyChart' && (
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  Period (Days)
                </label>
                <select
                  value={chart.period || 7}
                  onChange={(e) => updateChart({ period: parseInt(e.target.value) })}
                  style={{
                    width: '100%',
                    padding: '6px 8px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '4px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                >
                  <option value={7}>Last 7 Days</option>
                  <option value={14}>Last 14 Days</option>
                  <option value={30}>Last 30 Days</option>
                  <option value={90}>Last 90 Days</option>
                </select>
              </div>
            )}
          </div>

          {/* Filters */}
          <div style={{ marginTop: '16px' }}>
            <h4 style={{
              margin: '0 0 12px 0',
              fontSize: '14px',
              fontWeight: '500',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
            }}>
              Filters
            </h4>
            
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: '12px'
            }}>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  Assignee Filter
                </label>
                <select
                  value={chart.assigneeFilter || 'all'}
                  onChange={(e) => updateChart({ assigneeFilter: e.target.value })}
                  style={{
                    width: '100%',
                    padding: '6px 8px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '4px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                >
                  <option value="all">All Assignees</option>
                  <option value="specific">Specific Assignees</option>
                  <option value="unassigned">Unassigned Only</option>
                </select>
              </div>

              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '4px',
                  fontSize: '12px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  Date Filter
                </label>
                <select
                  value={chart.dateFilter || 'all'}
                  onChange={(e) => updateChart({ dateFilter: e.target.value })}
                  style={{
                    width: '100%',
                    padding: '6px 8px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '4px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>
            </div>
          </div>

          {/* Toggle Options */}
          <div style={{ marginTop: '16px' }}>
            <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
              {chart.id === 'organizationChart' && (
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  <input
                    type="checkbox"
                    checked={chart.showValues !== false}
                    onChange={(e) => updateChart({ showValues: e.target.checked })}
                  />
                  Show Values on Bars
                </label>
              )}
              
              {chart.id === 'weeklyChart' && (
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  cursor: 'pointer',
                  fontSize: '14px',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  <input
                    type="checkbox"
                    checked={chart.showFill !== false}
                    onChange={(e) => updateChart({ showFill: e.target.checked })}
                  />
                  Show Fill Area
                </label>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChartConfigurator;
