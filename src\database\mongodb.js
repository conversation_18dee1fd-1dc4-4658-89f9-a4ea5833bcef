import mongoose from 'mongoose';

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/jira-dashboard';

// Ticket Schema
const ticketSchema = new mongoose.Schema({
  key: { type: String, required: true, unique: true, index: true },
  summary: { type: String, required: true },
  status: { type: String, required: true, index: true },
  priority: { type: String, required: true, index: true },
  assignee: { type: String, index: true },
  project: { type: String, required: true, index: true },
  projectKey: { type: String, required: true, index: true },
  organization: { type: String, required: true, index: true },
  created: { type: Date, required: true, index: true },
  updated: { type: Date, required: true, index: true },
  description: { type: String },
  
  // Raw Jira fields for future use
  rawFields: {
    customfield_10000: mongoose.Schema.Types.Mixed,
    customfield_10001: mongoose.Schema.Types.Mixed,
    customfield_10002: mongoose.Schema.Types.Mixed,
    customfield_10003: mongoose.Schema.Types.Mixed,
    customfield_10004: mongoose.Schema.Types.Mixed,
    customfield_10005: mongoose.Schema.Types.Mixed,
    labels: [String],
    components: [mongoose.Schema.Types.Mixed]
  },
  
  // Metadata
  lastSynced: { type: Date, default: Date.now, index: true },
  isActive: { type: Boolean, default: true, index: true }
}, {
  timestamps: true,
  collection: 'tickets'
});

// Indexes for performance
ticketSchema.index({ created: -1 });
ticketSchema.index({ updated: -1 });
ticketSchema.index({ status: 1, priority: 1 });
ticketSchema.index({ organization: 1, status: 1 });
ticketSchema.index({ project: 1, status: 1 });
ticketSchema.index({ lastSynced: -1 });

// Create the model
const Ticket = mongoose.model('Ticket', ticketSchema);

// MongoDB connection class
class MongoDBService {
  constructor() {
    this.isConnected = false;
    this.connection = null;
  }

  async connect() {
    try {
      if (this.isConnected) {
        return this.connection;
      }

      console.log('🔌 Connecting to MongoDB...');
      this.connection = await mongoose.connect(MONGODB_URI, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        authSource: 'jira-dashboard', // Specify auth database
        retryWrites: true,
        w: 'majority'
      });

      this.isConnected = true;
      console.log('✅ Connected to MongoDB successfully');
      
      // Set up connection event listeners
      mongoose.connection.on('error', (error) => {
        console.error('❌ MongoDB connection error:', error);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        console.log('⚠️ MongoDB disconnected');
        this.isConnected = false;
      });

      return this.connection;
    } catch (error) {
      console.error('❌ Failed to connect to MongoDB:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.isConnected) {
        await mongoose.disconnect();
        this.isConnected = false;
        console.log('✅ Disconnected from MongoDB');
      }
    } catch (error) {
      console.error('❌ Error disconnecting from MongoDB:', error);
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }
}

// Create singleton instance
const mongoService = new MongoDBService();

export { mongoService, Ticket };
export default mongoService;
