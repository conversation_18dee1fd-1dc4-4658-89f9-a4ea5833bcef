import express from 'express';
import cors from 'cors';
import axios from 'axios';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module compatibility
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config();

// Import MongoDB services (dynamic import for ES modules)
let mongoDbService = null;
let incrementalSyncService = null;
let enhancedSyncService = null;
let realTimeService = null;

// Initialize MongoDB services
async function initializeMongoServices() {
  try {
    const mongoModule = await import('./src/services/mongoDbService.js');
    const syncModule = await import('./src/services/incrementalSyncService.js');
    const enhancedSyncModule = await import('./src/services/enhancedSyncService.js');
    const realTimeModule = await import('./src/services/realTimeService.js');

    mongoDbService = mongoModule.default;
    incrementalSyncService = syncModule.default;
    enhancedSyncService = enhancedSyncModule.default;
    realTimeService = realTimeModule.default;

    console.log('📊 MongoDB services loaded');

    // Initialize MongoDB connection
    if (mongoDbService) {
      await mongoDbService.connect();
    }

    // Initialize Enhanced Sync Service
    console.log('🔄 Initializing Enhanced Sync Service...');
    if (enhancedSyncService) {
      await enhancedSyncService.initialize();
      console.log('✅ Enhanced Sync Service initialized');

      // Resume sync from restart
      await enhancedSyncService.resumeSyncFromRestart();
    }

    // Initialize Incremental Sync Service (legacy)
    console.log('🔄 Initializing Incremental Sync Service...');
    if (incrementalSyncService) {
      await incrementalSyncService.initialize();
      console.log('✅ Incremental Sync Service initialized');
    }
  } catch (error) {
    console.warn('⚠️ MongoDB services not available:', error.message);
  }
}

const app = express();
const PORT = process.env.PORT || 3001;

// Enable CORS for all routes
app.use(cors());
app.use(express.json({ limit: '100mb' })); // Increase payload limit for large datasets
app.use(express.urlencoded({ limit: '100mb', extended: true }));

// Jira configuration
const JIRA_BASE_URL = process.env.VITE_JIRA_BASE_URL;
const JIRA_EMAIL = process.env.VITE_JIRA_EMAIL;
const JIRA_API_TOKEN = process.env.VITE_JIRA_API_TOKEN;

if (!JIRA_BASE_URL || !JIRA_EMAIL || !JIRA_API_TOKEN) {
  console.error('Missing Jira configuration. Please check your .env file.');
  process.exit(1);
}

// Create axios instance for Jira API
const jiraApi = axios.create({
  baseURL: `${JIRA_BASE_URL}/rest/api/3`,
  headers: {
    'Authorization': `Basic ${Buffer.from(`${JIRA_EMAIL}:${JIRA_API_TOKEN}`).toString('base64')}`,
    'Accept': 'application/json',
    'Content-Type': 'application/json'
  }
});

// Proxy endpoint for Jira search
app.get('/api/jira/search', async (req, res) => {
  try {
    const { jql, fields, maxResults = 1000 } = req.query;
    
    console.log('Proxying Jira request:', { jql, fields, maxResults });
    
    const response = await jiraApi.get('/search', {
      params: {
        jql,
        fields,
        maxResults,
        startAt: req.query.startAt || 0
      }
    });

    // Debug: Log the actual response details
    console.log('📊 Jira API Response Details:');
    console.log('   - Requested maxResults:', maxResults);
    console.log('   - Actual issues returned:', response.data.issues?.length || 0);
    console.log('   - Total available:', response.data.total || 0);
    console.log('   - Start at:', response.data.startAt || 0);

    res.json(response.data);
  } catch (error) {
    console.error('Jira API Error:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    // Handle specific error types
    if (error.response?.status === 429) {
      // Rate limiting
      console.warn('⚠️ Rate limit hit, backing off...');
      res.status(429).json({
        error: 'Rate limit exceeded. Please try again later.',
        retryAfter: error.response.headers['retry-after'] || 60
      });
    } else if (error.response?.status === 401) {
      // Authentication error
      res.status(401).json({ error: 'Authentication failed. Check Jira credentials.' });
    } else if (error.response?.status === 400) {
      // Bad request (usually JQL error)
      res.status(400).json({
        error: 'Invalid request. Check JQL syntax.',
        details: error.response?.data
      });
    } else if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
      // Timeout
      res.status(408).json({ error: 'Request timeout. Jira API is slow to respond.' });
    } else {
      // Generic error
      res.status(error.response?.status || 500).json({
        error: 'Failed to fetch data from Jira',
        details: error.response?.data || error.message
      });
    }
  }
});

// Get all projects
app.get('/api/jira/projects', async (req, res) => {
  try {
    console.log('Fetching all projects...');

    const response = await jiraApi.get('/project');

    res.json(response.data);
  } catch (error) {
    console.error('Jira Projects API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch projects from Jira',
      details: error.response?.data || error.message
    });
  }
});

// Debug endpoint to see actual Jira data
app.get('/api/jira/debug', async (req, res) => {
  try {
    console.log('Fetching comprehensive debug data...');

    const debug = {};

    // Test 1: Check if any tickets exist at all
    try {
      const allTickets = await jiraApi.get('/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'status,priority,created,assignee,project',
          maxResults: 10
        }
      });
      debug.allTickets = {
        total: allTickets.data.total,
        sample: allTickets.data.issues.slice(0, 3).map(ticket => ({
          key: ticket.key,
          status: ticket.fields.status.name,
          priority: ticket.fields.priority?.name || 'No Priority',
          project: ticket.fields.project.name,
          created: ticket.fields.created
        }))
      };
    } catch (error) {
      debug.allTickets = { error: error.message };
    }

    // Test 2: Check tickets from last year (2024)
    try {
      const lastYearTickets = await jiraApi.get('/search', {
        params: {
          jql: 'created >= "2024-01-01" ORDER BY created DESC',
          fields: 'status,priority,created,assignee,project',
          maxResults: 10
        }
      });
      debug.lastYearTickets = {
        total: lastYearTickets.data.total,
        sample: lastYearTickets.data.issues.slice(0, 3).map(ticket => ({
          key: ticket.key,
          status: ticket.fields.status.name,
          priority: ticket.fields.priority?.name || 'No Priority',
          project: ticket.fields.project.name,
          created: ticket.fields.created
        }))
      };
    } catch (error) {
      debug.lastYearTickets = { error: error.message };
    }

    // Test 3: Get available projects
    try {
      const projects = await jiraApi.get('/project');
      debug.projects = projects.data.map(p => ({
        key: p.key,
        name: p.name,
        projectTypeKey: p.projectTypeKey
      }));
    } catch (error) {
      debug.projects = { error: error.message };
    }

    // Test 4: Get current user info
    try {
      const user = await jiraApi.get('/myself');
      debug.currentUser = {
        accountId: user.data.accountId,
        displayName: user.data.displayName,
        emailAddress: user.data.emailAddress
      };
    } catch (error) {
      debug.currentUser = { error: error.message };
    }

    console.log('Comprehensive debug data:', JSON.stringify(debug, null, 2));
    res.json(debug);
  } catch (error) {
    console.error('Jira Debug API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch debug data from Jira',
      details: error.response?.data || error.message
    });
  }
});

// Test STORE project specifically
app.get('/api/jira/test-store', async (req, res) => {
  try {
    console.log('Testing STORE project...');

    const debug = {};

    // Test 1: Check if STORE project exists
    try {
      const storeProject = await jiraApi.get('/project/STORE');
      debug.storeProject = {
        key: storeProject.data.key,
        name: storeProject.data.name,
        projectTypeKey: storeProject.data.projectTypeKey,
        lead: storeProject.data.lead?.displayName
      };
    } catch (error) {
      debug.storeProject = { error: error.response?.status === 404 ? 'Project STORE not found' : error.message };
    }

    // Test 2: Try different project key variations
    const projectVariations = ['STORE', 'Store', 'store', 'ST'];
    debug.projectTests = {};

    for (const projectKey of projectVariations) {
      try {
        const response = await jiraApi.get('/search', {
          params: {
            jql: `project = "${projectKey}" ORDER BY created DESC`,
            fields: 'status,priority,created,project',
            maxResults: 5
          }
        });
        debug.projectTests[projectKey] = {
          total: response.data.total,
          found: response.data.total > 0
        };
      } catch (error) {
        debug.projectTests[projectKey] = { error: error.message };
      }
    }

    // Test 3: Search by project name
    try {
      const nameSearch = await jiraApi.get('/search', {
        params: {
          jql: 'project in projectsWhereUserHasPermission("Browse Projects") ORDER BY created DESC',
          fields: 'project',
          maxResults: 10
        }
      });
      debug.availableProjects = nameSearch.data.issues.map(issue => ({
        key: issue.fields.project.key,
        name: issue.fields.project.name
      }));
      // Remove duplicates
      debug.availableProjects = debug.availableProjects.filter((project, index, self) =>
        index === self.findIndex(p => p.key === project.key)
      );
    } catch (error) {
      debug.availableProjects = { error: error.message };
    }

    console.log('STORE project test:', JSON.stringify(debug, null, 2));
    res.json(debug);
  } catch (error) {
    console.error('STORE Test API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to test STORE project',
      details: error.response?.data || error.message
    });
  }
});

// Test what the frontend API would return with STORE project
app.get('/api/jira/test-frontend', async (req, res) => {
  try {
    console.log('Testing frontend API calls with STORE project...');

    const debug = {};

    // Test status query
    try {
      const statusResponse = await jiraApi.get('/search', {
        params: {
          jql: 'project = "STORE" ORDER BY created DESC',
          fields: 'status,priority,created,assignee,project',
          maxResults: 100
        }
      });

      const tickets = statusResponse.data.issues;
      const statusCounts = {};

      tickets.forEach(ticket => {
        const status = ticket.fields.status.name;
        statusCounts[status] = (statusCounts[status] || 0) + 1;
      });

      debug.statusTest = {
        totalTickets: statusResponse.data.total,
        sampleStatuses: Object.keys(statusCounts).slice(0, 10),
        statusCounts: statusCounts
      };
    } catch (error) {
      debug.statusTest = { error: error.message };
    }

    // Test priority query
    try {
      const priorityResponse = await jiraApi.get('/search', {
        params: {
          jql: 'project = "STORE" ORDER BY created DESC',
          fields: 'priority,created',
          maxResults: 100
        }
      });

      const tickets = priorityResponse.data.issues;
      const priorityCounts = {};

      tickets.forEach(ticket => {
        const priority = ticket.fields.priority?.name || 'No Priority';
        priorityCounts[priority] = (priorityCounts[priority] || 0) + 1;
      });

      debug.priorityTest = {
        totalTickets: priorityResponse.data.total,
        samplePriorities: Object.keys(priorityCounts).slice(0, 10),
        priorityCounts: priorityCounts
      };
    } catch (error) {
      debug.priorityTest = { error: error.message };
    }

    console.log('Frontend API test:', JSON.stringify(debug, null, 2));
    res.json(debug);
  } catch (error) {
    console.error('Frontend Test API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to test frontend API',
      details: error.response?.data || error.message
    });
  }
});

// Test ALL tickets from ALL projects
app.get('/api/jira/test-all-tickets', async (req, res) => {
  try {
    console.log('Testing ALL tickets from ALL projects...');

    const debug = {};

    // Test 1: Get ALL tickets with basic info
    try {
      const allTicketsResponse = await jiraApi.get('/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'key,summary,status,priority,project,created',
          maxResults: 100
        }
      });

      const tickets = allTicketsResponse.data.issues;
      const projects = [...new Set(tickets.map(t => t.fields.project.key))];
      const statuses = [...new Set(tickets.map(t => t.fields.status.name))];
      const priorities = [...new Set(tickets.map(t => t.fields.priority?.name).filter(Boolean))];

      debug.allTicketsTest = {
        totalTickets: allTicketsResponse.data.total,
        sampleSize: tickets.length,
        uniqueProjects: projects.length,
        projectsList: projects.slice(0, 10),
        uniqueStatuses: statuses.length,
        statusesList: statuses,
        uniquePriorities: priorities.length,
        prioritiesList: priorities,
        sampleTickets: tickets.slice(0, 5).map(ticket => ({
          key: ticket.key,
          project: ticket.fields.project.key,
          status: ticket.fields.status.name,
          priority: ticket.fields.priority?.name || 'No Priority',
          created: ticket.fields.created
        }))
      };
    } catch (error) {
      debug.allTicketsTest = { error: error.message };
    }

    console.log('ALL tickets test:', JSON.stringify(debug, null, 2));
    res.json(debug);
  } catch (error) {
    console.error('ALL Tickets Test API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to test ALL tickets',
      details: error.response?.data || error.message
    });
  }
});

// Get all projects
app.get('/api/jira/project', async (req, res) => {
  try {
    console.log('Fetching all projects...');
    const response = await jiraApi.get('/project');
    res.json(response.data);
  } catch (error) {
    console.error('Projects API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch projects',
      details: error.response?.data || error.message
    });
  }
});

// Get all statuses
app.get('/api/jira/status', async (req, res) => {
  try {
    console.log('Fetching all statuses...');
    const response = await jiraApi.get('/status');
    res.json(response.data);
  } catch (error) {
    console.error('Statuses API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch statuses',
      details: error.response?.data || error.message
    });
  }
});

// Get all priorities
app.get('/api/jira/priority', async (req, res) => {
  try {
    console.log('Fetching all priorities...');
    const response = await jiraApi.get('/priority');
    res.json(response.data);
  } catch (error) {
    console.error('Priorities API Error:', error.response?.data || error.message);
    res.status(error.response?.status || 500).json({
      error: 'Failed to fetch priorities',
      details: error.response?.data || error.message
    });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    jiraConfigured: !!(JIRA_BASE_URL && JIRA_EMAIL && JIRA_API_TOKEN)
  });
});

// Reset circuit breaker endpoint
app.post('/api/sync/reset-circuit-breaker', async (req, res) => {
  try {
    if (enhancedSyncService && typeof enhancedSyncService.resetCircuitBreaker === 'function') {
      enhancedSyncService.resetCircuitBreaker();
      res.json({
        success: true,
        message: 'Circuit breaker reset successfully',
        status: enhancedSyncService.getServiceStatus()
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Enhanced sync service not available'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Force service recovery endpoint
app.post('/api/sync/force-recovery', async (req, res) => {
  try {
    if (enhancedSyncService && typeof enhancedSyncService.forceServiceRecovery === 'function') {
      const recovered = await enhancedSyncService.forceServiceRecovery();
      res.json({
        success: recovered,
        message: recovered ? 'Service recovery successful' : 'Service recovery failed',
        status: enhancedSyncService.getServiceStatus()
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Enhanced sync service not available'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Debug endpoint to check organization extraction
app.get('/api/debug/organizations', async (req, res) => {
  try {
    console.log('🔍 Debug: Checking organization extraction...');

    const response = await jiraApi.get('/search', {
      params: {
        jql: 'ORDER BY created DESC',
        fields: 'key,summary,project,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
        maxResults: 10
      }
    });

    const tickets = response.data.issues || [];
    const debugInfo = tickets.map(ticket => {
      const fields = ticket.fields || {};
      return {
        key: ticket.key,
        summary: fields.summary?.substring(0, 50) + '...',
        projectKey: fields.project?.key,
        projectName: fields.project?.name,
        customFields: {
          cf10000: fields.customfield_10000,
          cf10001: fields.customfield_10001,
          cf10002: fields.customfield_10002,
          cf10003: fields.customfield_10003,
          cf10004: fields.customfield_10004,
          cf10005: fields.customfield_10005
        },
        labels: fields.labels,
        components: fields.components?.map(c => ({ name: c.name, id: c.id }))
      };
    });

    res.json({
      success: true,
      totalTickets: response.data.total,
      sampleTickets: debugInfo
    });

  } catch (error) {
    console.error('❌ Debug organizations error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== PERSISTENT STORAGE ENDPOINTS =====

const STORAGE_DIR = path.join(process.cwd(), 'storage');
const TICKETS_FILE = path.join(STORAGE_DIR, 'jira-tickets.json');
const METADATA_FILE = path.join(STORAGE_DIR, 'metadata.json');

// Ensure storage directory exists
async function ensureStorageDir() {
  try {
    await fs.access(STORAGE_DIR);
  } catch {
    await fs.mkdir(STORAGE_DIR, { recursive: true });
    console.log('📁 Created storage directory:', STORAGE_DIR);
  }
}

// Initialize storage directory
ensureStorageDir();

// Save tickets to file
app.post('/api/storage/save', async (req, res) => {
  try {
    const { tickets, timestamp, totalCount } = req.body;

    if (!tickets || !Array.isArray(tickets)) {
      return res.status(400).json({ success: false, error: 'Invalid tickets data' });
    }

    await ensureStorageDir();

    // Save tickets to file
    await fs.writeFile(TICKETS_FILE, JSON.stringify(tickets, null, 2));

    // Save metadata
    const metadata = {
      totalCount: totalCount || tickets.length,
      timestamp: timestamp || new Date().toISOString(),
      version: '1.0',
      savedAt: new Date().toISOString()
    };
    await fs.writeFile(METADATA_FILE, JSON.stringify(metadata, null, 2));

    console.log(`💾 Saved ${tickets.length} tickets to file storage`);

    res.json({
      success: true,
      message: `Saved ${tickets.length} tickets successfully`,
      savedCount: tickets.length,
      filePath: TICKETS_FILE
    });

  } catch (error) {
    console.error('❌ Storage save error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Load tickets from file
app.get('/api/storage/load', async (req, res) => {
  try {
    // Check if files exist
    try {
      await fs.access(TICKETS_FILE);
      await fs.access(METADATA_FILE);
    } catch {
      return res.json({
        success: true,
        tickets: [],
        message: 'No stored tickets found'
      });
    }

    // Load tickets and metadata
    const ticketsData = await fs.readFile(TICKETS_FILE, 'utf8');
    const metadataData = await fs.readFile(METADATA_FILE, 'utf8');

    const tickets = JSON.parse(ticketsData);
    const metadata = JSON.parse(metadataData);

    console.log(`📂 Loaded ${tickets.length} tickets from file storage`);

    res.json({
      success: true,
      tickets: tickets,
      totalCount: metadata.totalCount,
      timestamp: metadata.timestamp,
      savedAt: metadata.savedAt
    });

  } catch (error) {
    console.error('❌ Storage load error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Storage health check
app.get('/api/storage/health', async (req, res) => {
  try {
    await ensureStorageDir();
    res.json({
      success: true,
      status: 'healthy',
      storageDir: STORAGE_DIR
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      status: 'unhealthy',
      error: error.message
    });
  }
});

// Get storage statistics
app.get('/api/storage/stats', async (req, res) => {
  try {
    const stats = {
      ticketsFile: { exists: false, size: 0, tickets: 0 },
      metadataFile: { exists: false, size: 0 },
      storageDir: STORAGE_DIR
    };

    // Check tickets file
    try {
      const ticketsStats = await fs.stat(TICKETS_FILE);
      const ticketsData = await fs.readFile(TICKETS_FILE, 'utf8');
      const tickets = JSON.parse(ticketsData);

      stats.ticketsFile = {
        exists: true,
        size: ticketsStats.size,
        tickets: tickets.length,
        lastModified: ticketsStats.mtime
      };
    } catch {
      // File doesn't exist or is invalid
    }

    // Check metadata file
    try {
      const metadataStats = await fs.stat(METADATA_FILE);
      stats.metadataFile = {
        exists: true,
        size: metadataStats.size,
        lastModified: metadataStats.mtime
      };
    } catch {
      // File doesn't exist
    }

    res.json(stats);

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Clear storage
app.delete('/api/storage/clear', async (req, res) => {
  try {
    const deletedFiles = [];

    // Delete tickets file
    try {
      await fs.unlink(TICKETS_FILE);
      deletedFiles.push('tickets.json');
    } catch {
      // File might not exist
    }

    // Delete metadata file
    try {
      await fs.unlink(METADATA_FILE);
      deletedFiles.push('metadata.json');
    } catch {
      // File might not exist
    }

    console.log('🗑️ Cleared storage files:', deletedFiles);

    res.json({
      success: true,
      message: 'Storage cleared successfully',
      deletedFiles: deletedFiles
    });

  } catch (error) {
    console.error('❌ Storage clear error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== MONGODB API ENDPOINTS =====

// MongoDB health check
app.get('/api/mongodb/health', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.json({
        success: false,
        error: 'MongoDB service not initialized',
        available: false
      });
    }

    const health = await mongoDbService.getHealthCheck();
    res.json({
      success: true,
      ...health
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      available: false
    });
  }
});

// Get MongoDB statistics
app.get('/api/mongodb/stats', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const stats = await mongoDbService.getStats();
    if (stats) {
      res.json({
        success: true,
        stats: stats
      });
    } else {
      res.status(503).json({
        success: false,
        error: 'MongoDB not available'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get sync status
app.get('/api/mongodb/sync-status', async (req, res) => {
  try {
    if (!incrementalSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Sync service not available'
      });
    }

    const status = await incrementalSyncService.getSyncStatus();
    res.json({
      success: true,
      ...status
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Force full sync
app.post('/api/mongodb/force-sync', async (req, res) => {
  try {
    if (!incrementalSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Sync service not available'
      });
    }

    console.log('🔄 Force sync requested via API');
    const tickets = await incrementalSyncService.forceFullSync();

    res.json({
      success: true,
      message: 'Full sync completed',
      ticketsCount: tickets ? tickets.length : 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get total ticket count from Jira
app.get('/api/mongodb/total-count', async (req, res) => {
  try {
    if (!incrementalSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Sync service not available'
      });
    }

    const totalCount = await incrementalSyncService.getTotalTicketCount();

    res.json({
      success: true,
      totalTickets: totalCount,
      message: `Found ${totalCount.toLocaleString()} total tickets in Jira`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Clear MongoDB and restart fresh sync
app.post('/api/mongodb/clear-and-resync', async (req, res) => {
  try {
    if (!incrementalSyncService || !mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'Services not available'
      });
    }

    console.log('🗑️ Clearing MongoDB and starting fresh sync...');

    // Clear all existing tickets
    await mongoDbService.clearAllTickets();

    // Force a fresh full sync
    const tickets = await incrementalSyncService.forceFullSync();

    res.json({
      success: true,
      message: 'MongoDB cleared and fresh sync completed',
      ticketsCount: tickets ? tickets.length : 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get ticket count from MongoDB
app.get('/api/mongodb/count', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    // Get count directly from MongoDB
    const count = await mongoDbService.collection.countDocuments({});

    res.json({
      success: true,
      count: count,
      source: 'MongoDB Direct Count'
    });
  } catch (error) {
    console.error('❌ Error counting tickets in MongoDB:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get optimized dashboard data with aggregation pipeline
app.get('/api/mongodb/dashboard-tickets', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    console.log('📊 Loading optimized dashboard data...');
    const startTime = Date.now();

    // Get sample size for testing (if requested)
    const sampleSize = parseInt(req.query.sample) || null;
    const includeStats = req.query.stats === 'true';

    // Use aggregation pipeline for optimal performance
    const pipeline = [
      // Project only essential fields
      {
        $project: {
          key: 1,
          // Ensure summary comes through even if only available under fields.summary
          summary: { $ifNull: ['$summary', '$fields.summary'] },
          status: '$fields.status.name',
          priority: '$fields.priority.name',
          assignee: '$fields.assignee.displayName',
          project: '$fields.project.name',
          created: '$fields.created',
          updated: '$fields.updated',
          // Expose Organizations field for clients to parse
          customfield_10002: '$fields.customfield_10002',
          description: '$fields.description'
        }
      },
      // Add default values for missing fields
      {
        $addFields: {
          status: { $ifNull: ['$status', 'Unknown'] },
          priority: { $ifNull: ['$priority', 'Unknown'] },
          assignee: { $ifNull: ['$assignee', 'Unassigned'] },
          project: { $ifNull: ['$project', 'Unknown'] }
        }
      }
    ];

    // Add sample limit if requested
    if (sampleSize) {
      pipeline.push({ $limit: sampleSize });
    }

    // Execute aggregation
    const tickets = await mongoDbService.collection.aggregate(pipeline, {
      allowDiskUse: true,
      maxTimeMS: 30000 // 30 second timeout
    }).toArray();

    const loadTime = Date.now() - startTime;
    console.log(`✅ Loaded ${tickets.length} optimized tickets in ${loadTime}ms`);

    // Generate statistics if requested
    let stats = null;
    if (includeStats && tickets.length > 0) {
      const statsStartTime = Date.now();
      stats = await generateDashboardStats(mongoDbService.collection);
      const statsTime = Date.now() - statsStartTime;
      console.log(`📊 Generated dashboard stats in ${statsTime}ms`);
    }

    res.json({
      success: true,
      count: tickets.length,
      tickets: tickets,
      stats: stats,
      performance: {
        loadTimeMs: loadTime,
        ticketsPerSecond: Math.round(tickets.length / (loadTime / 1000))
      },
      source: 'MongoDB Aggregation Pipeline'
    });
  } catch (error) {
    console.error('❌ Error loading dashboard tickets:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Generate dashboard statistics using aggregation
async function generateDashboardStats(collection) {
  try {
    const pipeline = [
      {
        $project: {
          status: '$fields.status.name',
          priority: '$fields.priority.name',
          assignee: '$fields.assignee.displayName',
          project: '$fields.project.name',
          created: '$fields.created',
          updated: '$fields.updated'
        }
      },
      {
        $facet: {
          statusCounts: [
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
          ],
          priorityCounts: [
            { $group: { _id: '$priority', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
          ],
          organizationCounts: [
            {
              $addFields: {
                organization: {
                  $cond: {
                    if: { $ne: ['$fields.customfield_10002', null] },
                    then: {
                      $cond: {
                        if: { $eq: [{ $type: '$fields.customfield_10002' }, 'string'] },
                        then: '$fields.customfield_10002',
                        else: {
                          $cond: {
                            if: { $ne: ['$fields.customfield_10002.value', null] },
                            then: '$fields.customfield_10002.value',
                            else: {
                              $cond: {
                                if: { $ne: ['$fields.customfield_10002.displayName', null] },
                                then: '$fields.customfield_10002.displayName',
                                else: {
                                  $cond: {
                                    if: { $ne: ['$fields.customfield_10002.name', null] },
                                    then: '$fields.customfield_10002.name',
                                    else: 'N/A'
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    },
                    else: 'N/A'
                  }
                }
              }
            },
            { $group: { _id: '$organization', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
          ],
          assigneeCounts: [
            { $group: { _id: '$assignee', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 20 } // Top 20 assignees
          ],
          projectCounts: [
            { $group: { _id: '$project', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 20 } // Top 20 projects
          ],
          totalStats: [
            {
              $group: {
                _id: null,
                totalTickets: { $sum: 1 },
                oldestTicket: { $min: '$created' },
                newestTicket: { $max: '$created' },
                lastUpdated: { $max: '$updated' }
              }
            }
          ]
        }
      }
    ];

    const [result] = await collection.aggregate(pipeline).toArray();

    return {
      statusCounts: result.statusCounts.reduce((acc, item) => {
        acc[item._id || 'Unknown'] = item.count;
        return acc;
      }, {}),
      priorityCounts: result.priorityCounts.reduce((acc, item) => {
        acc[item._id || 'Unknown'] = item.count;
        return acc;
      }, {}),
      organizationCounts: result.organizationCounts.reduce((acc, item) => {
        acc[item._id || 'N/A'] = item.count;
        return acc;
      }, {}),
      assigneeCounts: result.assigneeCounts.reduce((acc, item) => {
        acc[item._id || 'Unassigned'] = item.count;
        return acc;
      }, {}),
      projectCounts: result.projectCounts.reduce((acc, item) => {
        acc[item._id || 'Unknown'] = item.count;
        return acc;
      }, {}),
      totalStats: result.totalStats[0] || {}
    };
  } catch (error) {
    console.error('❌ Error generating dashboard stats:', error);
    return null;
  }
}

// Get tickets with pagination from MongoDB (no sync)
app.get('/api/mongodb/tickets', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    // Parse pagination parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 1000; // Default to 1000 tickets per page
    const skip = (page - 1) * limit;

    console.log(`📊 Loading tickets page ${page} (${skip}-${skip + limit}) from MongoDB...`);

    // Get total count
    const totalCount = await mongoDbService.collection.countDocuments({});

    // Get paginated tickets
    const tickets = await mongoDbService.collection
      .find({})
      .skip(skip)
      .limit(limit)
      .toArray();

    console.log(`✅ Loaded ${tickets.length} tickets from MongoDB (page ${page}/${Math.ceil(totalCount / limit)})`);

    res.json({
      success: true,
      tickets: tickets || [],
      count: tickets ? tickets.length : 0,
      totalCount: totalCount,
      page: page,
      totalPages: Math.ceil(totalCount / limit),
      hasMore: skip + tickets.length < totalCount,
      source: 'MongoDB Direct Paginated'
    });
  } catch (error) {
    console.error('❌ Error loading tickets from MongoDB:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Clear MongoDB data
app.delete('/api/mongodb/clear', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const cleared = await mongoDbService.clearAllTickets();

    if (cleared) {
      res.json({
        success: true,
        message: 'MongoDB data cleared successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Failed to clear MongoDB data'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// High-performance dashboard endpoint with caching
app.get('/api/dashboard/high-performance', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    console.log('🚀 Loading high-performance dashboard data...');
    const startTime = Date.now();

    // Check for cached data
    const cacheKey = 'dashboard_data';
    const cacheTimeout = 30000; // 30 seconds

    // Use streaming for large datasets
    const streamData = req.query.stream === 'true';
    const pageSize = parseInt(req.query.pageSize) || 10000;
    const page = parseInt(req.query.page) || 0;

    if (streamData) {
      // Stream response for very large datasets
      res.writeHead(200, {
        'Content-Type': 'application/json',
        'Transfer-Encoding': 'chunked'
      });

      res.write('{"success":true,"data":[');

      let isFirst = true;
      const cursor = mongoDbService.collection.aggregate([
        {
          $project: {
            key: 1,
            summary: 1,
            status: '$fields.status.name',
            priority: '$fields.priority.name',
            assignee: '$fields.assignee.displayName',
            project: '$fields.project.name',
            created: '$fields.created',
            updated: '$fields.updated'
          }
        },
        { $skip: page * pageSize },
        { $limit: pageSize }
      ], { allowDiskUse: true });

      for await (const ticket of cursor) {
        if (!isFirst) res.write(',');
        res.write(JSON.stringify(ticket));
        isFirst = false;
      }

      res.write(']}');
      res.end();

      const loadTime = Date.now() - startTime;
      console.log(`✅ Streamed dashboard data in ${loadTime}ms`);
      return;
    }

    // Regular response with pagination
    const pipeline = [
      {
        $project: {
          key: 1,
          summary: 1,
          status: { $ifNull: ['$fields.status.name', 'Unknown'] },
          priority: { $ifNull: ['$fields.priority.name', 'Unknown'] },
          assignee: { $ifNull: ['$fields.assignee.displayName', 'Unassigned'] },
          project: { $ifNull: ['$fields.project.name', 'Unknown'] },
          created: '$fields.created',
          updated: '$fields.updated'
        }
      },
      { $skip: page * pageSize },
      { $limit: pageSize }
    ];

    // Execute with performance optimizations
    const tickets = await mongoDbService.collection.aggregate(pipeline, {
      allowDiskUse: true,
      maxTimeMS: 30000,
      hint: { 'fields.created': 1 } // Use index hint
    }).toArray();

    // Get total count efficiently
    const totalCount = await mongoDbService.collection.estimatedDocumentCount();

    const loadTime = Date.now() - startTime;
    console.log(`✅ Loaded ${tickets.length} tickets (page ${page + 1}) in ${loadTime}ms`);

    res.json({
      success: true,
      data: tickets,
      pagination: {
        page: page,
        pageSize: pageSize,
        totalCount: totalCount,
        totalPages: Math.ceil(totalCount / pageSize),
        hasNext: (page + 1) * pageSize < totalCount,
        hasPrev: page > 0
      },
      performance: {
        loadTimeMs: loadTime,
        ticketsPerSecond: Math.round(tickets.length / (loadTime / 1000))
      }
    });

  } catch (error) {
    console.error('❌ Error in high-performance dashboard:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Dashboard analytics endpoint
app.get('/api/dashboard/analytics', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    console.log('📊 Generating dashboard analytics...');
    const startTime = Date.now();

    // Advanced analytics pipeline
    const pipeline = [
      {
        $project: {
          status: '$fields.status.name',
          priority: '$fields.priority.name',
          assignee: '$fields.assignee.displayName',
          project: '$fields.project.name',
          created: { $dateFromString: { dateString: '$fields.created' } },
          updated: { $dateFromString: { dateString: '$fields.updated' } }
        }
      },
      {
        $facet: {
          statusDistribution: [
            { $group: { _id: '$status', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
          ],
          priorityDistribution: [
            { $group: { _id: '$priority', count: { $sum: 1 } } },
            { $sort: { count: -1 } }
          ],
          createdByMonth: [
            {
              $group: {
                _id: {
                  year: { $year: '$created' },
                  month: { $month: '$created' }
                },
                count: { $sum: 1 }
              }
            },
            { $sort: { '_id.year': 1, '_id.month': 1 } },
            { $limit: 12 } // Last 12 months
          ],
          topAssignees: [
            { $group: { _id: '$assignee', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
          ],
          topProjects: [
            { $group: { _id: '$project', count: { $sum: 1 } } },
            { $sort: { count: -1 } },
            { $limit: 10 }
          ]
        }
      }
    ];

    const [analytics] = await mongoDbService.collection.aggregate(pipeline, {
      allowDiskUse: true,
      maxTimeMS: 60000
    }).toArray();

    const loadTime = Date.now() - startTime;
    console.log(`✅ Generated analytics in ${loadTime}ms`);

    res.json({
      success: true,
      analytics: analytics,
      performance: {
        loadTimeMs: loadTime
      }
    });

  } catch (error) {
    console.error('❌ Error generating analytics:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== ENHANCED SYNC API ENDPOINTS =====

// Get enhanced sync status
app.get('/api/sync/status', async (req, res) => {
  try {
    if (!enhancedSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Enhanced sync service not available',
        healthy: false,
        circuitBreakerOpen: true,
        consecutiveErrors: 999
      });
    }

    const status = await enhancedSyncService.getSyncStatus();
    const serviceHealth = enhancedSyncService.getServiceStatus();

    res.json({
      success: true,
      ...status,
      ...serviceHealth
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      healthy: false,
      circuitBreakerOpen: true,
      consecutiveErrors: 999
    });
  }
});

// Trigger comprehensive sync
app.post('/api/sync/comprehensive', async (req, res) => {
  try {
    if (!enhancedSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Enhanced sync service not available'
      });
    }

    console.log('🔄 Manual comprehensive sync triggered');
    const result = await enhancedSyncService.performComprehensiveSync();

    res.json({
      success: true,
      message: 'Comprehensive sync completed',
      ticketsCount: result ? result.length : 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Trigger differential sync
app.post('/api/sync/differential', async (req, res) => {
  try {
    if (!enhancedSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Enhanced sync service not available'
      });
    }

    console.log('🔄 Manual differential sync triggered');
    const jiraTotalCount = await enhancedSyncService.getJiraTotalCount();
    const result = await enhancedSyncService.performDifferentialSync(jiraTotalCount);

    res.json({
      success: true,
      message: 'Differential sync completed',
      ticketsCount: result ? result.length : 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Trigger incremental sync
app.post('/api/sync/incremental', async (req, res) => {
  try {
    if (!enhancedSyncService) {
      return res.status(503).json({
        success: false,
        error: 'Enhanced sync service not available'
      });
    }

    console.log('🔄 Manual incremental sync triggered');
    const result = await enhancedSyncService.performIncrementalSync();

    res.json({
      success: true,
      message: 'Incremental sync completed',
      updatedTickets: result ? result.length : 0
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== CONFIGURATION API ENDPOINTS =====

// Get dashboard configuration
app.get('/api/config/dashboard', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    // Get configuration from MongoDB
    const configCollection = mongoDbService.db.collection('dashboard_config');
    const config = await configCollection.findOne({ _id: 'main_config' });

    if (config) {
      res.json({
        success: true,
        config: config.data
      });
    } else {
      // Return default configuration
      const defaultConfig = getDefaultDashboardConfig();
      res.json({
        success: true,
        config: defaultConfig
      });
    }
  } catch (error) {
    console.error('❌ Error loading dashboard configuration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Save dashboard configuration
app.post('/api/config/dashboard', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const { config } = req.body;

    if (!config) {
      return res.status(400).json({
        success: false,
        error: 'Configuration data is required'
      });
    }

    // Validate configuration
    const validationResult = validateDashboardConfig(config);
    if (!validationResult.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid configuration',
        details: validationResult.errors
      });
    }

    // Save configuration to MongoDB
    const configCollection = mongoDbService.db.collection('dashboard_config');
    await configCollection.replaceOne(
      { _id: 'main_config' },
      {
        _id: 'main_config',
        data: config,
        updatedAt: new Date(),
        version: Date.now()
      },
      { upsert: true }
    );

    console.log('✅ Dashboard configuration saved successfully');

    res.json({
      success: true,
      message: 'Configuration saved successfully'
    });
  } catch (error) {
    console.error('❌ Error saving dashboard configuration:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get configuration profiles
app.get('/api/config/profiles', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const configCollection = mongoDbService.db.collection('dashboard_config');
    const profiles = await configCollection.find({
      _id: { $ne: 'main_config' }
    }).toArray();

    res.json({
      success: true,
      profiles: profiles.map(profile => ({
        id: profile._id,
        name: profile.name,
        description: profile.description,
        createdAt: profile.createdAt,
        updatedAt: profile.updatedAt
      }))
    });
  } catch (error) {
    console.error('❌ Error loading configuration profiles:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Save configuration profile
app.post('/api/config/profiles', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const { name, description, config } = req.body;

    if (!name || !config) {
      return res.status(400).json({
        success: false,
        error: 'Name and configuration data are required'
      });
    }

    const profileId = `profile_${Date.now()}`;
    const configCollection = mongoDbService.db.collection('dashboard_config');

    await configCollection.insertOne({
      _id: profileId,
      name: name,
      description: description || '',
      data: config,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: 'Configuration profile saved successfully',
      profileId: profileId
    });
  } catch (error) {
    console.error('❌ Error saving configuration profile:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Default dashboard configuration
function getDefaultDashboardConfig() {
  return {
    statusCards: [
      {
        id: 'non_traites',
        title: 'Non traités',
        color: '#5e72e4',
        textColor: '#ffffff',
        jiraStatuses: ['Open'],
        visible: true,
        order: 0,
        assigneeFilter: null
      },
      {
        id: 'en_cours',
        title: 'En cours',
        color: '#11cdef',
        textColor: '#ffffff',
        jiraStatuses: ['In Progress'],
        visible: true,
        order: 1,
        assigneeFilter: null
      },
      {
        id: 'resolu',
        title: 'Résolu',
        color: '#2dce89',
        textColor: '#ffffff',
        jiraStatuses: ['Resolved', 'Closed'],
        visible: true,
        order: 2,
        assigneeFilter: null
      }
    ],
    priorityCards: [
      {
        id: 'tres_urgent',
        title: 'Incident Très Urgent',
        color: '#dc3545',
        textColor: '#ffffff',
        jiraPriorities: ['Urgent'],
        visible: true,
        order: 0,
        assigneeFilter: null
      },
      {
        id: 'urgent',
        title: 'Incident urgent',
        color: '#fd7e14',
        textColor: '#ffffff',
        jiraPriorities: ['Moyen'],
        visible: true,
        order: 1,
        assigneeFilter: null
      },
      {
        id: 'moyen',
        title: 'Incident moyen',
        color: '#6c757d',
        textColor: '#ffffff',
        jiraPriorities: ['Faible', 'Très faible'],
        visible: true,
        order: 2,
        assigneeFilter: null
      }
    ],
    assigneeFilters: [],
    theme: 'light',
    refreshInterval: 10000,
    enableNotifications: true,
    enableAnimations: true
  };
}

// Validate dashboard configuration
function validateDashboardConfig(config) {
  const errors = [];

  if (!config.statusCards || !Array.isArray(config.statusCards)) {
    errors.push('statusCards must be an array');
  }

  if (!config.priorityCards || !Array.isArray(config.priorityCards)) {
    errors.push('priorityCards must be an array');
  }

  if (config.refreshInterval && (config.refreshInterval < 1000 || config.refreshInterval > 60000)) {
    errors.push('refreshInterval must be between 1000 and 60000 milliseconds');
  }

  // Validate individual cards
  if (config.statusCards) {
    config.statusCards.forEach((card, index) => {
      if (!card.id) errors.push(`Status card ${index} missing id`);
      if (!card.title) errors.push(`Status card ${index} missing title`);
      if (!card.color) errors.push(`Status card ${index} missing color`);
    });
  }

  if (config.priorityCards) {
    config.priorityCards.forEach((card, index) => {
      if (!card.id) errors.push(`Priority card ${index} missing id`);
      if (!card.title) errors.push(`Priority card ${index} missing title`);
      if (!card.color) errors.push(`Priority card ${index} missing color`);
    });
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

// Get assignees for filtering
app.get('/api/dashboard/assignees', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    // Get assignees with ticket counts using aggregation
    const pipeline = [
      {
        $group: {
          _id: '$fields.assignee.displayName',
          email: { $first: '$fields.assignee.emailAddress' },
          ticketCount: { $sum: 1 }
        }
      },
      {
        $match: {
          _id: { $ne: null, $ne: '' }
        }
      },
      {
        $project: {
          id: '$_id',
          name: '$_id',
          email: '$email',
          ticketCount: '$ticketCount'
        }
      },
      {
        $sort: { ticketCount: -1 }
      }
    ];

    const assignees = await mongoDbService.collection.aggregate(pipeline).toArray();

    res.json({
      success: true,
      assignees: assignees
    });
  } catch (error) {
    console.error('❌ Error loading assignees:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get saved assignee filters
app.get('/api/config/assignee-filters', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const filtersCollection = mongoDbService.db.collection('assignee_filters');
    const filters = await filtersCollection.find({}).sort({ createdAt: -1 }).toArray();

    res.json({
      success: true,
      filters: filters
    });
  } catch (error) {
    console.error('❌ Error loading assignee filters:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Save assignee filter
app.post('/api/config/assignee-filters', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const { filter } = req.body;

    if (!filter || !filter.name) {
      return res.status(400).json({
        success: false,
        error: 'Filter name is required'
      });
    }

    const filtersCollection = mongoDbService.db.collection('assignee_filters');
    const filterId = `filter_${Date.now()}`;

    await filtersCollection.insertOne({
      _id: filterId,
      ...filter,
      id: filterId,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: 'Assignee filter saved successfully',
      filterId: filterId
    });
  } catch (error) {
    console.error('❌ Error saving assignee filter:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Delete assignee filter
app.delete('/api/config/assignee-filters/:filterId', async (req, res) => {
  try {
    if (!mongoDbService) {
      return res.status(503).json({
        success: false,
        error: 'MongoDB service not available'
      });
    }

    const { filterId } = req.params;
    const filtersCollection = mongoDbService.db.collection('assignee_filters');

    const result = await filtersCollection.deleteOne({ _id: filterId });

    if (result.deletedCount > 0) {
      res.json({
        success: true,
        message: 'Assignee filter deleted successfully'
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Filter not found'
      });
    }
  } catch (error) {
    console.error('❌ Error deleting assignee filter:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== SYSTEM API ENDPOINTS =====

// Auto-start configuration file
const AUTO_START_CONFIG_FILE = path.join(__dirname, 'config', 'auto-start.json');

// Ensure config directory exists
async function ensureConfigDir() {
  const configDir = path.dirname(AUTO_START_CONFIG_FILE);
  try {
    await fs.access(configDir);
  } catch (error) {
    await fs.mkdir(configDir, { recursive: true });
  }
}

// Get auto-start configuration
async function getAutoStartConfig() {
  try {
    await ensureConfigDir();
    const data = await fs.readFile(AUTO_START_CONFIG_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    // Return default config if file doesn't exist
    return {
      enabled: false,
      url: 'http://localhost:3000',
      fullScreen: true,
      lastModified: new Date().toISOString()
    };
  }
}

// Save auto-start configuration
async function saveAutoStartConfig(config) {
  try {
    await ensureConfigDir();
    config.lastModified = new Date().toISOString();
    await fs.writeFile(AUTO_START_CONFIG_FILE, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving auto-start config:', error);
    return false;
  }
}

// POST /api/system/auto-start - Toggle auto-start
app.post('/api/system/auto-start', async (req, res) => {
  try {
    const { enabled, url, fullScreen } = req.body;

    const config = await getAutoStartConfig();
    config.enabled = enabled;
    config.url = url || config.url;
    config.fullScreen = fullScreen !== undefined ? fullScreen : config.fullScreen;

    const saved = await saveAutoStartConfig(config);

    if (!saved) {
      return res.status(500).json({
        success: false,
        message: 'Failed to save auto-start configuration'
      });
    }

    res.json({
      success: true,
      message: enabled
        ? 'Auto-start enabled! Please run setup-auto-start.bat as Administrator to complete Windows configuration.'
        : 'Auto-start disabled! Please run setup-auto-start.bat as Administrator to remove Windows configuration.',
      config
    });

  } catch (error) {
    console.error('Error toggling auto-start:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/system/auto-start-status - Get auto-start status
app.get('/api/system/auto-start-status', async (req, res) => {
  try {
    const config = await getAutoStartConfig();

    res.json({
      success: true,
      enabled: config.enabled,
      config
    });

  } catch (error) {
    console.error('Error getting auto-start status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/system/info - Get system information
app.get('/api/system/info', async (req, res) => {
  try {
    const config = await getAutoStartConfig();

    res.json({
      success: true,
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        uptime: process.uptime()
      },
      autoStart: config
    });

  } catch (error) {
    console.error('Error getting system info:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// ===== REAL-TIME API ENDPOINTS =====

// Get real-time service status
app.get('/api/realtime/status', (req, res) => {
  try {
    if (!realTimeService) {
      return res.status(503).json({
        success: false,
        error: 'Real-time service not available'
      });
    }

    const status = realTimeService.getStatus();
    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Trigger manual update broadcast
app.post('/api/realtime/broadcast-update', async (req, res) => {
  try {
    if (!realTimeService) {
      return res.status(503).json({
        success: false,
        error: 'Real-time service not available'
      });
    }

    await realTimeService.checkForUpdates();

    res.json({
      success: true,
      message: 'Update broadcast triggered'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get real-time dashboard data (SSE endpoint)
app.get('/api/realtime/dashboard-stream', (req, res) => {
  try {
    if (!realTimeService) {
      return res.status(503).json({
        success: false,
        error: 'Real-time service not available'
      });
    }

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial data
    const sendUpdate = async () => {
      try {
        const stats = await realTimeService.getDashboardStats();
        const data = JSON.stringify({
          type: 'dashboard_update',
          data: { stats },
          timestamp: new Date().toISOString()
        });
        res.write(`data: ${data}\n\n`);
      } catch (error) {
        console.error('❌ Error sending SSE update:', error);
      }
    };

    // Send initial update
    sendUpdate();

    // Set up periodic updates
    const interval = setInterval(sendUpdate, 10000); // Every 10 seconds

    // Clean up on client disconnect
    req.on('close', () => {
      clearInterval(interval);
      res.end();
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Graceful shutdown handling
async function gracefulShutdown(signal) {
  console.log(`\n🛑 Received ${signal}, starting graceful shutdown...`);

  try {
    // Stop background sync
    if (enhancedSyncService) {
      await enhancedSyncService.shutdown();
    }

    // Stop real-time service
    if (realTimeService) {
      realTimeService.shutdown();
    }

    // Close MongoDB connections
    if (mongoDbService) {
      await mongoDbService.disconnect();
    }

    // Close server
    server.close(() => {
      console.log('✅ Server closed gracefully');
      process.exit(0);
    });

    // Force exit after 10 seconds
    setTimeout(() => {
      console.log('⚠️ Forcing exit after timeout');
      process.exit(1);
    }, 10000);

  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error.message);
    process.exit(1);
  }
}

// Register shutdown handlers
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGUSR2', () => gracefulShutdown('SIGUSR2')); // Nodemon restart

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  gracefulShutdown('unhandledRejection');
});

const server = app.listen(PORT, async () => {
  console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
  console.log(`📊 Jira Base URL: ${JIRA_BASE_URL}`);
  console.log(`📧 Jira Email: ${JIRA_EMAIL}`);
  console.log(`🔑 API Token: ${JIRA_API_TOKEN ? '***configured***' : 'NOT SET'}`);

  // Initialize MongoDB services
  console.log('🔄 Initializing MongoDB services...');
  await initializeMongoServices();

  // Initialize Real-Time Service
  if (realTimeService) {
    console.log('🔄 Initializing Real-Time Service...');
    realTimeService.initialize(server);
    console.log('✅ Real-Time Service initialized');
  }

  console.log('✅ Server initialization complete');
});

// Handle server errors
server.on('error', (error) => {
  console.error('❌ Server error:', error);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});
