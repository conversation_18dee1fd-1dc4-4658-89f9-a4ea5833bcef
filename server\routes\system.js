import express from 'express';
import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';
import util from 'util';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const router = express.Router();

const execAsync = util.promisify(exec);

// Auto-start configuration file
const AUTO_START_CONFIG_FILE = path.join(__dirname, '..', 'config', 'auto-start.json');

// Ensure config directory exists
async function ensureConfigDir() {
  const configDir = path.dirname(AUTO_START_CONFIG_FILE);
  try {
    await fs.access(configDir);
  } catch (error) {
    await fs.mkdir(configDir, { recursive: true });
  }
}

// Get auto-start configuration
async function getAutoStartConfig() {
  try {
    await ensureConfigDir();
    const data = await fs.readFile(AUTO_START_CONFIG_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    // Return default config if file doesn't exist
    return {
      enabled: false,
      url: 'http://localhost:3000',
      fullScreen: true,
      lastModified: new Date().toISOString()
    };
  }
}

// Save auto-start configuration
async function saveAutoStartConfig(config) {
  try {
    await ensureConfigDir();
    config.lastModified = new Date().toISOString();
    await fs.writeFile(AUTO_START_CONFIG_FILE, JSON.stringify(config, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving auto-start config:', error);
    return false;
  }
}

// Check if auto-start is enabled in Windows
async function checkWindowsAutoStart() {
  try {
    const { stdout } = await execAsync('reg query "HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run" /v JiraDashboard');
    return stdout.includes('JiraDashboard');
  } catch (error) {
    return false;
  }
}

// Check if scheduled task exists
async function checkScheduledTask() {
  try {
    const { stdout } = await execAsync('schtasks /query /tn "Jira Dashboard Auto-Start"');
    return stdout.includes('Jira Dashboard Auto-Start');
  } catch (error) {
    return false;
  }
}

// POST /api/system/auto-start - Toggle auto-start
router.post('/auto-start', async (req, res) => {
  try {
    const { enabled, url, fullScreen } = req.body;
    
    const config = await getAutoStartConfig();
    config.enabled = enabled;
    config.url = url || config.url;
    config.fullScreen = fullScreen !== undefined ? fullScreen : config.fullScreen;
    
    const saved = await saveAutoStartConfig(config);
    
    if (!saved) {
      return res.status(500).json({
        success: false,
        message: 'Failed to save auto-start configuration'
      });
    }
    
    // Try to configure Windows auto-start
    let windowsConfigured = false;
    let message = '';
    
    if (enabled) {
      try {
        // Use PowerShell to install auto-start
        const scriptPath = path.join(__dirname, '..', '..', 'scripts', 'install-auto-start.ps1');
        await execAsync(`powershell.exe -ExecutionPolicy Bypass -File "${scriptPath}" -Install`);
        windowsConfigured = true;
        message = 'Auto-start enabled successfully! The dashboard will start automatically on system boot.';
      } catch (error) {
        console.error('Error configuring Windows auto-start:', error);
        message = 'Auto-start configuration saved, but Windows auto-start setup failed. Please run setup-auto-start.bat as Administrator.';
      }
    } else {
      try {
        // Use PowerShell to uninstall auto-start
        const scriptPath = path.join(__dirname, '..', '..', 'scripts', 'install-auto-start.ps1');
        await execAsync(`powershell.exe -ExecutionPolicy Bypass -File "${scriptPath}" -Uninstall`);
        windowsConfigured = true;
        message = 'Auto-start disabled successfully!';
      } catch (error) {
        console.error('Error removing Windows auto-start:', error);
        message = 'Auto-start configuration saved, but Windows auto-start removal failed. Please run setup-auto-start.bat as Administrator.';
      }
    }
    
    res.json({
      success: true,
      message,
      config,
      windowsConfigured
    });
    
  } catch (error) {
    console.error('Error toggling auto-start:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/system/auto-start-status - Get auto-start status
router.get('/auto-start-status', async (req, res) => {
  try {
    const config = await getAutoStartConfig();
    const windowsRegistry = await checkWindowsAutoStart();
    const scheduledTask = await checkScheduledTask();
    
    res.json({
      success: true,
      enabled: config.enabled,
      config,
      windows: {
        registry: windowsRegistry,
        scheduledTask: scheduledTask,
        configured: windowsRegistry || scheduledTask
      }
    });
    
  } catch (error) {
    console.error('Error getting auto-start status:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

// GET /api/system/info - Get system information
router.get('/info', async (req, res) => {
  try {
    const config = await getAutoStartConfig();
    
    res.json({
      success: true,
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        uptime: process.uptime()
      },
      autoStart: config
    });
    
  } catch (error) {
    console.error('Error getting system info:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
});

export default router;
