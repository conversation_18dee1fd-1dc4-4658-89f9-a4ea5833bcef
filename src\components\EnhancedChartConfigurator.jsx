import React, { useState, useEffect, useCallback } from 'react';

const EnhancedChartConfigurator = ({ 
  config, 
  onConfigChange, 
  theme = 'light', 
  dashboardData,
  autoApply = true,
  onAutoApplyChange
}) => {
  const [localConfig, setLocalConfig] = useState(config);
  const [activeSection, setActiveSection] = useState('global');
  const [searchTerm, setSearchTerm] = useState('');

  // Auto-apply changes when enabled
  useEffect(() => {
    if (autoApply) {
      onConfigChange(localConfig);
    }
  }, [localConfig, autoApply, onConfigChange]);

  // Update local config when external config changes
  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  const updateChartConfig = useCallback((chartId, updates) => {
    setLocalConfig(prev => ({
      ...prev,
      charts: {
        ...prev.charts,
        [chartId]: {
          ...prev.charts[chartId],
          ...updates
        }
      }
    }));
  }, []);

  const updateGlobalSettings = useCallback((updates) => {
    setLocalConfig(prev => ({
      ...prev,
      globalChartSettings: {
        ...prev.globalChartSettings,
        ...updates
      }
    }));
  }, []);

  const applyToAllCharts = useCallback((settings) => {
    setLocalConfig(prev => ({
      ...prev,
      charts: Object.keys(prev.charts || {}).reduce((acc, chartId) => {
        acc[chartId] = {
          ...prev.charts[chartId],
          ...settings
        };
        return acc;
      }, {})
    }));
  }, []);

  const resetChart = useCallback((chartId) => {
    if (confirm('Reset this chart to default settings?')) {
      const defaultSettings = {
        visible: true,
        colorScheme: 'blue',
        showValues: true,
        dateFilter: 'all',
        assigneeFilter: 'all',
        selectedAssignees: []
      };
      updateChartConfig(chartId, defaultSettings);
    }
  }, [updateChartConfig]);

  const charts = localConfig.charts || {};
  const filteredCharts = Object.values(charts).filter(chart =>
    chart.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div style={{
      padding: '20px',
      backgroundColor: theme === 'dark' ? '#1f2937' : '#ffffff',
      borderRadius: '12px',
      border: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
      maxHeight: '80vh',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '20px',
        paddingBottom: '16px',
        borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`
      }}>
        <h3 style={{
          margin: 0,
          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
          fontSize: '20px',
          fontWeight: '600'
        }}>
          📊 Enhanced Chart Configuration
        </h3>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <label style={{ display: 'flex', alignItems: 'center', gap: '8px', fontSize: '14px' }}>
            <input
              type="checkbox"
              checked={autoApply}
              onChange={(e) => onAutoApplyChange?.(e.target.checked)}
              style={{ transform: 'scale(1.1)' }}
            />
            <span style={{ color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>Auto-Apply</span>
          </label>
          
          {!autoApply && (
            <button
              onClick={() => onConfigChange(localConfig)}
              style={{
                backgroundColor: '#10b981',
                color: 'white',
                padding: '8px 16px',
                borderRadius: '6px',
                border: 'none',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              Apply Changes
            </button>
          )}
        </div>
      </div>

      {/* Search */}
      <div style={{ marginBottom: '16px' }}>
        <input
          type="text"
          placeholder="🔍 Search charts..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          style={{
            width: '100%',
            padding: '10px 12px',
            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
            borderRadius: '6px',
            backgroundColor: theme === 'dark' ? '#374151' : 'white',
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Section Tabs */}
      <div style={{
        display: 'flex',
        gap: '4px',
        marginBottom: '20px',
        borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`
      }}>
        {[
          { id: 'global', label: '🌐 Global Settings', icon: '🌐' },
          { id: 'individual', label: '📊 Individual Charts', icon: '📊' },
          { id: 'presets', label: '⚡ Quick Presets', icon: '⚡' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveSection(tab.id)}
            style={{
              background: 'none',
              border: 'none',
              padding: '12px 16px',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: activeSection === tab.id ? '600' : '400',
              color: activeSection === tab.id
                ? (theme === 'dark' ? '#60a5fa' : '#2563eb')
                : (theme === 'dark' ? '#9ca3af' : '#64748b'),
              borderBottom: activeSection === tab.id
                ? `2px solid ${theme === 'dark' ? '#60a5fa' : '#2563eb'}`
                : '2px solid transparent',
              transition: 'all 0.2s ease'
            }}
          >
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content Area */}
      <div style={{ flex: 1, overflow: 'auto' }}>
        {/* Global Settings */}
        {activeSection === 'global' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
            {/* Date Filter Settings */}
            <div style={{
              padding: '16px',
              backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
              borderRadius: '8px',
              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
            }}>
              <h4 style={{
                margin: '0 0 12px 0',
                color: theme === 'dark' ? '#e5e7eb' : '#1e293b',
                fontSize: '16px',
                fontWeight: '500'
              }}>
                📅 Global Date Filter
              </h4>
              
              <div style={{ display: 'grid', gap: '12px', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' }}>
                <div>
                  <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                    Default Time Period
                  </label>
                  <select
                    value={localConfig.globalChartSettings?.defaultDateFilter || 'all'}
                    onChange={(e) => updateGlobalSettings({ defaultDateFilter: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      borderRadius: '6px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                      fontSize: '14px'
                    }}
                  >
                    <option value="all">All Time</option>
                    <option value="last7days">Last 7 Days</option>
                    <option value="last30days">Last 30 Days</option>
                    <option value="last90days">Last 90 Days</option>
                    <option value="thisyear">This Year</option>
                    <option value="lastyear">Last Year</option>
                    <option value="custom">Custom Date Range</option>
                  </select>
                </div>

                {/* Custom Date Range Inputs */}
                {localConfig.globalChartSettings?.defaultDateFilter === 'custom' && (
                  <>
                    <div>
                      <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                        Start Date
                      </label>
                      <input
                        type="date"
                        value={localConfig.globalChartSettings?.customStartDate || ''}
                        onChange={(e) => updateGlobalSettings({ customStartDate: e.target.value })}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          borderRadius: '6px',
                          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                          backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                          fontSize: '14px'
                        }}
                      />
                    </div>

                    <div>
                      <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                        End Date
                      </label>
                      <input
                        type="date"
                        value={localConfig.globalChartSettings?.customEndDate || ''}
                        onChange={(e) => updateGlobalSettings({ customEndDate: e.target.value })}
                        style={{
                          width: '100%',
                          padding: '8px 12px',
                          borderRadius: '6px',
                          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                          backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                          fontSize: '14px'
                        }}
                      />
                    </div>
                  </>
                )}

                <div>
                  <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                    Default Color Scheme
                  </label>
                  <select
                    value={localConfig.globalChartSettings?.defaultColorScheme || 'blue'}
                    onChange={(e) => updateGlobalSettings({ defaultColorScheme: e.target.value })}
                    style={{
                      width: '100%',
                      padding: '8px 12px',
                      borderRadius: '6px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                      fontSize: '14px'
                    }}
                  >
                    <option value="blue">Blue</option>
                    <option value="green">Green</option>
                    <option value="purple">Purple</option>
                    <option value="orange">Orange</option>
                    <option value="red">Red</option>
                  </select>
                </div>
              </div>

              {/* Apply to All Button */}
              <div style={{ marginTop: '12px', display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                <button
                  onClick={() => applyToAllCharts({
                    dateFilter: localConfig.globalChartSettings?.defaultDateFilter || 'all',
                    colorScheme: localConfig.globalChartSettings?.defaultColorScheme || 'blue',
                    ...(localConfig.globalChartSettings?.defaultDateFilter === 'custom' && {
                      customStartDate: localConfig.globalChartSettings?.customStartDate,
                      customEndDate: localConfig.globalChartSettings?.customEndDate
                    })
                  })}
                  style={{
                    backgroundColor: '#3b82f6',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Apply to All Charts
                </button>

                <button
                  onClick={() => applyToAllCharts({ assigneeFilter: 'all', selectedAssignees: [] })}
                  style={{
                    backgroundColor: '#ef4444',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Clear All Filters
                </button>

                <button
                  onClick={() => updateGlobalSettings({
                    defaultDateFilter: 'all',
                    customStartDate: '',
                    customEndDate: ''
                  })}
                  style={{
                    backgroundColor: '#6b7280',
                    color: 'white',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Reset Date Filter
                </button>
              </div>

              {/* Date Filter Status */}
              {localConfig.globalChartSettings?.defaultDateFilter && localConfig.globalChartSettings?.defaultDateFilter !== 'all' && (
                <div style={{
                  marginTop: '12px',
                  padding: '12px 16px',
                  backgroundColor: theme === 'dark' ? '#065f46' : '#d1fae5',
                  color: theme === 'dark' ? '#10b981' : '#065f46',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '500'
                }}>
                  ✓ Global Date Filter Active: {localConfig.globalChartSettings.defaultDateFilter.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                  {localConfig.globalChartSettings.defaultDateFilter === 'custom' &&
                   localConfig.globalChartSettings.customStartDate &&
                   localConfig.globalChartSettings.customEndDate && (
                    <span style={{ fontSize: '12px', opacity: 0.8, marginLeft: '8px' }}>
                      ({new Date(localConfig.globalChartSettings.customStartDate).toLocaleDateString()} - {new Date(localConfig.globalChartSettings.customEndDate).toLocaleDateString()})
                    </span>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Individual Charts */}
        {activeSection === 'individual' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {filteredCharts.map(chart => (
              <div key={chart.id} style={{
                padding: '16px',
                backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                borderRadius: '8px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                  <h4 style={{
                    margin: 0,
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b',
                    fontSize: '16px',
                    fontWeight: '500'
                  }}>
                    {chart.title}
                  </h4>
                  
                  <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                      <input
                        type="checkbox"
                        checked={chart.visible !== false}
                        onChange={(e) => updateChartConfig(chart.id, { visible: e.target.checked })}
                      />
                      <span style={{ fontSize: '12px', color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>Visible</span>
                    </label>
                    
                    <button
                      onClick={() => resetChart(chart.id)}
                      style={{
                        backgroundColor: '#f59e0b',
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '4px',
                        border: 'none',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      Reset
                    </button>
                  </div>
                </div>

                <div style={{ display: 'grid', gap: '12px', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))' }}>
                  {/* Chart Type */}
                  <div>
                    <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                      Chart Type
                    </label>
                    <select
                      value={chart.type || 'bar'}
                      onChange={(e) => updateChartConfig(chart.id, { type: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '6px 8px',
                        borderRadius: '4px',
                        border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                        fontSize: '12px'
                      }}
                    >
                      <option value="bar">Bar Chart</option>
                      <option value="horizontal-bar">Horizontal Bar</option>
                      <option value="line">Line Chart</option>
                      <option value="area">Area Chart</option>
                      <option value="pie">Pie Chart</option>
                      <option value="doughnut">Doughnut Chart</option>
                    </select>
                  </div>

                  {/* Color Scheme */}
                  <div>
                    <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                      Color Scheme
                    </label>
                    <select
                      value={chart.colorScheme || 'blue'}
                      onChange={(e) => updateChartConfig(chart.id, { colorScheme: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '6px 8px',
                        borderRadius: '4px',
                        border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                        fontSize: '12px'
                      }}
                    >
                      <option value="blue">🔵 Blue</option>
                      <option value="green">🟢 Green</option>
                      <option value="purple">🟣 Purple</option>
                      <option value="orange">🟠 Orange</option>
                      <option value="red">🔴 Red</option>
                      <option value="rainbow">🌈 Rainbow</option>
                    </select>
                  </div>

                  {/* Data Limit */}
                  {(chart.type === 'bar' || chart.type === 'horizontal-bar') && (
                    <div>
                      <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                        Data Limit
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="50"
                        value={chart.limit || 10}
                        onChange={(e) => updateChartConfig(chart.id, { limit: parseInt(e.target.value) })}
                        style={{
                          width: '100%',
                          padding: '6px 8px',
                          borderRadius: '4px',
                          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                          backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                          fontSize: '12px'
                        }}
                      />
                    </div>
                  )}

                  {/* Show Values Toggle */}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <label style={{ display: 'flex', alignItems: 'center', gap: '6px', fontSize: '12px' }}>
                      <input
                        type="checkbox"
                        checked={chart.showValues !== false}
                        onChange={(e) => updateChartConfig(chart.id, { showValues: e.target.checked })}
                      />
                      <span style={{ color: theme === 'dark' ? '#e5e7eb' : '#1e293b' }}>Show Values</span>
                    </label>
                  </div>
                </div>
              </div>
            ))}
            
            {filteredCharts.length === 0 && (
              <div style={{
                textAlign: 'center',
                padding: '40px',
                color: theme === 'dark' ? '#9ca3af' : '#64748b',
                fontSize: '14px'
              }}>
                {searchTerm ? 'No charts match your search.' : 'No charts configured.'}
              </div>
            )}
          </div>
        )}

        {/* Quick Presets */}
        {activeSection === 'presets' && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div style={{ display: 'grid', gap: '12px', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))' }}>
              <button
                onClick={() => applyToAllCharts({ colorScheme: 'blue', showValues: true, visible: true })}
                style={{
                  padding: '16px',
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  textAlign: 'left'
                }}
              >
                <div style={{ fontWeight: '600', marginBottom: '4px' }}>🔵 Professional Blue</div>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>Blue theme with values shown</div>
              </button>
              
              <button
                onClick={() => applyToAllCharts({ colorScheme: 'green', showValues: true, visible: true })}
                style={{
                  padding: '16px',
                  backgroundColor: '#10b981',
                  color: 'white',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  textAlign: 'left'
                }}
              >
                <div style={{ fontWeight: '600', marginBottom: '4px' }}>🟢 Success Green</div>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>Green theme with values shown</div>
              </button>
              
              <button
                onClick={() => applyToAllCharts({ colorScheme: 'rainbow', showValues: false, visible: true })}
                style={{
                  padding: '16px',
                  backgroundColor: '#8b5cf6',
                  color: 'white',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  textAlign: 'left'
                }}
              >
                <div style={{ fontWeight: '600', marginBottom: '4px' }}>🌈 Rainbow Theme</div>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>Colorful charts without values</div>
              </button>
              
              <button
                onClick={() => applyToAllCharts({ visible: false })}
                style={{
                  padding: '16px',
                  backgroundColor: '#6b7280',
                  color: 'white',
                  borderRadius: '8px',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '500',
                  textAlign: 'left'
                }}
              >
                <div style={{ fontWeight: '600', marginBottom: '4px' }}>👁️ Hide All Charts</div>
                <div style={{ fontSize: '12px', opacity: 0.9 }}>Hide all charts from dashboard</div>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedChartConfigurator;
