// MongoDB initialization script for Docker
// This script runs when the MongoDB container starts for the first time

// Switch to the jira-dashboard database
db = db.getSiblingDB('jira-dashboard');

// Create the jira_user with readWrite permissions
db.createUser({
  user: 'jira_user',
  pwd: 'jira_pass_2025',
  roles: [
    {
      role: 'readWrite',
      db: 'jira-dashboard'
    }
  ]
});

// Create the tickets collection and indexes for optimal performance
db.createCollection('tickets');

// Create indexes for fast queries
db.tickets.createIndex({ "key": 1 }, { unique: true, name: "key_unique" });
db.tickets.createIndex({ "status": 1 }, { name: "status_index" });
db.tickets.createIndex({ "priority": 1 }, { name: "priority_index" });
db.tickets.createIndex({ "organization": 1 }, { name: "organization_index" });
db.tickets.createIndex({ "project": 1 }, { name: "project_index" });
db.tickets.createIndex({ "projectKey": 1 }, { name: "projectKey_index" });
db.tickets.createIndex({ "assignee": 1 }, { name: "assignee_index" });
db.tickets.createIndex({ "created": -1 }, { name: "created_desc_index" });
db.tickets.createIndex({ "updated": -1 }, { name: "updated_desc_index" });
db.tickets.createIndex({ "lastSynced": -1 }, { name: "lastSynced_desc_index" });
db.tickets.createIndex({ "isActive": 1 }, { name: "isActive_index" });

// Compound indexes for common queries
db.tickets.createIndex({ "status": 1, "priority": 1 }, { name: "status_priority_index" });
db.tickets.createIndex({ "organization": 1, "status": 1 }, { name: "organization_status_index" });
db.tickets.createIndex({ "project": 1, "status": 1 }, { name: "project_status_index" });
db.tickets.createIndex({ "isActive": 1, "created": -1 }, { name: "active_created_index" });
db.tickets.createIndex({ "isActive": 1, "updated": -1 }, { name: "active_updated_index" });

// Text index for search functionality
db.tickets.createIndex(
  {
    "summary": "text",
    "description": "text",
    "key": "text"
  },
  {
    name: "text_search_index",
    weights: {
      "summary": 10,
      "key": 5,
      "description": 1
    }
  }
);

print("✅ MongoDB initialization completed successfully!");
print("📊 Database: jira-dashboard");
print("👤 User: jira_user created with readWrite permissions");
print("🔍 Indexes created for optimal performance");
print("🚀 Ready for Jira ticket synchronization!");
