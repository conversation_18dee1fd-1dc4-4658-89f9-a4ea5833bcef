import React, { useState, useEffect, useRef, useMemo, useCallback, memo } from 'react';
import OrganizationChart from './OrganizationChart';
import WeeklyTrendsChart from './WeeklyTrendsChart';
import DragDropLayout from './DragDropLayout';
import ConfigurationManager from './ConfigurationManager';
import OptimizedConfigurationManager from './OptimizedConfigurationManager';
import ChartConfigurator from './ChartConfigurator';
import EnhancedChartConfigurator from './EnhancedChartConfigurator';
import DBSYSLogo from './DBSYSLogo';

// Add CSS animations
const styles = `
  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

// Memoized card component for better performance
const DashboardCard = memo(({ card, theme, isMobile }) => (
  <div
    style={{
      backgroundColor: card.color,
      borderRadius: isMobile ? '12px' : '16px',
      padding: isMobile ? '16px' : '24px',
      color: 'white',
      boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
      transition: 'transform 0.2s ease, box-shadow 0.2s ease',
      cursor: 'pointer',
      minHeight: isMobile ? '100px' : '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'space-between'
    }}
    onMouseEnter={(e) => {
      e.target.style.transform = 'translateY(-2px)';
      e.target.style.boxShadow = '0 8px 15px rgba(0, 0, 0, 0.2)';
    }}
    onMouseLeave={(e) => {
      e.target.style.transform = 'translateY(0)';
      e.target.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
    }}
  >
    <div>
      <h3 style={{
        margin: '0 0 8px 0',
        fontSize: isMobile ? '14px' : '16px',
        fontWeight: '600',
        opacity: 0.9
      }}>
        {card.title}
      </h3>
    </div>
    <div style={{ fontSize: isMobile ? '24px' : '32px', fontWeight: 'bold' }}>
      {card.count.toLocaleString()}
    </div>
  </div>
));

const NewModernDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    tickets: [],
    stats: null,
    totalCount: 0,
    loading: true,
    error: null
  });

  const [theme, setTheme] = useState('light');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [lastSyncNotification, setLastSyncNotification] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [assigneeFilter, setAssigneeFilter] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  const [showLatestFirst, setShowLatestFirst] = useState(true);
  const [isFiltering, setIsFiltering] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [activeConfigTab, setActiveConfigTab] = useState('status'); // status, priority, organization, general
  const [performance, setPerformance] = useState(null);
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [showConfig, setShowConfig] = useState(false);
  const [showAdvancedConfig, setShowAdvancedConfig] = useState(false);
  const [showAssigneeGroupManager, setShowAssigneeGroupManager] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [selectedGroupAssignees, setSelectedGroupAssignees] = useState([]);
  const [editingGroup, setEditingGroup] = useState(null);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [autoStartEnabled, setAutoStartEnabled] = useState(false);
  const [showNavbar, setShowNavbar] = useState(false);
  const [showChartsConfig, setShowChartsConfig] = useState(false);
  const [showCardsConfig, setShowCardsConfig] = useState(false);
  const [chartDateFilter, setChartDateFilter] = useState({
    enabled: false,
    startDate: '',
    endDate: '',
    preset: 'last30days' // last7days, last30days, last90days, custom
  });
  const [chartAutoApply, setChartAutoApply] = useState(true);
  const [serviceStatus, setServiceStatus] = useState(null);
  const [isRecoveringService, setIsRecoveringService] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [layoutMode, setLayoutMode] = useState('grid'); // 'grid' or 'drag'
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(50);
  const [sortField, setSortField] = useState('updated');
  const [sortDirection, setSortDirection] = useState('desc');
  const [config, setConfig] = useState({
    // Global chart settings
    globalChartSettings: {
      defaultDateFilter: 'all',
      defaultColorScheme: 'blue',
      autoRefresh: true,
      refreshInterval: 10000,
      customStartDate: '',
      customEndDate: ''
    },
    // Chart configurations
    charts: {
      organizationChart: {
        id: 'organizationChart',
        title: 'RESTAURANTS',
        type: 'horizontal-bar',
        visible: true,
        position: { row: 1, col: 1 },
        size: { width: 1, height: 1 },
        dataSource: 'organizations',
        limit: 10,
        colorScheme: 'blue',
        showValues: true,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      weeklyChart: {
        id: 'weeklyChart',
        title: 'Tickets by Day (Last 7 Days)',
        type: 'line',
        visible: true,
        position: { row: 1, col: 2 },
        size: { width: 1, height: 1 },
        dataSource: 'created',
        period: 7,
        colorScheme: 'green',
        showFill: true,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      }
    },
    // Assignee groups for quick application
    assigneeGroups: [
      {
        id: 'team-leads',
        name: 'Team Leads',
        assignees: [],
        description: 'Team leadership members'
      },
      {
        id: 'qa-team',
        name: 'QA Team',
        assignees: [],
        description: 'Quality assurance team'
      },
      {
        id: 'dev-team',
        name: 'Development Team',
        assignees: [],
        description: 'Development team members'
      }
    ],
    // Global presets
    presets: [
      {
        id: 'team-lead-view',
        name: 'Team Lead Dashboard',
        description: 'Optimized for team leads and managers',
        config: {} // Will be populated with preset configurations
      },
      {
        id: 'ops-live-view',
        name: 'Operations Live View',
        description: 'Real-time operations monitoring',
        config: {}
      },
      {
        id: 'qa-review-view',
        name: 'QA Review Dashboard',
        description: 'Quality assurance focused view',
        config: {}
      }
    ],
    statusCards: [
      {
        id: 'open',
        title: 'Non traités',
        color: '#4f46e5',
        textColor: '#ffffff',
        jiraStatuses: ['Non traités'],
        visible: true,
        icon: '',
        description: 'Tickets not yet started',
        order: 1,
        position: { row: 2, col: 1 },
        size: { width: 1, height: 1 },
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'progress',
        title: 'En cours',
        color: '#0ea5e9',
        textColor: '#ffffff',
        jiraStatuses: ['En cours'],
        visible: true,
        icon: '',
        description: 'Tickets currently being worked on',
        order: 2,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'waiting',
        title: 'En attente client',
        color: '#6366f1',
        textColor: '#ffffff',
        jiraStatuses: ['En attente client'],
        visible: true,
        icon: '',
        description: 'Waiting for customer response',
        order: 3,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'hardware',
        title: 'Hardware',
        color: '#eab308',
        textColor: '#ffffff',
        jiraStatuses: ['Hardware'],
        visible: true,
        icon: '',
        description: 'Hardware related tickets',
        order: 4,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'resolved',
        title: 'Résolu',
        color: '#22c55e',
        textColor: '#ffffff',
        jiraStatuses: ['Résolu'],
        visible: true,
        icon: '',
        description: 'Tickets that have been resolved',
        order: 5,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'europe',
        title: 'Europe',
        color: '#ec4899',
        textColor: '#ffffff',
        jiraStatuses: ['Europe'],
        visible: true,
        icon: '',
        description: 'Europe related tickets',
        order: 6,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      }
    ],
    priorityCards: [
      {
        id: 'urgent',
        title: 'Très Urgent',
        color: '#ef4444',
        textColor: '#ffffff',
        jiraPriorities: ['Très Urgent'],
        visible: true,
        icon: '',
        description: 'Critical priority tickets',
        order: 1,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'high',
        title: 'Urgent',
        color: '#f97316',
        textColor: '#ffffff',
        jiraPriorities: ['Urgent'],
        visible: true,
        icon: '',
        description: 'High priority tickets',
        order: 2,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      },
      {
        id: 'medium',
        title: 'Moyen',
        color: '#d97706',
        textColor: '#ffffff',
        jiraPriorities: ['Moyen'],
        visible: true,
        icon: '',
        description: 'Medium priority tickets',
        order: 3,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      }

    ],
    organizationCards: [
      {
        id: 'org1',
        title: 'Organization A',
        color: '#8b5cf6',
        textColor: '#ffffff',
        organizationNames: [],
        visible: false,
        icon: '🏢',
        description: 'Custom organization filter',
        order: 1,
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: '',
        assigneeFilter: 'all',
        selectedAssignees: []
      }
    ]
  });

  const wsRef = useRef(null);
  const updateIntervalRef = useRef(null);

  // Debounce search term to improve performance
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300); // 300ms delay

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Helper function to get organization value
  const getOrganization = useCallback((ticket) => {
    // Specifically check customfield_10002 for Organizations
    const orgField = ticket.fields?.customfield_10002 || ticket.customfield_10002;

    if (orgField) {
      // Handle different field formats
      if (typeof orgField === 'string' && orgField.trim()) {
        return orgField.trim();
      }
      if (orgField.value && typeof orgField.value === 'string') {
        return orgField.value.trim();
      }
      if (orgField.displayName && typeof orgField.displayName === 'string') {
        return orgField.displayName.trim();
      }
      if (orgField.name && typeof orgField.name === 'string') {
        return orgField.name.trim();
      }
      // Handle array of objects (like your format: [{id: "59", name: "27 - Nador"}])
      if (Array.isArray(orgField) && orgField.length > 0) {
        const first = orgField[0];
        if (first && typeof first === 'object') {
          if (first.name) return first.name; // This should handle "27 - Nador"
          if (first.value) return first.value;
          if (first.displayName) return first.displayName;
          if (first.id) return first.id; // Fallback to ID if name not available
        }
        // If it's just a string in the array
        if (typeof first === 'string') return first;
      }
    }

    // Fallback: check if organization is stored in a different field structure
    if (ticket.organization) {
      return ticket.organization;
    }

    return 'N/A';
  }, []);



  // Helper function to safely get field value
  const getFieldValue = useCallback((ticket, field) => {
    switch (field) {
      case 'key':
        return ticket.key || '';
      case 'summary': {
        const raw = ticket.summary || ticket.fields?.summary;
        if (typeof raw === 'string') return raw;
        if (raw && typeof raw === 'object') {
          if (typeof raw.text === 'string') return raw.text;
          if (Array.isArray(raw.content) && raw.content[0]?.content?.[0]?.text) {
            return raw.content[0].content[0].text;
          }
        }
        return '';
      }
      case 'status':
        return ticket.status || ticket.fields?.status?.name || '';
      case 'priority':
        return ticket.priority || ticket.fields?.priority?.name || '';
      case 'assignee':
        return ticket.assignee || ticket.fields?.assignee?.displayName || '';
      case 'project':
        return ticket.project || ticket.fields?.project?.name || '';
      case 'organization':
        return getOrganization(ticket);
      case 'updated':
        return ticket.updated || ticket.fields?.updated || '';
      case 'created':
        return ticket.created || ticket.fields?.created || '';
      default:
        return '';
    }
  }, [getOrganization]);

  // Helper function to get assignee information from ticket
  const getAssignee = useCallback((ticket) => {
    const assigneeField = ticket.fields?.assignee || ticket.assignee;

    if (!assigneeField) {
      return {
        displayName: 'Unassigned',
        emailAddress: null,
        key: 'unassigned'
      };
    }

    // Handle different assignee field formats
    if (typeof assigneeField === 'string') {
      return {
        displayName: assigneeField,
        emailAddress: assigneeField,
        key: assigneeField.toLowerCase()
      };
    }

    return {
      displayName: assigneeField.displayName || assigneeField.name || 'Unknown',
      emailAddress: assigneeField.emailAddress || assigneeField.email || null,
      key: (assigneeField.key || assigneeField.accountId || assigneeField.displayName || 'unknown').toLowerCase()
    };
  }, []);

  // Helper function to filter tickets by assignee for cards
  const filterTicketsByAssignee = useCallback((tickets, assigneeFilter, selectedAssignees) => {
    if (!assigneeFilter || assigneeFilter === 'all') {
      return tickets;
    }

    return tickets.filter(ticket => {
      const assignee = getAssignee(ticket);

      switch (assigneeFilter) {
        case 'unassigned':
          return assignee.key === 'unassigned';
        case 'specific':
          if (!selectedAssignees || selectedAssignees.length === 0) {
            return true; // Show all if no specific assignees selected
          }
          return selectedAssignees.some(selectedAssignee =>
            assignee.key === selectedAssignee.toLowerCase() ||
            assignee.displayName.toLowerCase().includes(selectedAssignee.toLowerCase()) ||
            (assignee.emailAddress && assignee.emailAddress.toLowerCase().includes(selectedAssignee.toLowerCase()))
          );
        default:
          return true;
      }
    });
  }, [getAssignee]);

  // Helper function to filter tickets by date range for cards
  const filterTicketsByDate = useCallback((tickets, dateFilter, customDateFrom, customDateTo) => {
    if (!dateFilter || dateFilter === 'all') {
      return tickets;
    }

    return tickets.filter(ticket => {
      const ticketDate = new Date(getFieldValue(ticket, 'updated'));

      switch (dateFilter) {
        case 'today':
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return ticketDate >= today;
        case 'yesterday':
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          yesterday.setHours(0, 0, 0, 0);
          const yesterdayEnd = new Date(yesterday);
          yesterdayEnd.setHours(23, 59, 59, 999);
          return ticketDate >= yesterday && ticketDate <= yesterdayEnd;
        case 'last7days':
          const last7Days = new Date();
          last7Days.setDate(last7Days.getDate() - 7);
          return ticketDate >= last7Days;
        case 'last30days':
          const last30Days = new Date();
          last30Days.setDate(last30Days.getDate() - 30);
          return ticketDate >= last30Days;
        case 'last90days':
          const last90Days = new Date();
          last90Days.setDate(last90Days.getDate() - 90);
          return ticketDate >= last90Days;
        case 'thisweek':
          const startOfWeek = new Date();
          const day = startOfWeek.getDay();
          const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
          startOfWeek.setDate(diff);
          startOfWeek.setHours(0, 0, 0, 0);
          return ticketDate >= startOfWeek;
        case 'thismonth':
          const startOfMonth = new Date();
          startOfMonth.setDate(1);
          startOfMonth.setHours(0, 0, 0, 0);
          return ticketDate >= startOfMonth;
        case 'thisyear':
          const startOfYear = new Date();
          startOfYear.setMonth(0, 1);
          startOfYear.setHours(0, 0, 0, 0);
          return ticketDate >= startOfYear;
        case 'lastyear':
          const startOfLastYear = new Date();
          startOfLastYear.setFullYear(startOfLastYear.getFullYear() - 1, 0, 1);
          startOfLastYear.setHours(0, 0, 0, 0);
          const endOfLastYear = new Date();
          endOfLastYear.setFullYear(endOfLastYear.getFullYear() - 1, 11, 31);
          endOfLastYear.setHours(23, 59, 59, 999);
          return ticketDate >= startOfLastYear && ticketDate <= endOfLastYear;
        case 'custom':
          if (customDateFrom || customDateTo) {
            const fromDate = customDateFrom ? new Date(customDateFrom) : new Date('1900-01-01');
            const toDate = customDateTo ? new Date(customDateTo) : new Date('2100-12-31');
            toDate.setHours(23, 59, 59, 999);
            return ticketDate >= fromDate && ticketDate <= toDate;
          }
          return true;
        default:
          return true;
      }
    });
  }, [getFieldValue]);

  // Get all unique assignees from tickets for dropdown
  const getAllAssignees = useCallback(() => {
    const assigneeMap = new Map();

    (dashboardData.tickets || []).forEach(ticket => {
      const assignee = getAssignee(ticket);
      if (assignee.key !== 'unassigned') {
        assigneeMap.set(assignee.key, {
          displayName: assignee.displayName,
          emailAddress: assignee.emailAddress,
          key: assignee.key
        });
      }
    });

    return Array.from(assigneeMap.values()).sort((a, b) =>
      a.displayName.localeCompare(b.displayName)
    );
  }, [dashboardData.tickets, getAssignee]);

  // Filter tickets based on search term with improved performance
  useEffect(() => {
    if (!dashboardData.tickets) return;

    setIsFiltering(true);

    // Use setTimeout to prevent blocking the UI thread
    const filterTimeout = setTimeout(() => {
      let filtered = dashboardData.tickets;

    // Apply search filter
    if (debouncedSearchTerm) {
      const searchLower = debouncedSearchTerm.toLowerCase();
      filtered = filtered.filter(ticket => {
        const searchableFields = [
          getFieldValue(ticket, 'key'),
          getFieldValue(ticket, 'summary'),
          getFieldValue(ticket, 'status'),
          getFieldValue(ticket, 'priority'),
          getFieldValue(ticket, 'assignee'),
          getFieldValue(ticket, 'project'),
          getFieldValue(ticket, 'organization')
        ];

        return searchableFields.some(field =>
          field.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply status filter
    if (statusFilter) {
      filtered = filtered.filter(ticket =>
        getFieldValue(ticket, 'status').toLowerCase() === statusFilter.toLowerCase()
      );
    }

    // Apply assignee filter
    if (assigneeFilter) {
      filtered = filtered.filter(ticket => {
        const assignee = getAssignee(ticket);

        if (assigneeFilter === 'unassigned') {
          return assignee.key === 'unassigned';
        } else {
          return assignee.displayName.toLowerCase() === assigneeFilter.toLowerCase() ||
                 (assignee.emailAddress && assignee.emailAddress.toLowerCase() === assigneeFilter.toLowerCase());
        }
      });
    }

    // Apply priority filter
    if (priorityFilter) {
      filtered = filtered.filter(ticket =>
        getFieldValue(ticket, 'priority').toLowerCase() === priorityFilter.toLowerCase()
      );
    }

    // Apply date filter
    if (dateFilter || (customDateFrom || customDateTo)) {
      filtered = filtered.filter(ticket => {
        const ticketDate = new Date(getFieldValue(ticket, 'updated'));
        const now = new Date();

        if (dateFilter) {
          switch (dateFilter) {
            case 'today':
              const today = new Date();
              today.setHours(0, 0, 0, 0);
              return ticketDate >= today;
            case 'yesterday':
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              yesterday.setHours(0, 0, 0, 0);
              const yesterdayEnd = new Date(yesterday);
              yesterdayEnd.setHours(23, 59, 59, 999);
              return ticketDate >= yesterday && ticketDate <= yesterdayEnd;
            case 'last7days':
              const last7Days = new Date();
              last7Days.setDate(last7Days.getDate() - 7);
              return ticketDate >= last7Days;
            case 'last30days':
              const last30Days = new Date();
              last30Days.setDate(last30Days.getDate() - 30);
              return ticketDate >= last30Days;
            case 'last90days':
              const last90Days = new Date();
              last90Days.setDate(last90Days.getDate() - 90);
              return ticketDate >= last90Days;
            case 'thisweek':
              const startOfWeek = new Date();
              const day = startOfWeek.getDay();
              const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // adjust when day is sunday
              startOfWeek.setDate(diff);
              startOfWeek.setHours(0, 0, 0, 0);
              return ticketDate >= startOfWeek;
            case 'thismonth':
              const startOfMonth = new Date();
              startOfMonth.setDate(1);
              startOfMonth.setHours(0, 0, 0, 0);
              return ticketDate >= startOfMonth;
            case 'thisyear':
              const startOfYear = new Date();
              startOfYear.setMonth(0, 1);
              startOfYear.setHours(0, 0, 0, 0);
              return ticketDate >= startOfYear;
            case 'lastyear':
              const startOfLastYear = new Date();
              startOfLastYear.setFullYear(startOfLastYear.getFullYear() - 1, 0, 1);
              startOfLastYear.setHours(0, 0, 0, 0);
              const endOfLastYear = new Date();
              endOfLastYear.setFullYear(endOfLastYear.getFullYear() - 1, 11, 31);
              endOfLastYear.setHours(23, 59, 59, 999);
              return ticketDate >= startOfLastYear && ticketDate <= endOfLastYear;
            default:
              return true;
          }
        }

        // Custom date range
        if (customDateFrom || customDateTo) {
          const fromDate = customDateFrom ? new Date(customDateFrom) : new Date('1900-01-01');
          const toDate = customDateTo ? new Date(customDateTo) : new Date('2100-12-31');
          toDate.setHours(23, 59, 59, 999); // Include the entire end date
          return ticketDate >= fromDate && ticketDate <= toDate;
        }

        return true;
      });
    }

    // Sort tickets with improved field access
    filtered.sort((a, b) => {
      // If "show latest first" is enabled and we're sorting by updated date, prioritize recent tickets
      if (showLatestFirst && sortField === 'updated') {
        const aDate = new Date(getFieldValue(a, 'updated') || 0);
        const bDate = new Date(getFieldValue(b, 'updated') || 0);
        return bDate - aDate; // Newest first
      }

      let aValue = getFieldValue(a, sortField);
      let bValue = getFieldValue(b, sortField);

      // Handle date fields
      if (sortField === 'updated' || sortField === 'created') {
        aValue = new Date(aValue || 0);
        bValue = new Date(bValue || 0);
      }

      // Handle string comparison
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });

      setFilteredTickets(filtered);
      setCurrentPage(1);
      setIsFiltering(false);
    }, 0);

    return () => clearTimeout(filterTimeout);
  }, [dashboardData.tickets, debouncedSearchTerm, statusFilter, assigneeFilter, priorityFilter, dateFilter, customDateFrom, customDateTo, sortField, sortDirection, showLatestFirst, getFieldValue]);

  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  const initializeDashboard = async () => {
    try {
      setDashboardData(prev => ({ ...prev, loading: true }));
      await loadDashboardData();
      setupAutoRefresh();
    } catch (error) {
      console.error('Error initializing dashboard:', error);
    }
  };

  const loadDashboardData = async () => {
    try {
      console.log('Loading dashboard data...');
      const startTime = Date.now();

      // Try to fetch from API, but fallback to mock data if it fails
      try {
        const response = await fetch('/api/mongodb/dashboard-tickets?stats=true');
        const data = await response.json();

        if (data.success) {
          const newTicketCount = data.count || 0;
          const previousCount = dashboardData.totalCount;

          // Sort tickets by updated date (newest first) to ensure stability
          const sortedTickets = (data.tickets || []).sort((a, b) => {
            const dateA = new Date(a.updated || a.fields?.updated || 0);
            const dateB = new Date(b.updated || b.fields?.updated || 0);
            return dateB - dateA; // Newest first
          });

          setDashboardData({
            tickets: sortedTickets,
            stats: data.stats,
            totalCount: newTicketCount,
            loading: false,
            error: null
          });
          setPerformance(data.performance);
          setLastUpdate(new Date());
          setConnectionStatus('connected');
          console.log('Dashboard data loaded:', data.count, 'tickets');

          // Show notification if new tickets were added
          if (previousCount > 0 && newTicketCount > previousCount) {
            const newTickets = newTicketCount - previousCount;
            setLastSyncNotification(`🔄 ${newTickets} new ticket${newTickets > 1 ? 's' : ''} synced!`);
            setTimeout(() => setLastSyncNotification(null), 5000); // Hide after 5 seconds
          }
          return; // Success, exit early
        }
      } catch (apiError) {
        console.log('API not available, using mock data');
      }

      // Mock data fallback
      const mockStats = {
        statusCounts: {
          'Non traités': 9,
          'En cours': 8,
          'En attente client': 199,
          'Hardware': 86,
          'Résolu': 10731,
          'Europe': 63
        },
        priorityCounts: {
          'Très Urgent': 14,
          'Urgent': 3145,
          'Moyen': 4341
        },
        assigneeCounts: {
          'John Doe': 150,
          'Jane Smith': 200,
          'Bob Johnson': 175,
          'Alice Brown': 125,
          'Charlie Wilson': 100
        },
        organizationCounts: {
          'Marrakech drive': 2800,
          'Hay riad': 2600,
          'Marrakech gueliz': 2400,
          'Tanger plage': 2200,
          'Agadir Marjane': 2000,
          'Tanger marjane': 1800,
          'Skhirat': 1600,
          'Arnolab': 1400,
          'Hermitage': 1200,
          'Tetouan': 1000
        },
        dailyTickets: [
          { date: '2025-08-04', created: 65 },
          { date: '2025-08-05', created: 78 },
          { date: '2025-08-06', created: 52 },
          { date: '2025-08-07', created: 48 },
          { date: '2025-08-08', created: 45 },
          { date: '2025-08-09', created: 52 },
          { date: '2025-08-10', created: 15 }
        ]
      };

      setDashboardData({
        tickets: [],
        stats: mockStats,
        totalCount: 78171,
        loading: false,
        error: null
      });
      setPerformance({ queryTime: '12ms', totalRecords: 78171 });
      setLastUpdate(new Date());
      setConnectionStatus('connected');
      console.log('Using mock data - 78,171 tickets');

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setDashboardData(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
      setConnectionStatus('error');
    }
  };

  const setupWebSocketConnection = () => {
    // Disable WebSocket connection completely when using mock data
    console.log('WebSocket connection disabled - using mock data mode');
    return;
  };

  const setupAutoRefresh = () => {
    const interval = 5000; // 5 seconds for faster dashboard updates
    updateIntervalRef.current = setInterval(loadDashboardData, interval);
  };

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  // Full Screen Functions
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().then(() => {
        setIsFullScreen(true);
      }).catch(err => {
        console.error('Error attempting to enable fullscreen:', err);
      });
    } else {
      document.exitFullscreen().then(() => {
        setIsFullScreen(false);
      }).catch(err => {
        console.error('Error attempting to exit fullscreen:', err);
      });
    }
  };

  // Auto-start functionality
  const toggleAutoStart = async () => {
    try {
      const response = await fetch('/api/system/auto-start', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          enabled: !autoStartEnabled,
          url: window.location.origin,
          fullScreen: true
        })
      });

      if (response.ok) {
        const data = await response.json();
        setAutoStartEnabled(!autoStartEnabled);
        alert(data.message);
      } else {
        throw new Error('Failed to toggle auto-start');
      }
    } catch (error) {
      console.error('Error toggling auto-start:', error);
      alert('Error configuring auto-start. This feature requires server support.');
    }
  };

  // Check auto-start status on load
  useEffect(() => {
    const checkAutoStartStatus = async () => {
      try {
        const response = await fetch('/api/system/auto-start-status');
        if (response.ok) {
          const data = await response.json();
          setAutoStartEnabled(data.enabled);
        }
      } catch (error) {
        console.log('Auto-start status check failed:', error);
      }
    };

    checkAutoStartStatus();
  }, []);

  // Auto full screen on load if URL parameter is present
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('autostart') === 'true' || urlParams.get('fullscreen') === 'true') {
      setTimeout(() => {
        toggleFullScreen();
      }, 2000); // Wait 2 seconds for page to load
    }
  }, []);

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullScreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Assignee Group Management Functions
  const createAssigneeGroup = () => {
    if (!newGroupName.trim()) return;

    const newGroup = {
      id: `group-${Date.now()}`,
      name: newGroupName.trim(),
      assignees: [...selectedGroupAssignees],
      description: newGroupDescription.trim()
    };

    setConfig(prev => ({
      ...prev,
      assigneeGroups: [...(prev.assigneeGroups || []), newGroup]
    }));

    // Reset form
    setNewGroupName('');
    setNewGroupDescription('');
    setSelectedGroupAssignees([]);
  };

  const updateAssigneeGroup = (groupId, updates) => {
    setConfig(prev => ({
      ...prev,
      assigneeGroups: prev.assigneeGroups.map(group =>
        group.id === groupId ? { ...group, ...updates } : group
      )
    }));
  };

  const deleteAssigneeGroup = (groupId) => {
    if (confirm('Are you sure you want to delete this assignee group?')) {
      setConfig(prev => ({
        ...prev,
        assigneeGroups: prev.assigneeGroups.filter(group => group.id !== groupId)
      }));
    }
  };

  const applyAssigneeGroupToAll = (groupId) => {
    const group = config.assigneeGroups?.find(g => g.id === groupId);
    if (!group) return;

    setConfig(prev => {
      const updatedConfig = { ...prev };

      // Apply to status cards
      if (updatedConfig.statusCards) {
        updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
          ...card,
          assigneeFilter: 'specific',
          selectedAssignees: [...group.assignees]
        }));
      }

      // Apply to priority cards
      if (updatedConfig.priorityCards) {
        updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
          ...card,
          assigneeFilter: 'specific',
          selectedAssignees: [...group.assignees]
        }));
      }

      // Apply to organization cards
      if (updatedConfig.organizationCards) {
        updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
          ...card,
          assigneeFilter: 'specific',
          selectedAssignees: [...group.assignees]
        }));
      }

      // Apply to charts
      if (updatedConfig.charts) {
        Object.keys(updatedConfig.charts).forEach(chartId => {
          updatedConfig.charts[chartId] = {
            ...updatedConfig.charts[chartId],
            assigneeFilter: 'specific',
            selectedAssignees: [...group.assignees]
          };
        });
      }

      return updatedConfig;
    });

    alert(`Applied "${group.name}" assignee group to all cards and charts!`);
  };

  const clearAllAssigneeFilters = () => {
    if (confirm('Are you sure you want to clear all assignee filters from cards and charts?')) {
      setConfig(prev => {
        const updatedConfig = { ...prev };

        // Clear status cards
        if (updatedConfig.statusCards) {
          updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
            ...card,
            assigneeFilter: 'all',
            selectedAssignees: []
          }));
        }

        // Clear priority cards
        if (updatedConfig.priorityCards) {
          updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
            ...card,
            assigneeFilter: 'all',
            selectedAssignees: []
          }));
        }

        // Clear organization cards
        if (updatedConfig.organizationCards) {
          updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
            ...card,
            assigneeFilter: 'all',
            selectedAssignees: []
          }));
        }

        // Clear charts
        if (updatedConfig.charts) {
          Object.keys(updatedConfig.charts).forEach(chartId => {
            updatedConfig.charts[chartId] = {
              ...updatedConfig.charts[chartId],
              assigneeFilter: 'all',
              selectedAssignees: []
            };
          });
        }

        return updatedConfig;
      });

      alert('Cleared all assignee filters!');
    }
  };

  const getStatusCards = () => {
    return config.statusCards
      .filter(card => card.visible)
      .sort((a, b) => a.order - b.order)
      .map(card => {
        // If we have tickets data, use the filtering logic
        if (dashboardData.tickets && dashboardData.tickets.length > 0) {
          // Filter tickets by date for this card
          let filteredTickets = filterTicketsByDate(
            dashboardData.tickets || [],
            card.dateFilter,
            card.customDateFrom,
            card.customDateTo
          );

          // Filter tickets by assignee for this card
          filteredTickets = filterTicketsByAssignee(
            filteredTickets,
            card.assigneeFilter,
            card.selectedAssignees
          );

          // Count tickets matching this card's status criteria
          const count = filteredTickets.filter(ticket => {
            const ticketStatus = getFieldValue(ticket, 'status');
            return card.jiraStatuses.some(status =>
              ticketStatus.toLowerCase() === status.toLowerCase()
            );
          }).length;

          return {
            ...card,
            count
          };
        } else {
          // Use stats data for mock mode
          const statusCounts = dashboardData.stats?.statusCounts || {};
          let count = 0;

          // Sum up counts for all statuses that match this card
          card.jiraStatuses.forEach(status => {
            count += statusCounts[status] || 0;
          });

          return {
            ...card,
            count
          };
        }
      });
  };

  const getPriorityCards = () => {
    return config.priorityCards
      .filter(card => card.visible)
      .sort((a, b) => a.order - b.order)
      .map(card => {
        // If we have tickets data, use the filtering logic
        if (dashboardData.tickets && dashboardData.tickets.length > 0) {
          // Filter tickets by date for this card
          let filteredTickets = filterTicketsByDate(
            dashboardData.tickets || [],
            card.dateFilter,
            card.customDateFrom,
            card.customDateTo
          );

          // Filter tickets by assignee for this card
          filteredTickets = filterTicketsByAssignee(
            filteredTickets,
            card.assigneeFilter,
            card.selectedAssignees
          );

          // Count tickets matching this card's priority criteria
          const count = filteredTickets.filter(ticket => {
            const ticketPriority = getFieldValue(ticket, 'priority');
            return card.jiraPriorities.some(priority =>
              ticketPriority.toLowerCase() === priority.toLowerCase()
            );
          }).length;

          return {
            ...card,
            count
          };
        } else {
          // Use stats data for mock mode
          const priorityCounts = dashboardData.stats?.priorityCounts || {};
          let count = 0;

          // Sum up counts for all priorities that match this card
          card.jiraPriorities.forEach(priority => {
            count += priorityCounts[priority] || 0;
          });

          return {
            ...card,
            count
          };
        }
      });
  };

  const getOrganizationCards = () => {
    return config.organizationCards
      .filter(card => card.visible)
      .sort((a, b) => a.order - b.order)
      .map(card => {
        // Filter tickets by date for this card
        let filteredTickets = filterTicketsByDate(
          dashboardData.tickets || [],
          card.dateFilter,
          card.customDateFrom,
          card.customDateTo
        );

        // Filter tickets by assignee for this card
        filteredTickets = filterTicketsByAssignee(
          filteredTickets,
          card.assigneeFilter,
          card.selectedAssignees
        );

        // Count tickets matching this card's organization criteria
        const count = filteredTickets.filter(ticket => {
          const ticketOrg = getOrganization(ticket);
          return card.organizationNames.some(org =>
            ticketOrg.toLowerCase() === org.toLowerCase()
          );
        }).length;

        return {
          ...card,
          count
        };
      });
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#10b981';
      case 'disconnected': return '#f59e0b';
      case 'error':
      case 'failed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return '🟢';
      case 'disconnected': return '🟡';
      case 'error':
      case 'failed': return '🔴';
      default: return '⚪';
    }
  };

  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const getPaginatedTickets = () => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredTickets.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    return Math.ceil(filteredTickets.length / itemsPerPage);
  };



  const saveConfig = () => {
    localStorage.setItem('jiraDashboardConfig', JSON.stringify(config));
    setShowConfig(false);
    alert('Configuration saved successfully!');
  };


  // Helper function to get filtered tickets for charts based on global settings
  const getFilteredTicketsForCharts = useCallback(() => {
    let tickets = dashboardData.tickets || [];
    const dateFilter = config.globalChartSettings?.defaultDateFilter;

    if (!dateFilter || dateFilter === 'all') {
      return tickets;
    }

    const now = new Date();
    let startDate, endDate;

    switch (dateFilter) {
      case 'last7days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'last30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'last90days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'thisyear':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = now;
        break;
      case 'lastyear':
        startDate = new Date(now.getFullYear() - 1, 0, 1);
        endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59);
        break;
      case 'custom':
        if (config.globalChartSettings?.customStartDate && config.globalChartSettings?.customEndDate) {
          startDate = new Date(config.globalChartSettings.customStartDate);
          endDate = new Date(config.globalChartSettings.customEndDate);
        } else {
          return tickets;
        }
        break;
      default:
        return tickets;
    }

    return tickets.filter(ticket => {
      const ticketDate = new Date(ticket.created || ticket.updated);
      return ticketDate >= startDate && ticketDate <= endDate;
    });
  }, [dashboardData.tickets, config.globalChartSettings]);

  // Derived analytics: Top 10 organizations and weekly ticket counts (with global date filtering)
  const topOrganizations = useMemo(() => {
    const counts = new Map();
    const filteredTickets = getFilteredTicketsForCharts();

    filteredTickets.forEach(t => {
      const org = (getOrganization(t) || 'N/A').trim();
      if (!org) return;
      counts.set(org, (counts.get(org) || 0) + 1);
    });
    return Array.from(counts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, config.charts?.organizationChart?.limit || 10)
      .map(([name, count]) => ({ name, count }));
  }, [getFilteredTicketsForCharts, getOrganization, config.charts?.organizationChart?.limit]);

  const weeklyTickets = useMemo(() => {
    const labels = [];
    const created = Array(7).fill(0);
    const today = new Date();

    for (let i = 6; i >= 0; i--) {
      const d = new Date(today);
      d.setDate(today.getDate() - i);
      labels.push(d.toLocaleDateString(undefined, { day: '2-digit', month: 'short', year: 'numeric' }));
    }

    const isoKeys = [];
    for (let i = 6; i >= 0; i--) {
      const d = new Date(today);
      d.setDate(today.getDate() - i);
      isoKeys.push(d.toISOString().slice(0, 10));
    }
    const indexByIso = Object.fromEntries(isoKeys.map((k, i) => [k, i]));

    // Use filtered tickets for charts
    const filteredTickets = getFilteredTicketsForCharts();

    filteredTickets.forEach(t => {
      const c = new Date(t.created || t.fields?.created || 0);
      const cKey = isNaN(c) ? null : c.toISOString().slice(0, 10);
      if (cKey && indexByIso[cKey] !== undefined) created[indexByIso[cKey]] += 1;
    });

    return { labels, created };
  }, [getFilteredTicketsForCharts]);

  const loadConfig = async () => {
    // First try to load from server
    try {
      const response = await fetch('/api/config/dashboard');
      if (response.ok) {
        const serverConfig = await response.json();
        if (serverConfig.success && serverConfig.config) {
          console.log('✅ Configuration loaded from server');
          setConfig(serverConfig.config);
          // Also save to localStorage as backup
          localStorage.setItem('jiraDashboardConfig', JSON.stringify(serverConfig.config));
          return;
        }
      }
    } catch (error) {
      console.log('Server config not available, trying localStorage:', error.message);
    }

    // Fallback to localStorage
    const savedConfig = localStorage.getItem('jiraDashboardConfig');
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig);
        console.log('✅ Configuration loaded from localStorage');
        setConfig(parsedConfig);
      } catch (error) {
        console.error('Error loading saved config:', error);
        // Keep default config if saved config is corrupted
      }
    } else {
      console.log('ℹ️ Using default configuration - will be saved automatically');
      // Force save the default configuration
      setTimeout(() => {
        localStorage.setItem('jiraDashboardConfig', JSON.stringify(config));
        console.log('✅ Default configuration saved to localStorage');
      }, 1000);
    }
  };

  const updateCardConfig = (type, cardId, updates) => {
    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: prev[`${type}Cards`].map(card =>
        card.id === cardId ? { ...card, ...updates } : card
      )
    }));
  };

  const addNewCard = (type) => {
    const newCard = {
      id: `${type}_${Date.now()}`,
      title: `New ${type} Card`,
      color: '#6b7280',
      textColor: '#ffffff',
      visible: true,
      icon: '📊',
      description: `Custom ${type} card`,
      order: config[`${type}Cards`].length + 1,
      ...(type === 'status' && { jiraStatuses: [] }),
      ...(type === 'priority' && { jiraPriorities: [] }),
      ...(type === 'organization' && { organizationNames: [] })
    };

    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: [...prev[`${type}Cards`], newCard]
    }));
  };
  // Save config to localStorage and server whenever it changes
  useEffect(() => {
    localStorage.setItem('jiraDashboardConfig', JSON.stringify(config));

    // Also save to server (debounced)
    const saveToServer = setTimeout(async () => {
      try {
        // Save current configuration
        await fetch('/api/config/dashboard', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ config })
        });
        console.log('✅ Configuration saved to server');

        // Also save as default configuration for future use
        await fetch('/api/config/dashboard/default', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            config: {
              ...config,
              charts: {
                ...config.charts,
                organizationChart: {
                  ...config.charts.organizationChart,
                  title: 'RESTAURANTS'
                }
              }
            }
          })
        });
        console.log('✅ Default configuration updated');
      } catch (error) {
        console.error('❌ Failed to save configuration to server:', error);
      }
    }, 300); // Reduced debounce for better responsiveness

    return () => clearTimeout(saveToServer);
  }, [config]);

  // Handle layout changes from drag and drop
  const handleLayoutChange = useCallback((itemId, newPosition) => {
    setConfig(prevConfig => {
      const updatedConfig = { ...prevConfig };

      // Update position for status cards
      if (updatedConfig.statusCards) {
        updatedConfig.statusCards = updatedConfig.statusCards.map(card =>
          card.id === itemId ? { ...card, position: newPosition } : card
        );
      }

      // Update position for priority cards
      if (updatedConfig.priorityCards) {
        updatedConfig.priorityCards = updatedConfig.priorityCards.map(card =>
          card.id === itemId ? { ...card, position: newPosition } : card
        );
      }

      // Update position for organization cards
      if (updatedConfig.organizationCards) {
        updatedConfig.organizationCards = updatedConfig.organizationCards.map(card =>
          card.id === itemId ? { ...card, position: newPosition } : card
        );
      }

      // Update position for charts
      if (updatedConfig.charts) {
        Object.keys(updatedConfig.charts).forEach(chartId => {
          if (chartId === itemId) {
            updatedConfig.charts[chartId] = {
              ...updatedConfig.charts[chartId],
              position: newPosition
            };
          }
        });
      }

      return updatedConfig;
    });
  }, []);

  // Handle chart configuration updates
  const handleChartUpdate = useCallback((chartId, updatedChart) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      charts: {
        ...prevConfig.charts,
        [chartId]: updatedChart
      }
    }));
  }, []);

  // Apply date filter to chart data with auto-apply
  const applyDateFilterToCharts = useCallback(() => {
    // Use global chart settings if available
    const dateFilter = config.globalChartSettings?.defaultDateFilter || chartDateFilter.preset || 'all';

    if (!chartDateFilter.enabled && dateFilter === 'all') {
      // Clear filter if disabled
      setDashboardData(prev => ({
        ...prev,
        filteredTickets: null,
        dateFilterApplied: false,
        dateFilterRange: null
      }));
      return;
    }

    let startDate, endDate;
    const now = new Date();

    switch (dateFilter) {
      case 'last7days':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'last30days':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'last90days':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        endDate = now;
        break;
      case 'thisyear':
        startDate = new Date(now.getFullYear(), 0, 1); // January 1st of current year
        endDate = now;
        break;
      case 'lastyear':
        startDate = new Date(now.getFullYear() - 1, 0, 1); // January 1st of last year
        endDate = new Date(now.getFullYear() - 1, 11, 31, 23, 59, 59); // December 31st of last year
        break;
      case 'custom':
        const customStart = config.globalChartSettings?.customStartDate || chartDateFilter.startDate;
        const customEnd = config.globalChartSettings?.customEndDate || chartDateFilter.endDate;

        if (customStart && customEnd) {
          startDate = new Date(customStart);
          endDate = new Date(customEnd);
        } else {
          return; // Invalid custom range
        }
        break;
      case 'all':
      default:
        // No filtering
        setDashboardData(prev => ({
          ...prev,
          filteredTickets: null,
          dateFilterApplied: false,
          dateFilterRange: null
        }));
        return;
    }

    // Filter tickets by date range
    const filteredTickets = dashboardData.tickets?.filter(ticket => {
      const ticketDate = new Date(ticket.created || ticket.updated);
      return ticketDate >= startDate && ticketDate <= endDate;
    }) || [];

    // Update dashboard data with filtered tickets
    setDashboardData(prev => ({
      ...prev,
      filteredTickets,
      dateFilterApplied: true,
      dateFilterRange: { startDate, endDate, preset: chartDateFilter.preset }
    }));

    console.log(`📊 Date filter applied: ${filteredTickets.length} tickets from ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`);
  }, [chartDateFilter, dashboardData.tickets]);

  // Auto-apply date filter when settings change
  useEffect(() => {
    if (chartAutoApply) {
      applyDateFilterToCharts();
    }
  }, [chartDateFilter, chartAutoApply, applyDateFilterToCharts]);

  // Clear date filter
  const clearDateFilter = useCallback(() => {
    setChartDateFilter(prev => ({ ...prev, enabled: false }));
  }, []);

  // Check service status
  const checkServiceStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/sync/status');
      if (response.ok) {
        const status = await response.json();
        setServiceStatus(status);
        return status;
      }
    } catch (error) {
      console.error('❌ Failed to check service status:', error);
    }
    return null;
  }, []);

  // Fetch dashboard data
  const fetchDashboardData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/dashboard/tickets');
      if (response.ok) {
        const data = await response.json();
        setDashboardData(data);
        setLastUpdate(new Date());
      } else {
        console.error('Failed to fetch dashboard data');
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Force service recovery
  const forceServiceRecovery = useCallback(async () => {
    setIsRecoveringService(true);
    try {
      const response = await fetch('/api/sync/force-recovery', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log('✅ Service recovery successful');
          setServiceStatus(result.status);
          // Trigger a data refresh
          await fetchDashboardData();
          return true;
        } else {
          console.error('❌ Service recovery failed:', result.message);
          return false;
        }
      } else {
        console.error('❌ Service recovery request failed');
        return false;
      }
    } catch (error) {
      console.error('❌ Service recovery error:', error);
      return false;
    } finally {
      setIsRecoveringService(false);
    }
  }, [fetchDashboardData]);



  // Check service status periodically
  useEffect(() => {
    checkServiceStatus();
    const statusInterval = setInterval(checkServiceStatus, 30000); // Check every 30 seconds
    return () => clearInterval(statusInterval);
  }, [checkServiceStatus]);

  // Initial data fetch
  useEffect(() => {
    fetchDashboardData();
  }, [fetchDashboardData]);

  const deleteCard = (type, cardId) => {
    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: prev[`${type}Cards`].filter(card => card.id !== cardId)
    }));
  };

  const moveCard = (type, cardId, direction) => {
    setConfig(prev => {
      const cards = [...prev[`${type}Cards`]];
      const cardIndex = cards.findIndex(card => card.id === cardId);

      if (cardIndex === -1) return prev;

      const newIndex = direction === 'up' ? cardIndex - 1 : cardIndex + 1;

      if (newIndex < 0 || newIndex >= cards.length) return prev;

      // Swap cards
      [cards[cardIndex], cards[newIndex]] = [cards[newIndex], cards[cardIndex]];

      // Update order
      cards.forEach((card, index) => {
        card.order = index + 1;
      });

      return {
        ...prev,
        [`${type}Cards`]: cards
      };
    });
  };

  const updateJiraMapping = (type, cardId, mappingType, values) => {
    const mappingKey = type === 'status' ? 'jiraStatuses' :
                      type === 'priority' ? 'jiraPriorities' : 'organizationNames';

    updateCardConfig(type, cardId, { [mappingKey]: values });
  };

  const getAvailableJiraValues = (type) => {
    if (type === 'status') {
      return Object.keys(dashboardData.stats?.statusCounts || {});
    } else if (type === 'priority') {
      return Object.keys(dashboardData.stats?.priorityCounts || {});
    } else if (type === 'organization') {
      return Object.keys(dashboardData.stats?.organizationCounts || {});
    }
    return [];
  };

  // Initialize dashboard
  useEffect(() => {
    const initializeApp = async () => {
      // Load saved theme
      const savedTheme = localStorage.getItem('dashboard-theme');
      if (savedTheme) {
        setTheme(savedTheme);
      }

      await loadConfig(); // Load saved configuration first (now async)
      initializeDashboard();
      setupWebSocketConnection();
      handleResize();
    };

    initializeApp();

    window.addEventListener('resize', handleResize);

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Theme management
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
    localStorage.setItem('dashboard-theme', theme);
  }, [theme]);

  if (dashboardData.loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: theme === 'dark' ? '#111827' : '#f8fafc',
        color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div className="animate-spin" style={{
            width: '50px',
            height: '50px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            margin: '0 auto 20px'
          }}></div>
          <p style={{ fontSize: '18px' }}>Loading Modern Jira Dashboard...</p>
        </div>
      </div>
    );
  }

  if (dashboardData.error) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: theme === 'dark' ? '#111827' : '#f8fafc'
      }}>
        <div style={{
          textAlign: 'center',
          padding: '40px',
          backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
          borderRadius: '12px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
          maxWidth: '500px',
          color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
        }}>
          <h2 style={{ color: '#ef4444', marginBottom: '16px' }}>Error Loading Dashboard</h2>
          <p style={{ marginBottom: '24px', opacity: 0.8 }}>{dashboardData.error}</p>
          <button
            onClick={loadDashboardData}
            className="btn btn-primary"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      minHeight: '100vh',
      backgroundColor: theme === 'dark' ? '#111827' : '#f8fafc',
      color: theme === 'dark' ? '#e5e7eb' : '#1e293b',
      fontFamily: 'system-ui, -apple-system, sans-serif',
      transition: 'all 0.3s ease'
    }}>
      {/* Sync Notification */}
      {lastSyncNotification && (
        <div style={{
          position: 'fixed',
          top: '20px',
          right: '20px',
          backgroundColor: '#10b981',
          color: 'white',
          padding: '12px 20px',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          fontSize: '14px',
          fontWeight: '500',
          animation: 'slideIn 0.3s ease-out'
        }}>
          {lastSyncNotification}
        </div>
      )}

      {/* Floating Toggle Button */}
      <button
        onClick={() => setShowNavbar(!showNavbar)}
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          width: '40px',
          height: '40px',
          borderRadius: '50%',
          backgroundColor: theme === 'dark' ? '#374151' : '#f3f4f6',
          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
          cursor: 'pointer',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '18px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          transition: 'all 0.3s ease',
          opacity: showNavbar ? 1 : 0.7
        }}
        title={showNavbar ? 'Hide Controls' : 'Show Controls'}
      >
        {showNavbar ? '×' : '☰'}
      </button>

      {/* Header - Always Visible */}
      <header style={{
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        padding: isMobile ? '16px 20px' : '24px 32px',
        position: 'sticky',
        top: 0,
        zIndex: 50,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        animation: 'slideDown 0.3s ease-out'
      }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <h1 style={{
              margin: 0,
              fontSize: isMobile ? '28px' : '42px',
              fontWeight: '700',
              color: '#ffffff',
              textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
              letterSpacing: '2px'
            }}>
              Dashboard Incident MCDTech
            </h1>
          </div>
        </div>
      </header>

      {/* Navigation Menu */}
      {showNavbar && (
        <div style={{
          background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
          padding: '20px 24px',
          borderBottom: '3px solid #b45309'
        }}>
        <div style={{ maxWidth: '1400px', margin: '0 auto' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '24px' }}>
              {/* DBSYS Logo */}
              <DBSYSLogo width={isMobile ? 120 : 180} height={isMobile ? 30 : 45} />

              {!isMobile && (
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  color: '#ffffff',
                  fontSize: '14px',
                  fontWeight: '500',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  padding: '6px 12px',
                  borderRadius: '20px',
                  backdropFilter: 'blur(4px)'
                }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    backgroundColor: getConnectionStatusColor(),
                    borderRadius: '50%',
                    border: '1px solid rgba(255, 255, 255, 0.3)'
                  }} className="animate-pulse"></div>
                  <span style={{ textTransform: 'capitalize' }}>{connectionStatus}</span>
                </div>
              )}
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              {isMobile ? (
                <button
                  onClick={() => setShowMobileMenu(!showMobileMenu)}
                  style={{
                    padding: '10px',
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    border: '1px solid rgba(255, 255, 255, 0.3)',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    color: '#ffffff',
                    fontSize: '16px',
                    backdropFilter: 'blur(4px)'
                  }}
                >
                  ☰
                </button>
              ) : (
                <>
                  <div style={{ position: 'relative' }}>
                    <input
                      type="text"
                      placeholder="Search tickets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{
                        width: '300px',
                        padding: '10px 12px 10px 40px',
                        border: '1px solid rgba(255, 255, 255, 0.3)',
                        borderRadius: '8px',
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        color: '#1f2937',
                        fontSize: '14px',
                        backdropFilter: 'blur(4px)'
                      }}
                    />
                    <div style={{
                      position: 'absolute',
                      left: '12px',
                      top: '50%',
                      transform: 'translateY(-50%)',
                      color: '#6b7280'
                    }}>
                      ⌕
                    </div>
                  </div>

                  <button
                    onClick={() => setShowTable(!showTable)}
                    className="btn"
                    style={{
                      backgroundColor: showTable ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    {showTable ? 'Hide' : 'Show'} Table
                  </button>

                  <button
                    onClick={() => setShowChartsConfig(!showChartsConfig)}
                    className="btn"
                    style={{
                      backgroundColor: showChartsConfig ? 'rgba(139, 92, 246, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Charts
                  </button>

                  <button
                    onClick={() => setShowCardsConfig(!showCardsConfig)}
                    className="btn"
                    style={{
                      backgroundColor: showCardsConfig ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Cards
                  </button>

                  <button
                    onClick={() => setShowAdvancedConfig(!showAdvancedConfig)}
                    className="btn"
                    style={{
                      backgroundColor: showAdvancedConfig ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Advanced Config
                  </button>

                  {/* Service Status Indicator */}
                  {serviceStatus && !serviceStatus.healthy && (
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '6px',
                      padding: '6px 10px',
                      backgroundColor: '#fef2f2',
                      border: '1px solid #fecaca',
                      borderRadius: '6px',
                      fontSize: '12px'
                    }}>
                      <span style={{ color: '#dc2626' }}>⚠️ Service Issue</span>
                      <button
                        onClick={forceServiceRecovery}
                        disabled={isRecoveringService}
                        style={{
                          backgroundColor: '#dc2626',
                          color: 'white',
                          border: 'none',
                          padding: '3px 6px',
                          borderRadius: '3px',
                          fontSize: '10px',
                          cursor: isRecoveringService ? 'not-allowed' : 'pointer',
                          opacity: isRecoveringService ? 0.6 : 1
                        }}
                      >
                        {isRecoveringService ? '🔄' : '🔧 Fix'}
                      </button>
                    </div>
                  )}

                  <button
                    onClick={() => setLayoutMode(layoutMode === 'grid' ? 'drag' : 'grid')}
                    className="btn"
                    style={{
                      backgroundColor: layoutMode === 'drag' ? 'rgba(245, 158, 11, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    {layoutMode === 'drag' ? 'Lock Layout' : 'Drag Mode'}
                  </button>

                  <button
                    onClick={() => {
                      // Export configuration
                      const exportConfig = {
                        version: '1.0',
                        timestamp: new Date().toISOString(),
                        theme,
                        config
                      };
                      const blob = new Blob([JSON.stringify(exportConfig, null, 2)], { type: 'application/json' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `jira-dashboard-config-${new Date().toISOString().slice(0, 10)}.json`;
                      a.click();
                      URL.revokeObjectURL(url);
                      alert('Configuration exported successfully!');
                    }}
                    className="btn"
                    style={{
                      backgroundColor: 'rgba(139, 92, 246, 0.9)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Export Config
                  </button>

                  <button
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = '.json';
                      input.onchange = (e) => {
                        const file = e.target.files[0];
                        if (file) {
                          const reader = new FileReader();
                          reader.onload = (e) => {
                            try {
                              const importedData = JSON.parse(e.target.result);
                              if (importedData.theme) setTheme(importedData.theme);
                              if (importedData.config) {
                                setConfig(importedData.config);
                              } else if (importedData.version) {
                                // Handle legacy format
                                setConfig(importedData);
                              }
                              alert('Configuration imported successfully!');
                            } catch (error) {
                              alert('Error importing configuration: ' + error.message);
                            }
                          };
                          reader.readAsText(file);
                        }
                      };
                      input.click();
                    }}
                    className="btn"
                    style={{
                      backgroundColor: 'rgba(6, 182, 212, 0.9)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Import Config
                  </button>

                  <button
                    onClick={loadDashboardData}
                    className="btn btn-primary"
                    style={{
                      backgroundColor: 'rgba(59, 130, 246, 0.9)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    Refresh
                  </button>

                  <button
                    onClick={async () => {
                      try {
                        const response = await fetch('/api/sync/incremental', { method: 'POST' });
                        const data = await response.json();
                        if (data.success) {
                          setLastSyncNotification(`✅ Manual sync completed! ${data.updatedTickets || 0} tickets updated`);
                          setTimeout(() => setLastSyncNotification(null), 5000);
                          // Refresh dashboard after sync
                          setTimeout(() => loadDashboardData(), 1000);
                        }
                      } catch (error) {
                        console.error('Manual sync failed:', error);
                      }
                    }}
                    className="btn"
                    style={{
                      backgroundColor: 'rgba(245, 158, 11, 0.9)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    ⚡ Force Sync
                  </button>

                  <button
                    onClick={toggleFullScreen}
                    className="btn"
                    style={{
                      backgroundColor: isFullScreen ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                    title={isFullScreen ? 'Exit Full Screen' : 'Enter Full Screen'}
                  >
                    {isFullScreen ? 'Exit Full Screen' : 'Full Screen'}
                  </button>

                  <button
                    onClick={toggleAutoStart}
                    className="btn"
                    style={{
                      backgroundColor: autoStartEnabled ? 'rgba(16, 185, 129, 0.9)' : 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(4px)'
                    }}
                    title={autoStartEnabled ? 'Disable Auto-Start' : 'Enable Auto-Start'}
                  >
                    {autoStartEnabled ? 'Auto-Start ON' : 'Auto-Start OFF'}
                  </button>

                  <button
                    onClick={toggleTheme}
                    style={{
                      padding: '10px 12px',
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      fontSize: '16px',
                      color: '#ffffff',
                      backdropFilter: 'blur(4px)'
                    }}
                  >
                    {theme === 'dark' ? '☀️' : '🌙'}
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobile && showMobileMenu && (
            <div style={{
              marginTop: '16px',
              padding: '16px',
              backgroundColor: theme === 'dark' ? '#374151' : '#f9fafb',
              borderRadius: '8px',
              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e5e7eb'}`
            }}>
              <div style={{ marginBottom: '12px' }}>
                <input
                  type="text"
                  placeholder="Search tickets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                <button
                  onClick={() => {
                    setShowTable(!showTable);
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: showTable ? '#10b981' : '#6b7280',
                    color: 'white'
                  }}
                >
                  {showTable ? 'Hide' : 'Show'} Table
                </button>

                <button
                  onClick={() => {
                    setShowChartsConfig(!showChartsConfig);
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: showChartsConfig ? '#8b5cf6' : '#6b7280',
                    color: 'white'
                  }}
                >
                  Charts
                </button>

                <button
                  onClick={() => {
                    setShowCardsConfig(!showCardsConfig);
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: showCardsConfig ? '#10b981' : '#6b7280',
                    color: 'white'
                  }}
                >
                  Cards
                </button>

                <button
                  onClick={() => {
                    setShowAdvancedConfig(!showAdvancedConfig);
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: showAdvancedConfig ? '#10b981' : '#6b7280',
                    color: 'white'
                  }}
                >
                  Advanced
                </button>

                <button
                  onClick={() => {
                    setLayoutMode(layoutMode === 'grid' ? 'drag' : 'grid');
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: layoutMode === 'drag' ? '#f59e0b' : '#6b7280',
                    color: 'white'
                  }}
                >
                  {layoutMode === 'drag' ? 'Lock' : 'Drag'}
                </button>

                <button
                  onClick={() => {
                    // Export configuration
                    const config = {
                      theme,
                      chartConfigs,
                      assigneeGroups,
                      selectedAssigneeGroup,
                      layoutPositions: {}
                    };
                    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'dashboard-config.json';
                    a.click();
                    URL.revokeObjectURL(url);
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: '#8b5cf6',
                    color: 'white'
                  }}
                >
                  Export
                </button>

                <button
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    input.onchange = (e) => {
                      const file = e.target.files[0];
                      if (file) {
                        const reader = new FileReader();
                        reader.onload = (e) => {
                          try {
                            const config = JSON.parse(e.target.result);
                            if (config.theme) setTheme(config.theme);
                            if (config.chartConfigs) setChartConfigs(config.chartConfigs);
                            if (config.assigneeGroups) setAssigneeGroups(config.assigneeGroups);
                            if (config.selectedAssigneeGroup) setSelectedAssigneeGroup(config.selectedAssigneeGroup);
                            alert('Configuration imported successfully!');
                          } catch (error) {
                            alert('Error importing configuration: ' + error.message);
                          }
                        };
                        reader.readAsText(file);
                      }
                    };
                    input.click();
                    setShowMobileMenu(false);
                  }}
                  className="btn"
                  style={{
                    flex: 1,
                    backgroundColor: '#06b6d4',
                    color: 'white'
                  }}
                >
                  Import
                </button>
              </div>

              <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', marginTop: '8px' }}>
                <button
                  onClick={() => {
                    loadDashboardData();
                    setShowMobileMenu(false);
                  }}
                  className="btn btn-primary"
                  style={{ flex: 1 }}
                >
                  Refresh
                </button>

                <button
                  onClick={toggleTheme}
                  className="btn btn-secondary"
                  style={{ flex: 1 }}
                >
                  {theme === 'dark' ? '☀️ Light' : '🌙 Dark'}
                </button>
              </div>

              <div style={{
                marginTop: '12px',
                padding: '8px',
                backgroundColor: theme === 'dark' ? '#1f2937' : '#f3f4f6',
                borderRadius: '6px',
                fontSize: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <span>{getConnectionStatusIcon()}</span>
                <span>Status: {connectionStatus}</span>
              </div>
            </div>
          )}
        </div>
        </div>
      )}

      {/* Main Content */}
      <main style={{ maxWidth: '1400px', margin: '0 auto', padding: isMobile ? '16px' : '32px' }}>
        {/* Minimal Stats Bar */}
        <div style={{
          position: 'fixed',
          bottom: '10px',
          left: '50%',
          transform: 'translateX(-50%)',
          display: 'flex',
          gap: '16px',
          padding: '6px 16px',
          backgroundColor: theme === 'dark' ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(8px)',
          borderRadius: '20px',
          border: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
          fontSize: '12px',
          color: theme === 'dark' ? '#9ca3af' : '#64748b',
          zIndex: 100,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <span>Total Tickets</span>
            <span style={{ fontWeight: 'bold', color: '#3b82f6' }}>
              {dashboardData.totalCount.toLocaleString()}
            </span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <span>Performance</span>
            <span style={{ fontWeight: 'bold', color: '#10b981' }}>
              {performance ? `${performance.loadTimeMs}ms` : 'N/A'}
            </span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <span>Last Update</span>
            <span style={{ fontWeight: 'bold', color: '#8b5cf6' }}>
              {lastUpdate ? lastUpdate.toLocaleTimeString() : 'Never'}
            </span>
          </div>
        </div>

        {/* Status Cards - Top Row */}
        <div style={{ marginBottom: isMobile ? '24px' : '32px' }}>
          <div className="grid grid-auto-fit">
            {getStatusCards().map((card, index) => (
              <DashboardCard
                key={`status-${card.id || index}`}
                card={card}
                theme={theme}
                isMobile={isMobile}
              />
            ))}
          </div>
        </div>

        {/* Main Content Layout - Charts and Priority Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: isMobile ? '1fr' : '3fr 1fr',
          gap: '24px',
          marginBottom: isMobile ? '24px' : '32px'
        }}>
          {/* Left Side - Charts */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            {/* Charts Side by Side */}
            <div style={{
              display: 'grid',
              gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr',
              gap: '24px'
            }}>
              {/* Organization Chart */}
              {(config.charts?.organizationChart?.visible !== false) && (
                <div className="card" style={{ padding: '20px' }}>
                  <h2 style={{
                    margin: '0 0 16px 0',
                    fontSize: isMobile ? '18px' : '20px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    RESTAURANTS
                  </h2>
                  <OrganizationChart data={topOrganizations} config={config.charts.organizationChart} />
                </div>
              )}

              {/* Weekly Chart */}
              {(config.charts?.weeklyChart?.visible !== false) && (
                <div className="card" style={{ padding: '20px' }}>
                  <h2 style={{
                    margin: '0 0 16px 0',
                    fontSize: isMobile ? '18px' : '20px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    Tickets
                  </h2>
                  <WeeklyTrendsChart data={weeklyTickets} config={config.charts.weeklyChart} />
                </div>
              )}
            </div>

            {/* DBSYS Logo at Bottom */}
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: isMobile ? '20px' : '40px 32px',
              marginTop: isMobile ? '16px' : '32px',
              backgroundColor: theme === 'dark' ? 'rgba(31, 41, 55, 0.3)' : 'rgba(255, 255, 255, 0.5)',
              borderRadius: '12px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
              border: `1px solid ${theme === 'dark' ? 'rgba(75, 85, 99, 0.3)' : 'rgba(229, 231, 235, 0.5)'}`
            }}>
              <DBSYSLogo width={isMobile ? 280 : 400} height={isMobile ? 90 : 130} />
            </div>
          </div>

          {/* Right Side - Priority Cards */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            {getPriorityCards().map((card, index) => (
              <DashboardCard
                key={`priority-${card.id || index}`}
                card={card}
                theme={theme}
                isMobile={isMobile}
              />
            ))}
          </div>
        </div>









        {/* Recent Activity */}
        {dashboardData.stats?.recentActivity && (
          <div className="card" style={{
            background: `linear-gradient(135deg, ${theme === 'dark' ? '#1f2937' : '#ffffff'}, ${theme === 'dark' ? '#374151' : '#f8fafc'})`,
            border: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
          }}>
            <h2 style={{
              marginBottom: '20px',
              fontSize: isMobile ? '20px' : '24px',
              fontWeight: '600',
              color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
            }}>
              📋 Recent Activity
            </h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {dashboardData.stats.recentActivity.slice(0, isMobile ? 3 : 8).map((ticket, index) => (
                <div
                  key={ticket.key || index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: isMobile ? 'flex-start' : 'center',
                    flexDirection: isMobile ? 'column' : 'row',
                    padding: '16px',
                    backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                    borderRadius: '8px',
                    borderLeft: `4px solid ${index < 3 ? '#10b981' : '#64748b'}`,
                    transition: 'all 0.2s ease'
                  }}
                  className="card-hover"
                >
                  <div style={{ flex: 1 }}>
                    <span style={{
                      fontWeight: 'bold',
                      color: theme === 'dark' ? '#e5e7eb' : '#1e293b',
                      fontSize: '14px'
                    }}>
                      {ticket.key}
                    </span>
                    <span style={{
                      marginLeft: isMobile ? '0' : '12px',
                      color: theme === 'dark' ? '#d1d5db' : '#64748b',
                      display: isMobile ? 'block' : 'inline',
                      fontSize: '13px'
                    }}>
                      {ticket.summary}
                    </span>
                  </div>
                  <div style={{
                    fontSize: '12px',
                    color: theme === 'dark' ? '#9ca3af' : '#64748b',
                    marginTop: isMobile ? '8px' : '0'
                  }}>
                    {ticket.status} • {new Date(ticket.updated).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Tickets Table */}
        {showTable && (
          <div className="card" style={{
            marginTop: '32px',
            background: `linear-gradient(135deg, ${theme === 'dark' ? '#1f2937' : '#ffffff'}, ${theme === 'dark' ? '#374151' : '#f8fafc'})`,
            border: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',

              alignItems: 'center',
              marginBottom: '20px',
              flexWrap: 'wrap',
              gap: '12px'
            }}>
              <h2 style={{
                margin: 0,
                fontSize: isMobile ? '20px' : '24px',
                fontWeight: '600',
                color: theme === 'dark' ? '#e5e7eb' : '#1e293b',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                📋 All Tickets ({filteredTickets.length.toLocaleString()})
                {isFiltering && (
                  <div style={{
                    width: '16px',
                    height: '16px',
                    border: '2px solid #e2e8f0',
                    borderTop: '2px solid #3b82f6',
                    borderRadius: '50%',
                    animation: 'spin 1s linear infinite'
                  }}></div>
                )}
              </h2>

              <div style={{ display: 'flex', gap: '8px', alignItems: 'center', flexWrap: 'wrap' }}>
                {/* Filter Summary */}
                {(statusFilter || assigneeFilter || priorityFilter || dateFilter || customDateFrom || customDateTo || debouncedSearchTerm) && (
                  <div style={{
                    fontSize: '12px',
                    color: theme === 'dark' ? '#9ca3af' : '#64748b',
                    padding: '4px 8px',
                    backgroundColor: theme === 'dark' ? '#374151' : '#f3f4f6',
                    borderRadius: '4px'
                  }}>
                    Filtered: {filteredTickets.length} of {dashboardData.totalCount} tickets
                  </div>
                )}

                {/* Clear Filters Button */}
                {(statusFilter || assigneeFilter || priorityFilter || dateFilter || customDateFrom || customDateTo || debouncedSearchTerm) && (
                  <button
                    onClick={() => {
                      setStatusFilter('');
                      setAssigneeFilter('');
                      setPriorityFilter('');
                      setDateFilter('');
                      setCustomDateFrom('');
                      setCustomDateTo('');
                      setSearchTerm('');
                    }}
                    style={{
                      padding: '6px 12px',
                      backgroundColor: '#ef4444',
                      color: 'white',
                      border: 'none',
                      borderRadius: '6px',


                      fontSize: '12px',
                      cursor: 'pointer'
                    }}
                  >
                    🗑️ Clear Filters
                  </button>
                )}
              </div>
            </div>

            {/* Advanced Filters */}
            <div style={{
              display: 'flex',
              gap: '12px',
              marginBottom: '20px',
              flexWrap: 'wrap',
              alignItems: 'center'
            }}>
              {/* Status Filter */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                  Status
                </label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  style={{
                    padding: '6px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px',
                    minWidth: '120px'
                  }}
                >
                  <option value="">All Statuses</option>
                  {Object.keys(dashboardData.stats?.statusCounts || {}).map(status => (
                    <option key={status} value={status}>{status}</option>
                  ))}
                </select>
              </div>



              {/* Priority Filter */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                  Priority
                </label>
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  style={{
                    padding: '6px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px',
                    minWidth: '120px'
                  }}
                >
                  <option value="">All Priorities</option>
                  {Object.keys(dashboardData.stats?.priorityCounts || {}).map(priority => (
                    <option key={priority} value={priority}>{priority}</option>
                  ))}
                </select>
              </div>

              {/* Assignee Filter */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                  Assignee
                </label>
                <select
                  value={assigneeFilter}
                  onChange={(e) => setAssigneeFilter(e.target.value)}
                  style={{
                    padding: '6px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px',
                    minWidth: '180px'
                  }}
                >
                  <option value="">All Assignees</option>
                  <option value="unassigned">🚫 Unassigned</option>
                  {getAllAssignees().map(assignee => (
                    <option key={assignee.key} value={assignee.displayName}>
                      👤 {assignee.displayName}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date Filter */}
              <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                  Date Range
                </label>
                <select
                  value={dateFilter}
                  onChange={(e) => {
                    setDateFilter(e.target.value);
                    if (e.target.value !== 'custom') {
                      setCustomDateFrom('');
                      setCustomDateTo('');
                    }
                  }}
                  style={{
                    padding: '6px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px',
                    minWidth: '140px'
                  }}
                >
                  <option value="">All Time</option>
                  <option value="today">Today</option>
                  <option value="yesterday">Yesterday</option>
                  <option value="last7days">Last 7 Days</option>
                  <option value="last30days">Last 30 Days</option>
                  <option value="last90days">Last 90 Days</option>
                  <option value="custom">Custom Range</option>
                </select>
              </div>

              {/* Custom Date Range */}
              {dateFilter === 'custom' && (
                <>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                      From Date
                    </label>
                    <input
                      type="date"
                      value={customDateFrom}
                      onChange={(e) => setCustomDateFrom(e.target.value)}
                      style={{
                        padding: '6px 12px',
                        border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        borderRadius: '6px',
                        backgroundColor: theme === 'dark' ? '#374151' : 'white',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                        fontSize: '14px',
                        minWidth: '140px'
                      }}
                    />
                  </div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                    <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                      To Date
                    </label>
                    <input
                      type="date"
                      value={customDateTo}
                      onChange={(e) => setCustomDateTo(e.target.value)}
                      style={{
                        padding: '6px 12px',
                        border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        borderRadius: '6px',
                        backgroundColor: theme === 'dark' ? '#374151' : 'white',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                        fontSize: '14px',
                        minWidth: '140px'
                      }}
                    />
                  </div>
                </>
              )}

              <div style={{ display: 'flex', gap: '8px', alignItems: 'center', flexWrap: 'wrap' }}>
                {/* Items per page selector */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                  <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                    Show:
                  </label>
                  <select
                    value={itemsPerPage}
                    onChange={(e) => {
                      setItemsPerPage(Number(e.target.value));
                      setCurrentPage(1);
                    }}
                    style={{
                      padding: '4px 8px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                      borderRadius: '4px',
                      backgroundColor: theme === 'dark' ? '#374151' : 'white',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                      fontSize: '12px'
                    }}
                  >
                    <option value={25}>25</option>
                    <option value={50}>50</option>
                    <option value={100}>100</option>
                    <option value={200}>200</option>
                  </select>
                </div>

                {/* Show Latest First Toggle */}
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <input
                    type="checkbox"
                    id="showLatestFirst"
                    checked={showLatestFirst}
                    onChange={(e) => setShowLatestFirst(e.target.checked)}
                    style={{ transform: 'scale(1.2)' }}
                  />
                  <label
                    htmlFor="showLatestFirst"
                    style={{
                      fontSize: '12px',
                      color: theme === 'dark' ? '#9ca3af' : '#64748b',
                      cursor: 'pointer',
                      userSelect: 'none'
                    }}
                  >
                    📅 Latest First
                  </label>
                </div>

                <select
                  value={`${sortField}-${sortDirection}`}
                  onChange={(e) => {
                    const [field, direction] = e.target.value.split('-');
                    setSortField(field);
                    setSortDirection(direction);
                  }}
                  style={{
                    padding: '6px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#374151' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                >
                  <option value="updated-desc">Updated (Newest)</option>
                  <option value="updated-asc">Updated (Oldest)</option>
                  <option value="created-desc">Created (Newest)</option>
                  <option value="created-asc">Created (Oldest)</option>
                  <option value="key-asc">Key (A-Z)</option>
                  <option value="key-desc">Key (Z-A)</option>
                  <option value="status-asc">Status (A-Z)</option>
                  <option value="priority-desc">Priority</option>
                </select>

                <span style={{
                  fontSize: '14px',
                  color: theme === 'dark' ? '#9ca3af' : '#64748b'
                }}>
                  Page {currentPage} of {getTotalPages()}
                </span>
              </div>
            </div>

            {/* Table */}
            <div style={{ overflowX: 'auto' }}>
              <table style={{
                width: '100%',
                borderCollapse: 'collapse',
                fontSize: isMobile ? '12px' : '14px'
              }}>
                <thead>
                  <tr style={{
                    backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                    borderBottom: `2px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
                  }}>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('key')}>
                      Key {sortField === 'key' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('summary')}>
                      Summary {sortField === 'summary' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('status')}>
                      Status {sortField === 'status' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('priority')}>
                      Priority {sortField === 'priority' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('assignee')}>
                      Assignee {sortField === 'assignee' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }}>
                      Organization
                    </th>
                    <th style={{
                      padding: '12px 8px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      fontWeight: '600',
                      color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                    }} onClick={() => handleSort('updated')}>
                      Updated {sortField === 'updated' && (sortDirection === 'asc' ? '↑' : '↓')}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {getPaginatedTickets().map((ticket, index) => (
                    <tr key={ticket.key || index} style={{
                      borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
                      backgroundColor: index % 2 === 0
                        ? (theme === 'dark' ? '#1f2937' : '#ffffff')
                        : (theme === 'dark' ? '#374151' : '#f8fafc')
                    }}>
                      <td style={{
                        padding: '12px 8px',
                        fontWeight: '600',
                        color: '#3b82f6'
                      }}>
                        {ticket.key}
                      </td>
                      <td style={{
                        padding: '12px 8px',
                        maxWidth: isMobile ? '150px' : '300px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                      }}>
                        {getFieldValue(ticket, 'summary')}
                      </td>
                      <td style={{ padding: '12px 8px' }}>
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getFieldValue(ticket, 'status') === 'Open' ? '#fef3c7' :
                                         getFieldValue(ticket, 'status') === 'In Progress' ? '#dbeafe' :
                                         getFieldValue(ticket, 'status') === 'Resolved' ? '#d1fae5' : '#f3f4f6',
                          color: getFieldValue(ticket, 'status') === 'Open' ? '#92400e' :
                                getFieldValue(ticket, 'status') === 'In Progress' ? '#1e40af' :
                                getFieldValue(ticket, 'status') === 'Resolved' ? '#065f46' : '#374151'
                        }}>
                          {getFieldValue(ticket, 'status')}
                        </span>
                      </td>
                      <td style={{ padding: '12px 8px' }}>
                        <span style={{
                          padding: '4px 8px',
                          borderRadius: '4px',
                          fontSize: '12px',
                          fontWeight: '500',
                          backgroundColor: getFieldValue(ticket, 'priority') === 'Urgent' ? '#fee2e2' :
                                         getFieldValue(ticket, 'priority') === 'High' ? '#fed7aa' :
                                         getFieldValue(ticket, 'priority') === 'Medium' ? '#fef3c7' : '#f0f9ff',
                          color: getFieldValue(ticket, 'priority') === 'Urgent' ? '#991b1b' :
                                getFieldValue(ticket, 'priority') === 'High' ? '#9a3412' :
                                getFieldValue(ticket, 'priority') === 'Medium' ? '#92400e' : '#0369a1'
                        }}>
                          {getFieldValue(ticket, 'priority')}
                        </span>
                      </td>
                      <td style={{
                        padding: '12px 8px',
                        color: theme === 'dark' ? '#d1d5db' : '#64748b'
                      }}>
                        <div>
                          <div style={{ fontWeight: '500' }}>
                            {getAssignee(ticket).displayName}
                          </div>
                          {getAssignee(ticket).emailAddress && getAssignee(ticket).key !== 'unassigned' && (
                            <div style={{
                              fontSize: '10px',
                              color: theme === 'dark' ? '#9ca3af' : '#64748b',
                              marginTop: '2px'
                            }}>
                              {getAssignee(ticket).emailAddress}
                            </div>
                          )}
                        </div>
                      </td>
                      <td style={{
                        padding: '12px 8px',
                        color: theme === 'dark' ? '#d1d5db' : '#64748b',
                        fontWeight: '500'
                      }}>
                        {getOrganization(ticket)}
                      </td>
                      <td style={{
                        padding: '12px 8px',
                        color: theme === 'dark' ? '#9ca3af' : '#64748b',
                        fontSize: '12px'
                      }}>
                        {getFieldValue(ticket, 'updated') ? new Date(getFieldValue(ticket, 'updated')).toLocaleDateString() : 'N/A'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {getTotalPages() > 1 && (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '8px',
                marginTop: '20px',
                flexWrap: 'wrap'
              }}>
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="btn"
                  style={{
                    backgroundColor: currentPage === 1 ? '#9ca3af' : '#6b7280',
                    color: 'white',
                    padding: '6px 12px',
                    fontSize: '14px'
                  }}
                >
                  ← Previous
                </button>

                <span style={{
                  padding: '6px 12px',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}>
                  {currentPage} / {getTotalPages()}
                </span>

                <button
                  onClick={() => setCurrentPage(Math.min(getTotalPages(), currentPage + 1))}
                  disabled={currentPage === getTotalPages()}
                  className="btn"
                  style={{
                    backgroundColor: currentPage === getTotalPages() ? '#9ca3af' : '#6b7280',
                    color: 'white',
                    padding: '6px 12px',
                    fontSize: '14px'
                  }}
                >
                  Next →
                </button>
              </div>
            )}
          </div>
        )}

        {/* Advanced Configuration Panel */}
        {showAdvancedConfig && (
          <div className="card" style={{
            marginBottom: '24px',
            background: `linear-gradient(135deg, ${theme === 'dark' ? '#1f2937' : '#ffffff'}, ${theme === 'dark' ? '#374151' : '#f8fafc'})`,
            border: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
          }}>
            <ConfigurationManager
              config={config}
              onConfigChange={setConfig}
              theme={theme}
              dashboardData={dashboardData}
              getAllAssignees={getAllAssignees}
            />
          </div>
        )}

        {/* Separate Charts Configuration Panel */}
        {showChartsConfig && (
          <div className="card" style={{
            marginBottom: '24px',
            background: `linear-gradient(135deg, ${theme === 'dark' ? '#1f2937' : '#ffffff'}, ${theme === 'dark' ? '#374151' : '#f8fafc'})`,
            border: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
            boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)'
          }}>
            <div style={{ padding: '20px' }}>
              <EnhancedChartConfigurator
                config={config}
                onConfigChange={setConfig}
                theme={theme}
                dashboardData={dashboardData}
                autoApply={chartAutoApply}
                onAutoApplyChange={setChartAutoApply}
              />




            </div>
          </div>
        )}

        {/* Cards Configuration Panel */}
        {showCardsConfig && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px'
          }}>
            <div style={{
              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
              borderRadius: '12px',
              padding: '24px',
              maxWidth: '900px',
              width: '100%',
              maxHeight: '80vh',
              overflowY: 'auto',
              boxShadow: '0 20px 50px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '24px'
              }}>
                <h2 style={{
                  margin: 0,
                  fontSize: '24px',
                  fontWeight: '600',
                  color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                }}>
                  Cards Configuration
                </h2>
                <button
                  onClick={() => setShowCardsConfig(false)}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '24px',
                    cursor: 'pointer',
                    color: theme === 'dark' ? '#9ca3af' : '#64748b'
                  }}
                >
                  ✕
                </button>
              </div>

              <OptimizedConfigurationManager
                config={config}
                onConfigChange={setConfig}
                theme={theme}
                dashboardData={dashboardData}
                activeConfigTab={activeConfigTab}
                setActiveConfigTab={setActiveConfigTab}
                updateCardConfig={updateCardConfig}
                addNewCard={addNewCard}
                getAllAssignees={getAllAssignees}
              />
            </div>
          </div>
        )}

        {/* Configuration Panel */}
        {showConfig && (
          <div style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 1000,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            padding: '20px'
          }}>
            <div style={{
              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
              borderRadius: '12px',
              padding: '24px',
              maxWidth: '800px',
              width: '100%',
              maxHeight: '80vh',
              overflowY: 'auto',
              boxShadow: '0 20px 50px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '24px'
              }}>
                <h2 style={{
                  margin: 0,
                  fontSize: '24px',
                  fontWeight: '600',
                  color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                }}>
                  Dashboard Configuration
                </h2>
                <button
                  onClick={() => setShowConfig(false)}
                  style={{
                    background: 'none',
                    border: 'none',
                    fontSize: '24px',
                    cursor: 'pointer',
                    color: theme === 'dark' ? '#9ca3af' : '#64748b'
                  }}
                >
                  ✕
                </button>
              </div>

              {/* Configuration Tabs */}
              <div style={{
                display: 'flex',
                borderBottom: `1px solid ${theme === 'dark' ? '#374151' : '#e5e7eb'}`,
                marginBottom: '24px'
              }}>
                {[
                  { id: 'status', label: '📊 Status Cards', icon: '📊' },
                  { id: 'priority', label: '🔥 Priority Cards', icon: '🔥' },
                  { id: 'organization', label: '🏢 Organization Cards', icon: '🏢' },
                  { id: 'general', label: '⚙️ General Settings', icon: '⚙️' }
                ].map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveConfigTab(tab.id)}
                    style={{
                      background: 'none',
                      border: 'none',
                      padding: '12px 16px',
                      cursor: 'pointer',
                      fontSize: '14px',
                      fontWeight: activeConfigTab === tab.id ? '600' : '400',
                      color: activeConfigTab === tab.id
                        ? (theme === 'dark' ? '#60a5fa' : '#2563eb')
                        : (theme === 'dark' ? '#9ca3af' : '#64748b'),
                      borderBottom: activeConfigTab === tab.id
                        ? `2px solid ${theme === 'dark' ? '#60a5fa' : '#2563eb'}`
                        : '2px solid transparent',
                      transition: 'all 0.2s ease'
                    }}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>

              {/* Status Cards Configuration */}
              {activeConfigTab === 'status' && (
              <div style={{ marginBottom: '32px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <h3 style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    📊 Status Cards
                  </h3>
                  <button
                    onClick={() => addNewCard('status')}
                    className="btn"
                    style={{
                      backgroundColor: '#10b981',
                      color: 'white',
                      padding: '6px 12px',
                      fontSize: '12px'
                    }}
                  >
                    + Add Card
                  </button>
                </div>
                <div style={{
                  display: 'grid',
                  gap: '16px',
                  maxHeight: '60vh',
                  overflowY: 'auto',
                  paddingRight: '8px'
                }}>
                  {config.statusCards.map((card, index) => (
                    <div key={card.id} style={{
                      padding: '16px',
                      backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                      borderRadius: '8px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
                    }}>
                      {/* Card Header */}
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                        <input
                          type="checkbox"
                          checked={card.visible}
                          onChange={(e) => updateCardConfig('status', card.id, { visible: e.target.checked })}
                          style={{ transform: 'scale(1.2)' }}
                        />
                        <input
                          type="text"
                          placeholder="Card Title"
                          value={card.title}
                          onChange={(e) => updateCardConfig('status', card.id, { title: e.target.value })}
                          style={{
                            flex: 1,
                            padding: '8px 12px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '6px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                          }}
                        />
                        <div style={{ display: 'flex', gap: '4px' }}>
                          <button
                            onClick={() => moveCard('status', card.id, 'up')}
                            disabled={index === 0}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === 0 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === 0 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↑
                          </button>
                          <button
                            onClick={() => moveCard('status', card.id, 'down')}
                            disabled={index === config.statusCards.length - 1}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === config.statusCards.length - 1 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === config.statusCards.length - 1 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↓
                          </button>
                          <button
                            onClick={() => deleteCard('status', card.id)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ✕
                          </button>
                        </div>
                      </div>

                      {/* Card Properties */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px', marginBottom: '12px' }}>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Icon
                          </label>
                          <input
                            type="text"
                            placeholder="📊"
                            value={card.icon}
                            onChange={(e) => updateCardConfig('status', card.id, { icon: e.target.value })}
                            style={{
                              width: '100%',
                              padding: '6px 8px',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                              fontSize: '14px'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Background Color
                          </label>
                          <input
                            type="color"
                            value={card.color}
                            onChange={(e) => updateCardConfig('status', card.id, { color: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Text Color
                          </label>
                          <input
                            type="color"
                            value={card.textColor}
                            onChange={(e) => updateCardConfig('status', card.id, { textColor: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                      </div>

                      {/* Description */}
                      <div style={{ marginBottom: '12px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Description
                        </label>
                        <input
                          type="text"
                          placeholder="Card description"
                          value={card.description}
                          onChange={(e) => updateCardConfig('status', card.id, { description: e.target.value })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                      </div>

                      {/* Date Filter Configuration */}
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Date Filter
                        </label>
                        <select
                          value={card.dateFilter || 'all'}
                          onChange={(e) => updateCardConfig('status', card.id, {
                            dateFilter: e.target.value,
                            customDateFrom: e.target.value !== 'custom' ? '' : card.customDateFrom,
                            customDateTo: e.target.value !== 'custom' ? '' : card.customDateTo
                          })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        >
                          <option value="all">All Time</option>
                          <option value="today">Today</option>
                          <option value="yesterday">Yesterday</option>
                          <option value="last7days">Last 7 Days</option>
                          <option value="last30days">Last 30 Days</option>
                          <option value="last90days">Last 90 Days</option>
                          <option value="custom">Custom Range</option>
                        </select>

                        {/* Custom Date Range */}
                        {card.dateFilter === 'custom' && (
                          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginTop: '8px' }}>
                            <div>
                              <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '2px' }}>
                                From Date
                              </label>
                              <input
                                type="date"
                                value={card.customDateFrom || ''}
                                onChange={(e) => updateCardConfig('status', card.id, { customDateFrom: e.target.value })}
                                style={{
                                  width: '100%',
                                  padding: '4px 6px',
                                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                                  borderRadius: '4px',
                                  backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  fontSize: '11px'
                                }}
                              />
                            </div>
                            <div>
                              <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '2px' }}>
                                To Date
                              </label>
                              <input
                                type="date"
                                value={card.customDateTo || ''}
                                onChange={(e) => updateCardConfig('status', card.id, { customDateTo: e.target.value })}
                                style={{
                                  width: '100%',
                                  padding: '4px 6px',
                                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                                  borderRadius: '4px',
                                  backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  fontSize: '11px'
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Assignee Filter Configuration */}
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Assignee Filter
                        </label>
                        <select
                          value={card.assigneeFilter || 'all'}
                          onChange={(e) => updateCardConfig('status', card.id, {
                            assigneeFilter: e.target.value,
                            selectedAssignees: e.target.value !== 'specific' ? [] : card.selectedAssignees
                          })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        >
                          <option value="all">All Assignees</option>
                          <option value="unassigned">Unassigned Only</option>
                          <option value="specific">Specific Assignees</option>
                        </select>

                        {/* Specific Assignees Selection */}
                        {card.assigneeFilter === 'specific' && (
                          <div style={{ marginTop: '8px' }}>
                            <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                              Select Assignees
                            </label>
                            <div style={{
                              maxHeight: '120px',
                              overflowY: 'auto',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              padding: '4px'
                            }}>
                              {getAllAssignees().map(assignee => (
                                <label key={assignee.key} style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  padding: '4px 8px',
                                  cursor: 'pointer',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  borderRadius: '2px',
                                  ':hover': {
                                    backgroundColor: theme === 'dark' ? '#374151' : '#f3f4f6'
                                  }
                                }}>
                                  <input
                                    type="checkbox"
                                    checked={(card.selectedAssignees || []).includes(assignee.displayName)}
                                    onChange={(e) => {
                                      const currentAssignees = card.selectedAssignees || [];
                                      const newAssignees = e.target.checked
                                        ? [...currentAssignees, assignee.displayName]
                                        : currentAssignees.filter(name => name !== assignee.displayName);
                                      updateCardConfig('status', card.id, { selectedAssignees: newAssignees });
                                    }}
                                    style={{ marginRight: '8px', transform: 'scale(0.9)' }}
                                  />
                                  <div>
                                    <div style={{ fontWeight: '500' }}>{assignee.displayName}</div>
                                    {assignee.emailAddress && (
                                      <div style={{ fontSize: '10px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                                        {assignee.emailAddress}
                                      </div>
                                    )}
                                  </div>
                                </label>
                              ))}
                              {getAllAssignees().length === 0 && (
                                <div style={{
                                  padding: '8px',
                                  textAlign: 'center',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#9ca3af' : '#64748b'
                                }}>
                                  No assignees found
                                </div>
                              )}
                            </div>
                            {(card.selectedAssignees || []).length > 0 && (
                              <div style={{
                                marginTop: '4px',
                                fontSize: '10px',
                                color: theme === 'dark' ? '#9ca3af' : '#64748b'
                              }}>
                                Selected: {(card.selectedAssignees || []).join(', ')}
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Jira Status Mapping */}
                      <div>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Jira Statuses (comma-separated)
                        </label>
                        <input
                          type="text"
                          placeholder="Open, To Do, New"
                          value={card.jiraStatuses.join(', ')}
                          onChange={(e) => updateJiraMapping('status', card.id, 'statuses', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                        <div style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', marginTop: '4px' }}>
                          Available: {getAvailableJiraValues('status').join(', ')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              )}

              {/* Priority Cards Configuration */}
              {activeConfigTab === 'priority' && (
              <div style={{ marginBottom: '32px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <h3 style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    🚨 Priority Cards
                  </h3>
                  <button
                    onClick={() => addNewCard('priority')}
                    className="btn"
                    style={{
                      backgroundColor: '#10b981',
                      color: 'white',
                      padding: '6px 12px',
                      fontSize: '12px'
                    }}
                  >
                    + Add Card
                  </button>
                </div>
                <div style={{
                  display: 'grid',
                  gap: '16px',
                  maxHeight: '60vh',
                  overflowY: 'auto',
                  paddingRight: '8px'
                }}>
                  {config.priorityCards.map((card, index) => (
                    <div key={card.id} style={{
                      padding: '16px',
                      backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                      borderRadius: '8px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
                    }}>
                      {/* Card Header */}
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                        <input
                          type="checkbox"
                          checked={card.visible}
                          onChange={(e) => updateCardConfig('priority', card.id, { visible: e.target.checked })}
                          style={{ transform: 'scale(1.2)' }}
                        />
                        <input
                          type="text"
                          placeholder="Card Title"
                          value={card.title}
                          onChange={(e) => updateCardConfig('priority', card.id, { title: e.target.value })}
                          style={{
                            flex: 1,
                            padding: '8px 12px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '6px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                          }}
                        />
                        <div style={{ display: 'flex', gap: '4px' }}>
                          <button
                            onClick={() => moveCard('priority', card.id, 'up')}
                            disabled={index === 0}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === 0 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === 0 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↑
                          </button>
                          <button
                            onClick={() => moveCard('priority', card.id, 'down')}
                            disabled={index === config.priorityCards.length - 1}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === config.priorityCards.length - 1 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === config.priorityCards.length - 1 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↓
                          </button>
                          <button
                            onClick={() => deleteCard('priority', card.id)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ✕
                          </button>
                        </div>
                      </div>

                      {/* Card Properties */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px', marginBottom: '12px' }}>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Icon
                          </label>
                          <input
                            type="text"
                            placeholder="🚨"
                            value={card.icon}
                            onChange={(e) => updateCardConfig('priority', card.id, { icon: e.target.value })}
                            style={{
                              width: '100%',
                              padding: '6px 8px',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                              fontSize: '14px'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Background Color
                          </label>
                          <input
                            type="color"
                            value={card.color}
                            onChange={(e) => updateCardConfig('priority', card.id, { color: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Text Color
                          </label>
                          <input
                            type="color"
                            value={card.textColor}
                            onChange={(e) => updateCardConfig('priority', card.id, { textColor: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                      </div>

                      {/* Description */}
                      <div style={{ marginBottom: '12px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Description
                        </label>
                        <input
                          type="text"
                          placeholder="Card description"
                          value={card.description}
                          onChange={(e) => updateCardConfig('priority', card.id, { description: e.target.value })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                      </div>

                      {/* Date Filter Configuration */}
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Date Filter
                        </label>
                        <select
                          value={card.dateFilter || 'all'}
                          onChange={(e) => updateCardConfig('priority', card.id, {
                            dateFilter: e.target.value,
                            customDateFrom: e.target.value !== 'custom' ? '' : card.customDateFrom,
                            customDateTo: e.target.value !== 'custom' ? '' : card.customDateTo
                          })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        >
                          <option value="all">All Time</option>
                          <option value="today">Today</option>
                          <option value="yesterday">Yesterday</option>
                          <option value="last7days">Last 7 Days</option>
                          <option value="last30days">Last 30 Days</option>
                          <option value="last90days">Last 90 Days</option>
                          <option value="custom">Custom Range</option>
                        </select>

                        {/* Custom Date Range */}
                        {card.dateFilter === 'custom' && (
                          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginTop: '8px' }}>
                            <div>
                              <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '2px' }}>
                                From Date
                              </label>
                              <input
                                type="date"
                                value={card.customDateFrom || ''}
                                onChange={(e) => updateCardConfig('priority', card.id, { customDateFrom: e.target.value })}
                                style={{
                                  width: '100%',
                                  padding: '4px 6px',
                                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                                  borderRadius: '4px',
                                  backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  fontSize: '11px'
                                }}
                              />
                            </div>
                            <div>
                              <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '2px' }}>
                                To Date
                              </label>
                              <input
                                type="date"
                                value={card.customDateTo || ''}
                                onChange={(e) => updateCardConfig('priority', card.id, { customDateTo: e.target.value })}
                                style={{
                                  width: '100%',
                                  padding: '4px 6px',
                                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                                  borderRadius: '4px',
                                  backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  fontSize: '11px'
                                }}
                              />
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Assignee Filter Configuration */}
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Assignee Filter
                        </label>
                        <select
                          value={card.assigneeFilter || 'all'}
                          onChange={(e) => updateCardConfig('priority', card.id, {
                            assigneeFilter: e.target.value,
                            selectedAssignees: e.target.value !== 'specific' ? [] : card.selectedAssignees
                          })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        >
                          <option value="all">All Assignees</option>
                          <option value="unassigned">Unassigned Only</option>
                          <option value="specific">Specific Assignees</option>
                        </select>

                        {/* Specific Assignees Selection */}
                        {card.assigneeFilter === 'specific' && (
                          <div style={{ marginTop: '8px' }}>
                            <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                              Select Assignees
                            </label>
                            <div style={{
                              maxHeight: '120px',
                              overflowY: 'auto',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              padding: '4px'
                            }}>
                              {getAllAssignees().map(assignee => (
                                <label key={assignee.key} style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  padding: '4px 8px',
                                  cursor: 'pointer',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  borderRadius: '2px'
                                }}>
                                  <input
                                    type="checkbox"
                                    checked={(card.selectedAssignees || []).includes(assignee.displayName)}
                                    onChange={(e) => {
                                      const currentAssignees = card.selectedAssignees || [];
                                      const newAssignees = e.target.checked
                                        ? [...currentAssignees, assignee.displayName]
                                        : currentAssignees.filter(name => name !== assignee.displayName);
                                      updateCardConfig('priority', card.id, { selectedAssignees: newAssignees });
                                    }}
                                    style={{ marginRight: '8px', transform: 'scale(0.9)' }}
                                  />
                                  <div>
                                    <div style={{ fontWeight: '500' }}>{assignee.displayName}</div>
                                    {assignee.emailAddress && (
                                      <div style={{ fontSize: '10px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                                        {assignee.emailAddress}
                                      </div>
                                    )}
                                  </div>
                                </label>
                              ))}
                              {getAllAssignees().length === 0 && (
                                <div style={{
                                  padding: '8px',
                                  textAlign: 'center',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#9ca3af' : '#64748b'
                                }}>
                                  No assignees found
                                </div>
                              )}
                            </div>
                            {(card.selectedAssignees || []).length > 0 && (
                              <div style={{
                                marginTop: '4px',
                                fontSize: '10px',
                                color: theme === 'dark' ? '#9ca3af' : '#64748b'
                              }}>
                                Selected: {(card.selectedAssignees || []).join(', ')}
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Jira Priority Mapping */}
                      <div>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Jira Priorities (comma-separated)
                        </label>
                        <input
                          type="text"
                          placeholder="Urgent, High, Critical"
                          value={card.jiraPriorities.join(', ')}
                          onChange={(e) => updateJiraMapping('priority', card.id, 'priorities', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                        <div style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', marginTop: '4px' }}>
                          Available: {getAvailableJiraValues('priority').join(', ')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              )}

              {/* Organization Cards Configuration */}
              {activeConfigTab === 'organization' && (
              <div style={{ marginBottom: '32px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                  <h3 style={{
                    margin: 0,
                    fontSize: '18px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    🏢 Organization Cards
                  </h3>
                  <button
                    onClick={() => addNewCard('organization')}
                    className="btn"
                    style={{
                      backgroundColor: '#10b981',
                      color: 'white',
                      padding: '6px 12px',
                      fontSize: '12px'
                    }}
                  >
                    + Add Card
                  </button>
                </div>
                <div style={{
                  display: 'grid',
                  gap: '16px',
                  maxHeight: '60vh',
                  overflowY: 'auto',
                  paddingRight: '8px'
                }}>
                  {config.organizationCards.map((card, index) => (
                    <div key={card.id} style={{
                      padding: '16px',
                      backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
                      borderRadius: '8px',
                      border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`
                    }}>
                      {/* Card Header */}
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
                        <input
                          type="checkbox"
                          checked={card.visible}
                          onChange={(e) => updateCardConfig('organization', card.id, { visible: e.target.checked })}
                          style={{ transform: 'scale(1.2)' }}
                        />
                        <input
                          type="text"
                          placeholder="Card Title"
                          value={card.title}
                          onChange={(e) => updateCardConfig('organization', card.id, { title: e.target.value })}
                          style={{
                            flex: 1,
                            padding: '8px 12px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '6px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                          }}
                        />
                        <div style={{ display: 'flex', gap: '4px' }}>
                          <button
                            onClick={() => moveCard('organization', card.id, 'up')}
                            disabled={index === 0}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === 0 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === 0 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↑
                          </button>
                          <button
                            onClick={() => moveCard('organization', card.id, 'down')}
                            disabled={index === config.organizationCards.length - 1}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: index === config.organizationCards.length - 1 ? '#9ca3af' : '#6b7280',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: index === config.organizationCards.length - 1 ? 'not-allowed' : 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ↓
                          </button>
                          <button
                            onClick={() => deleteCard('organization', card.id)}
                            style={{
                              padding: '4px 8px',
                              backgroundColor: '#ef4444',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer',
                              fontSize: '12px'
                            }}
                          >
                            ✕
                          </button>
                        </div>
                      </div>

                      {/* Card Properties */}
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '8px', marginBottom: '12px' }}>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Icon
                          </label>
                          <input
                            type="text"
                            placeholder="🏢"
                            value={card.icon}
                            onChange={(e) => updateCardConfig('organization', card.id, { icon: e.target.value })}
                            style={{
                              width: '100%',
                              padding: '6px 8px',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                              fontSize: '14px'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Background Color
                          </label>
                          <input
                            type="color"
                            value={card.color}
                            onChange={(e) => updateCardConfig('organization', card.id, { color: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                        <div>
                          <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                            Text Color
                          </label>
                          <input
                            type="color"
                            value={card.textColor}
                            onChange={(e) => updateCardConfig('organization', card.id, { textColor: e.target.value })}
                            style={{
                              width: '100%',
                              height: '32px',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          />
                        </div>
                      </div>

                      {/* Description */}
                      <div style={{ marginBottom: '12px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Description
                        </label>
                        <input
                          type="text"
                          placeholder="Card description"
                          value={card.description}
                          onChange={(e) => updateCardConfig('organization', card.id, { description: e.target.value })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                      </div>

                      {/* Assignee Filter Configuration */}
                      <div style={{ marginBottom: '16px' }}>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Assignee Filter
                        </label>
                        <select
                          value={card.assigneeFilter || 'all'}
                          onChange={(e) => updateCardConfig('organization', card.id, {
                            assigneeFilter: e.target.value,
                            selectedAssignees: e.target.value !== 'specific' ? [] : card.selectedAssignees
                          })}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        >
                          <option value="all">All Assignees</option>
                          <option value="unassigned">Unassigned Only</option>
                          <option value="specific">Specific Assignees</option>
                        </select>

                        {/* Specific Assignees Selection */}
                        {card.assigneeFilter === 'specific' && (
                          <div style={{ marginTop: '8px' }}>
                            <label style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                              Select Assignees
                            </label>
                            <div style={{
                              maxHeight: '120px',
                              overflowY: 'auto',
                              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                              borderRadius: '4px',
                              backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                              padding: '4px'
                            }}>
                              {getAllAssignees().map(assignee => (
                                <label key={assignee.key} style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  padding: '4px 8px',
                                  cursor: 'pointer',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                                  borderRadius: '2px'
                                }}>
                                  <input
                                    type="checkbox"
                                    checked={(card.selectedAssignees || []).includes(assignee.displayName)}
                                    onChange={(e) => {
                                      const currentAssignees = card.selectedAssignees || [];
                                      const newAssignees = e.target.checked
                                        ? [...currentAssignees, assignee.displayName]
                                        : currentAssignees.filter(name => name !== assignee.displayName);
                                      updateCardConfig('organization', card.id, { selectedAssignees: newAssignees });
                                    }}
                                    style={{ marginRight: '8px', transform: 'scale(0.9)' }}
                                  />
                                  <div>
                                    <div style={{ fontWeight: '500' }}>{assignee.displayName}</div>
                                    {assignee.emailAddress && (
                                      <div style={{ fontSize: '10px', color: theme === 'dark' ? '#9ca3af' : '#64748b' }}>
                                        {assignee.emailAddress}
                                      </div>
                                    )}
                                  </div>
                                </label>
                              ))}
                              {getAllAssignees().length === 0 && (
                                <div style={{
                                  padding: '8px',
                                  textAlign: 'center',
                                  fontSize: '11px',
                                  color: theme === 'dark' ? '#9ca3af' : '#64748b'
                                }}>
                                  No assignees found
                                </div>
                              )}
                            </div>
                            {(card.selectedAssignees || []).length > 0 && (
                              <div style={{
                                marginTop: '4px',
                                fontSize: '10px',
                                color: theme === 'dark' ? '#9ca3af' : '#64748b'
                              }}>
                                Selected: {(card.selectedAssignees || []).join(', ')}
                              </div>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Organization Names Mapping */}
                      <div>
                        <label style={{ fontSize: '12px', color: theme === 'dark' ? '#9ca3af' : '#64748b', display: 'block', marginBottom: '4px' }}>
                          Organization Names (comma-separated)
                        </label>
                        <input
                          type="text"
                          placeholder="Company A, Organization B, Department C"
                          value={card.organizationNames.join(', ')}
                          onChange={(e) => updateJiraMapping('organization', card.id, 'organizations', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                          style={{
                            width: '100%',
                            padding: '6px 8px',
                            border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                            borderRadius: '4px',
                            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                            fontSize: '12px'
                          }}
                        />
                        <div style={{ fontSize: '11px', color: theme === 'dark' ? '#9ca3af' : '#64748b', marginTop: '4px' }}>
                          Available: {getAvailableJiraValues('organization').join(', ')}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              )}

              {/* General Settings */}
              {activeConfigTab === 'general' && (
                <div style={{ marginBottom: '32px' }}>
                  <h3 style={{
                    margin: '0 0 16px 0',
                    fontSize: '18px',
                    fontWeight: '600',
                    color: theme === 'dark' ? '#e5e7eb' : '#1e293b'
                  }}>
                    ⚙️ General Settings
                  </h3>

                  {/* Theme Settings */}
                  <div style={{ marginBottom: '24px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: theme === 'dark' ? '#e5e7eb' : '#374151',
                      marginBottom: '8px'
                    }}>
                      🎨 Theme
                    </label>
                    <select
                      value={theme}
                      onChange={(e) => setTheme(e.target.value)}
                      style={{
                        width: '200px',
                        padding: '8px 12px',
                        border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                        borderRadius: '6px',
                        backgroundColor: theme === 'dark' ? '#374151' : 'white',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                        fontSize: '14px'
                      }}
                    >
                      <option value="light">☀️ Light</option>
                      <option value="dark">🌙 Dark</option>
                    </select>
                  </div>

                  {/* Refresh Settings */}
                  <div style={{ marginBottom: '24px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: theme === 'dark' ? '#e5e7eb' : '#374151',
                      marginBottom: '8px'
                    }}>
                      Auto Refresh Interval
                    </label>
                    <p style={{
                      fontSize: '12px',
                      color: theme === 'dark' ? '#9ca3af' : '#64748b',
                      margin: '0 0 8px 0'
                    }}>
                      Dashboard refreshes every 5 seconds, server syncs every 10 seconds
                    </p>
                  </div>

                  {/* Performance Settings */}
                  <div style={{ marginBottom: '24px' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: theme === 'dark' ? '#e5e7eb' : '#374151',
                      marginBottom: '8px'
                    }}>
                      ⚡ Performance
                    </label>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <input
                        type="checkbox"
                        id="showLatestFirstConfig"
                        checked={showLatestFirst}
                        onChange={(e) => setShowLatestFirst(e.target.checked)}
                      />
                      <label htmlFor="showLatestFirstConfig" style={{
                        fontSize: '14px',
                        color: theme === 'dark' ? '#e5e7eb' : '#374151'
                      }}>
                        📅 Show latest tickets first by default
                      </label>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '12px',
                justifyContent: 'flex-end',
                borderTop: `1px solid ${theme === 'dark' ? '#374151' : '#e2e8f0'}`,
                paddingTop: '20px'
              }}>
                <button
                  onClick={loadConfig}
                  className="btn btn-secondary"
                >
                  📂 Load Saved
                </button>
                <button
                  onClick={saveConfig}
                  className="btn btn-primary"
                >
                  💾 Save Configuration
                </button>
                <button
                  onClick={() => setShowConfig(false)}
                  className="btn"
                  style={{
                    backgroundColor: '#6b7280',
                    color: 'white'
                  }}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default NewModernDashboard;
