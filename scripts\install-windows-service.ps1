# Jira Dashboard Windows Service Installer
# This script installs the Jira Dashboard as a Windows Service for maximum reliability

param(
    [switch]$Install,
    [switch]$Uninstall,
    [switch]$Status,
    [switch]$Start,
    [switch]$Stop
)

# Require administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir
$serviceName = "JiraDashboardService"
$serviceDisplayName = "Jira Dashboard Service"
$serviceDescription = "Jira Dashboard Production Service - Auto-restart and monitoring"

# Path to Node.js and the service script
$nodeExe = (Get-Command node).Source
$serviceScript = Join-Path $projectDir "scripts\service-wrapper.js"

function Install-Service {
    Write-Host "Installing Jira Dashboard as Windows Service..." -ForegroundColor Green
    
    # Create service wrapper script
    $wrapperContent = @"
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const projectDir = path.dirname(__dirname);
const logFile = path.join(projectDir, 'logs', 'service.log');

// Ensure logs directory exists
const logDir = path.dirname(logFile);
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[`${timestamp}`] `${message}`\n`;
    console.log(logMessage.trim());
    fs.appendFileSync(logFile, logMessage);
}

function startDashboard() {
    log('🚀 Starting Jira Dashboard Service');
    
    const child = spawn('npm', ['run', 'production'], {
        cwd: projectDir,
        stdio: ['ignore', 'pipe', 'pipe'],
        detached: false
    });
    
    child.stdout.on('data', (data) => {
        log(`STDOUT: `${data.toString().trim()}`);
    });
    
    child.stderr.on('data', (data) => {
        log(`STDERR: `${data.toString().trim()}`);
    });
    
    child.on('close', (code) => {
        log(`❌ Dashboard process exited with code `${code}`);
        if (code !== 0) {
            log('🔄 Restarting in 10 seconds...');
            setTimeout(startDashboard, 10000);
        }
    });
    
    child.on('error', (error) => {
        log(`❌ Dashboard process error: `${error.message}`);
        log('🔄 Restarting in 10 seconds...');
        setTimeout(startDashboard, 10000);
    });
    
    return child;
}

// Handle service shutdown
process.on('SIGTERM', () => {
    log('📴 Service shutdown requested');
    process.exit(0);
});

process.on('SIGINT', () => {
    log('📴 Service interrupted');
    process.exit(0);
});

// Start the dashboard
log('🎯 Jira Dashboard Service starting...');
startDashboard();
"@
    
    # Write the service wrapper
    $wrapperContent | Out-File -FilePath $serviceScript -Encoding UTF8
    Write-Host "✅ Service wrapper created" -ForegroundColor Green
    
    # Install the service using sc.exe
    try {
        $servicePath = "`"$nodeExe`" `"$serviceScript`""
        
        # Create the service
        $result = & sc.exe create $serviceName binPath= $servicePath DisplayName= $serviceDisplayName start= auto
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Windows Service created successfully" -ForegroundColor Green
        } else {
            throw "Failed to create service: $result"
        }
        
        # Set service description
        & sc.exe description $serviceName $serviceDescription
        
        # Configure service recovery options
        & sc.exe failure $serviceName reset= 86400 actions= restart/5000/restart/10000/restart/30000
        
        Write-Host "✅ Service recovery options configured" -ForegroundColor Green
        
        # Start the service
        Start-Service -Name $serviceName
        Write-Host "✅ Service started successfully" -ForegroundColor Green
        
    }
    catch {
        Write-Host "❌ Failed to install service: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    Write-Host "`n🎉 Jira Dashboard Service installed successfully!" -ForegroundColor Green
    Write-Host "Service Name: $serviceName" -ForegroundColor Yellow
    Write-Host "Display Name: $serviceDisplayName" -ForegroundColor Yellow
    Write-Host "Status: Running" -ForegroundColor Yellow
    Write-Host "`nThe dashboard will now start automatically with Windows!" -ForegroundColor Green
    
    return $true
}

function Uninstall-Service {
    Write-Host "Uninstalling Jira Dashboard Service..." -ForegroundColor Yellow
    
    try {
        # Stop the service if running
        Stop-Service -Name $serviceName -Force -ErrorAction SilentlyContinue
        Write-Host "✅ Service stopped" -ForegroundColor Green
        
        # Delete the service
        & sc.exe delete $serviceName
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Service deleted successfully" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Service may not have existed" -ForegroundColor Yellow
        }
        
        # Remove service wrapper script
        if (Test-Path $serviceScript) {
            Remove-Item $serviceScript -Force
            Write-Host "✅ Service wrapper removed" -ForegroundColor Green
        }
        
    }
    catch {
        Write-Host "❌ Failed to uninstall service: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    Write-Host "`n✅ Jira Dashboard Service uninstalled successfully!" -ForegroundColor Green
    return $true
}

function Get-ServiceStatus {
    Write-Host "Jira Dashboard Service Status:" -ForegroundColor Cyan
    
    try {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        if ($service) {
            Write-Host "✅ Service exists: $($service.DisplayName)" -ForegroundColor Green
            Write-Host "📊 Status: $($service.Status)" -ForegroundColor $(if ($service.Status -eq 'Running') { 'Green' } else { 'Red' })
            Write-Host "🔧 Start Type: $($service.StartType)" -ForegroundColor Yellow
            
            # Check if dashboard is responding
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
                if ($response.StatusCode -eq 200) {
                    Write-Host "🌐 Dashboard: RESPONDING" -ForegroundColor Green
                } else {
                    Write-Host "🌐 Dashboard: NOT RESPONDING" -ForegroundColor Red
                }
            }
            catch {
                Write-Host "🌐 Dashboard: NOT RESPONDING" -ForegroundColor Red
            }
            
        } else {
            Write-Host "❌ Service not installed" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error checking service: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # Check log file
    $logFile = Join-Path $projectDir "logs\service.log"
    if (Test-Path $logFile) {
        $logSize = (Get-Item $logFile).Length
        Write-Host "📝 Log file: EXISTS ($([math]::Round($logSize/1KB, 2)) KB)" -ForegroundColor Yellow
        Write-Host "📁 Log path: $logFile" -ForegroundColor Gray
    } else {
        Write-Host "📝 Log file: NOT FOUND" -ForegroundColor Red
    }
}

function Start-DashboardService {
    try {
        Start-Service -Name $serviceName
        Write-Host "✅ Service started successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to start service: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Stop-DashboardService {
    try {
        Stop-Service -Name $serviceName -Force
        Write-Host "✅ Service stopped successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to stop service: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
if ($Install) {
    Install-Service
}
elseif ($Uninstall) {
    Uninstall-Service
}
elseif ($Status) {
    Get-ServiceStatus
}
elseif ($Start) {
    Start-DashboardService
}
elseif ($Stop) {
    Stop-DashboardService
}
else {
    Write-Host "Jira Dashboard Windows Service Manager" -ForegroundColor Cyan
    Write-Host "=====================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\install-windows-service.ps1 -Install    # Install as Windows Service"
    Write-Host "  .\install-windows-service.ps1 -Uninstall  # Remove Windows Service"
    Write-Host "  .\install-windows-service.ps1 -Status     # Check service status"
    Write-Host "  .\install-windows-service.ps1 -Start      # Start the service"
    Write-Host "  .\install-windows-service.ps1 -Stop       # Stop the service"
    Write-Host ""
    Write-Host "Note: This script must be run as Administrator" -ForegroundColor Red
    Write-Host ""
    Write-Host "Windows Service provides:" -ForegroundColor Green
    Write-Host "  ✅ Automatic startup with Windows"
    Write-Host "  ✅ Auto-restart on crashes"
    Write-Host "  ✅ Runs without user login"
    Write-Host "  ✅ Better reliability than scheduled tasks"
    Write-Host "  ✅ Integrated Windows service management"
}
