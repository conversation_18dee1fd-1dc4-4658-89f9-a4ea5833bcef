import axios from 'axios';
import {
  loadDashboardConfig,
  mapJiraStatusToCard,
  mapJiraPriorityToCard,
  getVisibleStatusCards,
  getVisiblePriorityCards
} from '../config/enhancedDashboardConfig.js';

class JiraApiService {
  constructor() {
    // Use proxy server instead of direct Jira API calls
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 60000, // Increased timeout
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    // Add request delay to prevent overwhelming the server
    this.requestDelay = 200; // 200ms between requests
    this.lastRequestTime = 0;
  }

  // Add delay between requests to prevent resource exhaustion
  async delayedRequest(requestFn) {
    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.requestDelay) {
      await new Promise(resolve => setTimeout(resolve, this.requestDelay - timeSinceLastRequest));
    }

    this.lastRequestTime = Date.now();
    return await requestFn();
  }

  // Get tickets by status - Enhanced to show ALL tickets
  async getTicketsByStatus() {
    try {
      // Get ALL tickets count first with delay
      const totalResponse = await this.delayedRequest(() =>
        this.api.get('/jira/search', {
          params: {
            jql: 'ORDER BY created DESC',
            fields: 'status',
            maxResults: 0
          }
        })
      );

      // Get a large sample to calculate accurate status distribution
      const sampleResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'status,priority,created,assignee,project',
          maxResults: 15000 // Increased sample size for better accuracy
        }
      });

      const tickets = sampleResponse.data.issues;
      const statusCounts = {};

      // Initialize all configured status cards with 0
      const currentConfig = loadDashboardConfig();
      getVisibleStatusCards(currentConfig).forEach(card => {
        statusCounts[card.id] = 0;
      });

      // Count tickets by mapped status
      tickets.forEach(ticket => {
        const jiraStatus = ticket.fields.status.name;
        const mappedStatus = mapJiraStatusToCard(jiraStatus);
        if (mappedStatus) {
          statusCounts[mappedStatus]++;
        }
      });

      // Calculate proportional counts based on total tickets
      const totalTickets = totalResponse.data.total || 0;
      const sampleSize = tickets.length;
      const scaleFactor = totalTickets / sampleSize;

      // Scale up the counts proportionally to represent ALL tickets
      const scaledStatusCounts = {};
      getVisibleStatusCards(currentConfig).forEach(card => {
        const originalCount = statusCounts[card.id] || 0;
        scaledStatusCounts[card.id] = Math.round(originalCount * scaleFactor);
      });

      // Return counts mapped to card titles
      const result = {};
      getVisibleStatusCards(currentConfig).forEach(card => {
        result[card.title] = scaledStatusCounts[card.id];
      });

      return {
        ...result,
        _metadata: {
          totalTickets,
          sampleSize,
          scaleFactor: scaleFactor.toFixed(2)
        }
      };
    } catch (error) {
      console.error('Error fetching tickets by status:', error);
      throw error;
    }
  }

  // Get tickets by priority - Enhanced to show ALL tickets
  async getTicketsByPriority() {
    try {
      // Get ALL tickets count first
      const totalResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'priority',
          maxResults: 0
        }
      });

      // Get a large sample to calculate accurate priority distribution
      const sampleResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'priority,created',
          maxResults: 15000 // Increased sample size for better accuracy
        }
      });

      const tickets = sampleResponse.data.issues;
      const priorityCounts = {};

      // Initialize all configured priority cards with 0
      const currentConfig = loadDashboardConfig();
      getVisiblePriorityCards(currentConfig).forEach(card => {
        priorityCounts[card.id] = 0;
      });

      // Count tickets by mapped priority
      tickets.forEach(ticket => {
        const jiraPriority = ticket.fields.priority?.name;
        if (jiraPriority) {
          const mappedPriority = mapJiraPriorityToCard(jiraPriority);
          if (mappedPriority) {
            priorityCounts[mappedPriority]++;
          }
        }
      });

      // Calculate proportional counts based on total tickets
      const totalTickets = totalResponse.data.total || 0;
      const sampleSize = tickets.length;
      const scaleFactor = totalTickets / sampleSize;

      // Scale up the counts proportionally to represent ALL tickets
      const scaledPriorityCounts = {};
      getVisiblePriorityCards(currentConfig).forEach(card => {
        const originalCount = priorityCounts[card.id] || 0;
        scaledPriorityCounts[card.id] = Math.round(originalCount * scaleFactor);
      });

      // Return counts mapped to card titles
      const result = {};
      getVisiblePriorityCards(currentConfig).forEach(card => {
        result[card.title] = scaledPriorityCounts[card.id];
      });

      return {
        ...result,
        _metadata: {
          totalTickets,
          sampleSize,
          scaleFactor: scaleFactor.toFixed(2)
        }
      };
    } catch (error) {
      console.error('Error fetching tickets by priority:', error);
      throw error;
    }
  }

  // Get tickets by organization (configurable grouping)
  async getTicketsByOrganization() {
    try {
      const currentConfig = loadDashboardConfig();
      const config = currentConfig.charts?.projectChart || { title: 'Tickets by Project' };
      const jql = 'project in (STORE, MPDD) ORDER BY created DESC';

      let fields = 'project,created';
      if (config.groupBy === 'assignee') {
        fields += ',assignee';
      } else if (config.groupBy === 'component') {
        fields += ',components';
      }

      const response = await this.api.get('/jira/search', {
        params: {
          jql,
          fields,
          maxResults: 5000
        }
      });

      const tickets = response.data.issues;
      const counts = {};

      tickets.forEach(ticket => {
        let groupKey;

        switch (config.groupBy) {
          case 'assignee':
            groupKey = ticket.fields.assignee?.displayName || 'Unassigned';
            break;
          case 'component':
            groupKey = ticket.fields.components?.length > 0
              ? ticket.fields.components[0].name
              : 'No Component';
            break;
          case 'project':
          default:
            groupKey = ticket.fields.project.name || ticket.fields.project.key;
            break;
        }

        counts[groupKey] = (counts[groupKey] || 0) + 1;
      });

      // Sort by count and return top items
      const sortedItems = Object.entries(counts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, config.maxItems);

      return sortedItems.map(([name, count]) => ({ name, count }));
    } catch (error) {
      console.error('Error fetching tickets by organization:', error);
      throw error;
    }
  }

  // Get monthly ticket trends
  async getMonthlyTrends() {
    try {
      const currentConfig = loadDashboardConfig();
      const config = currentConfig.charts?.trendsChart || {
        title: 'Monthly Trends',
        months: 6
      };

      // Generate months data
      const months = [];
      const currentDate = new Date();
      for (let i = config.months - 1; i >= 0; i--) {
        const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
        months.push({
          name: date.toLocaleDateString('en-US', { month: 'long' }),
          num: date.getMonth() + 1,
          year: date.getFullYear()
        });
      }
      const trends = [];

      for (const month of months) {
        const startDate = `${config.currentYear}-${month.num.toString().padStart(2, '0')}-01`;
        let endDate;

        if (month.num === 2) {
          // February - check for leap year
          const isLeapYear = (config.currentYear % 4 === 0 && config.currentYear % 100 !== 0) || (config.currentYear % 400 === 0);
          endDate = `${config.currentYear}-02-${isLeapYear ? '29' : '28'}`;
        } else if ([4, 6, 9, 11].includes(month.num)) {
          // April, June, September, November have 30 days
          endDate = `${config.currentYear}-${month.num.toString().padStart(2, '0')}-30`;
        } else {
          // January, March, May, July, August, October, December have 31 days
          endDate = `${config.currentYear}-${month.num.toString().padStart(2, '0')}-31`;
        }

        const jql = `project in (STORE, MPDD) AND created >= "${startDate}" AND created <= "${endDate}"`;

        const response = await this.api.get('/jira/search', {
          params: {
            jql,
            fields: 'created',
            maxResults: 0 // We only need the total count
          }
        });

        trends.push({
          month: month.name,
          count: response.data.total
        });
      }

      return trends;
    } catch (error) {
      console.error('Error fetching monthly trends:', error);
      throw error;
    }
  }

  // Get tickets for table view
  async getTicketsForTable(page = 0, pageSize = 50, filters = {}) {
    try {
      let conditions = [];

      // Add filters if provided
      if (filters.project) {
        conditions.push(`project = "${filters.project}"`);
      }
      if (filters.status) {
        conditions.push(`status = "${filters.status}"`);
      }
      if (filters.priority) {
        conditions.push(`priority = "${filters.priority}"`);
      }
      if (filters.assignee) {
        conditions.push(`assignee = "${filters.assignee}"`);
      }

      // Build JQL with proper syntax - include all projects by default
      let jql;
      if (conditions.length > 0) {
        jql = conditions.join(' AND ') + ' ORDER BY created DESC';
      } else {
        jql = 'ORDER BY created DESC'; // Show ALL tickets from ALL projects
      }

      const response = await this.api.get('/jira/search', {
        params: {
          jql,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description',
          maxResults: pageSize,
          startAt: page * pageSize
        }
      });

      const tickets = response.data.issues.map(ticket => ({
        key: ticket.key,
        summary: ticket.fields.summary,
        status: ticket.fields.status.name,
        priority: ticket.fields.priority?.name || 'No Priority',
        assignee: ticket.fields.assignee?.displayName || 'Unassigned',
        project: ticket.fields.project.name,
        projectKey: ticket.fields.project.key,
        created: new Date(ticket.fields.created).toLocaleDateString(),
        updated: new Date(ticket.fields.updated).toLocaleDateString(),
        description: ticket.fields.description ?
          (typeof ticket.fields.description === 'string' ?
            ticket.fields.description.substring(0, 100) + '...' :
            'Description available') :
          'No description'
      }));

      return {
        tickets,
        total: response.data.total,
        page,
        pageSize,
        totalPages: Math.ceil(response.data.total / pageSize)
      };
    } catch (error) {
      console.error('Error fetching tickets for table:', error);
      throw error;
    }
  }

  // Get all dashboard data
  async getDashboardData() {
    try {
      const [statusData, priorityData, orgData, trendsData] = await Promise.all([
        this.getTicketsByStatus(),
        this.getTicketsByPriority(),
        this.getTicketsByOrganization(),
        this.getMonthlyTrends()
      ]);

      return {
        status: statusData,
        priority: priorityData,
        organizations: orgData,
        trends: trendsData
      };
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      throw error;
    }
  }

  // Get enhanced dashboard statistics
  async getDashboardStats() {
    try {
      // Get total count of ALL tickets
      const totalResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'key',
          maxResults: 0
        }
      });

      // Get recent tickets for activity feed
      const recentResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'key,summary,status,priority,created,assignee',
          maxResults: 10
        }
      });

      // Get tickets created today
      const today = new Date().toISOString().split('T')[0];
      const todayResponse = await this.api.get('/jira/search', {
        params: {
          jql: `created >= "${today}" ORDER BY created DESC`,
          fields: 'key',
          maxResults: 0
        }
      });

      return {
        totalTickets: totalResponse.data.total || 0,
        ticketsToday: todayResponse.data.total || 0,
        recentTickets: recentResponse.data.issues || [],
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  // Get all available projects
  async getProjects() {
    try {
      const response = await this.api.get('/jira/project');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  // Get all available statuses
  async getStatuses() {
    try {
      const response = await this.api.get('/jira/status');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching statuses:', error);
      throw error;
    }
  }

  // Get all available priorities
  async getPriorities() {
    try {
      const response = await this.api.get('/jira/priority');
      return response.data || [];
    } catch (error) {
      console.error('Error fetching priorities:', error);
      throw error;
    }
  }
}

export default new JiraApiService();
