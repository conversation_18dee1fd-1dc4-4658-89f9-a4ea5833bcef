import { WebSocketServer } from 'ws';
import mongoDbService from './mongoDbService.js';

class RealTimeService {
  constructor() {
    this.wss = null;
    this.clients = new Set();
    this.updateInterval = null;
    this.updateIntervalMs = 10000; // 10 seconds
    this.lastUpdateTime = null;
    this.isRunning = false;
  }

  // Initialize WebSocket server
  initialize(server) {
    try {
      console.log('🔄 Initializing Real-Time Service...');
      
      this.wss = new WebSocketServer({ 
        server,
        path: '/ws/dashboard'
      });

      this.wss.on('connection', (ws, req) => {
        console.log('🔌 New WebSocket client connected');
        this.clients.add(ws);

        // Send initial data
        this.sendInitialData(ws);

        // Handle client messages
        ws.on('message', (message) => {
          try {
            const data = JSON.parse(message);
            this.handleClientMessage(ws, data);
          } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
          }
        });

        // Handle client disconnect
        ws.on('close', () => {
          console.log('🔌 WebSocket client disconnected');
          this.clients.delete(ws);
        });

        // Handle errors
        ws.on('error', (error) => {
          console.error('❌ WebSocket error:', error);
          this.clients.delete(ws);
        });
      });

      // Start real-time updates
      this.startRealTimeUpdates();
      this.isRunning = true;

      console.log('✅ Real-Time Service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Real-Time Service:', error);
      return false;
    }
  }

  // Send initial data to new client
  async sendInitialData(ws) {
    try {
      const stats = await this.getDashboardStats();
      const message = {
        type: 'initial_data',
        data: stats,
        timestamp: new Date().toISOString()
      };
      
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      console.error('❌ Error sending initial data:', error);
    }
  }

  // Handle client messages
  handleClientMessage(ws, data) {
    switch (data.type) {
      case 'ping':
        this.sendMessage(ws, { type: 'pong', timestamp: new Date().toISOString() });
        break;
      case 'request_update':
        this.sendDashboardUpdate(ws);
        break;
      case 'subscribe_filters':
        // Store client filter preferences
        ws.filters = data.filters;
        break;
      default:
        console.warn('⚠️ Unknown message type:', data.type);
    }
  }

  // Send message to specific client
  sendMessage(ws, message) {
    try {
      if (ws.readyState === ws.OPEN) {
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      console.error('❌ Error sending WebSocket message:', error);
    }
  }

  // Broadcast message to all clients
  broadcast(message) {
    const messageStr = JSON.stringify(message);
    let sentCount = 0;
    
    this.clients.forEach(ws => {
      try {
        if (ws.readyState === ws.OPEN) {
          ws.send(messageStr);
          sentCount++;
        } else {
          this.clients.delete(ws);
        }
      } catch (error) {
        console.error('❌ Error broadcasting message:', error);
        this.clients.delete(ws);
      }
    });

    if (sentCount > 0) {
      console.log(`📡 Broadcasted update to ${sentCount} clients`);
    }
  }

  // Start real-time updates
  startRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    console.log(`🔄 Starting real-time updates every ${this.updateIntervalMs / 1000} seconds`);
    
    this.updateInterval = setInterval(async () => {
      try {
        await this.checkForUpdates();
      } catch (error) {
        console.error('❌ Error in real-time update:', error);
      }
    }, this.updateIntervalMs);
  }

  // Check for updates and broadcast if found
  async checkForUpdates() {
    try {
      if (this.clients.size === 0) {
        return; // No clients connected
      }

      const currentTime = new Date();
      const checkSince = this.lastUpdateTime || new Date(currentTime.getTime() - this.updateIntervalMs);

      // Check for new/updated tickets
      const newTickets = await this.getRecentTickets(checkSince);
      const stats = await this.getDashboardStats();

      if (newTickets.length > 0 || this.hasStatsChanged(stats)) {
        const updateMessage = {
          type: 'dashboard_update',
          data: {
            newTickets: newTickets,
            stats: stats,
            updateCount: newTickets.length,
            lastUpdate: currentTime.toISOString()
          },
          timestamp: currentTime.toISOString()
        };

        this.broadcast(updateMessage);
        this.lastUpdateTime = currentTime;

        if (newTickets.length > 0) {
          console.log(`🆕 Found ${newTickets.length} new/updated tickets`);
        }
      }
    } catch (error) {
      console.error('❌ Error checking for updates:', error);
    }
  }

  // Get recent tickets
  async getRecentTickets(since) {
    try {
      if (!await mongoDbService.isAvailable()) {
        return [];
      }

      const pipeline = [
        {
          $match: {
            $or: [
              { 'fields.created': { $gte: since.toISOString() } },
              { 'fields.updated': { $gte: since.toISOString() } }
            ]
          }
        },
        {
          $project: {
            key: 1,
            summary: 1,
            status: '$fields.status.name',
            priority: '$fields.priority.name',
            assignee: '$fields.assignee.displayName',
            project: '$fields.project.name',
            created: '$fields.created',
            updated: '$fields.updated'
          }
        },
        { $sort: { 'fields.updated': -1 } },
        { $limit: 100 } // Limit to prevent overwhelming clients
      ];

      const tickets = await mongoDbService.collection.aggregate(pipeline).toArray();
      return tickets;
    } catch (error) {
      console.error('❌ Error getting recent tickets:', error);
      return [];
    }
  }

  // Get dashboard statistics
  async getDashboardStats() {
    try {
      if (!await mongoDbService.isAvailable()) {
        return null;
      }

      const pipeline = [
        {
          $facet: {
            statusCounts: [
              { $group: { _id: '$fields.status.name', count: { $sum: 1 } } }
            ],
            priorityCounts: [
              { $group: { _id: '$fields.priority.name', count: { $sum: 1 } } }
            ],
            totalCount: [
              { $count: 'total' }
            ],
            recentActivity: [
              { $sort: { 'fields.updated': -1 } },
              { $limit: 10 },
              {
                $project: {
                  key: 1,
                  summary: 1,
                  status: '$fields.status.name',
                  updated: '$fields.updated'
                }
              }
            ]
          }
        }
      ];

      const [result] = await mongoDbService.collection.aggregate(pipeline).toArray();
      
      return {
        statusCounts: result.statusCounts.reduce((acc, item) => {
          acc[item._id || 'Unknown'] = item.count;
          return acc;
        }, {}),
        priorityCounts: result.priorityCounts.reduce((acc, item) => {
          acc[item._id || 'Unknown'] = item.count;
          return acc;
        }, {}),
        totalCount: result.totalCount[0]?.total || 0,
        recentActivity: result.recentActivity
      };
    } catch (error) {
      console.error('❌ Error getting dashboard stats:', error);
      return null;
    }
  }

  // Check if stats have changed significantly
  hasStatsChanged(newStats) {
    if (!this.lastStats || !newStats) {
      this.lastStats = newStats;
      return true;
    }

    // Compare total counts
    const oldTotal = this.lastStats.totalCount || 0;
    const newTotal = newStats.totalCount || 0;
    
    if (Math.abs(newTotal - oldTotal) > 0) {
      this.lastStats = newStats;
      return true;
    }

    return false;
  }

  // Send dashboard update to specific client
  async sendDashboardUpdate(ws) {
    try {
      const stats = await this.getDashboardStats();
      const message = {
        type: 'dashboard_update',
        data: { stats },
        timestamp: new Date().toISOString()
      };
      
      this.sendMessage(ws, message);
    } catch (error) {
      console.error('❌ Error sending dashboard update:', error);
    }
  }

  // Stop real-time updates
  stopRealTimeUpdates() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
      console.log('⏹️ Real-time updates stopped');
    }
  }

  // Shutdown the service
  shutdown() {
    console.log('🛑 Shutting down Real-Time Service...');
    
    this.stopRealTimeUpdates();
    
    // Close all client connections
    this.clients.forEach(ws => {
      try {
        ws.close();
      } catch (error) {
        console.error('❌ Error closing WebSocket:', error);
      }
    });
    
    this.clients.clear();
    
    if (this.wss) {
      this.wss.close();
    }
    
    this.isRunning = false;
    console.log('✅ Real-Time Service shutdown complete');
  }

  // Get service status
  getStatus() {
    return {
      isRunning: this.isRunning,
      connectedClients: this.clients.size,
      updateInterval: this.updateIntervalMs,
      lastUpdateTime: this.lastUpdateTime
    };
  }
}

// Create singleton instance
const realTimeService = new RealTimeService();
export default realTimeService;
