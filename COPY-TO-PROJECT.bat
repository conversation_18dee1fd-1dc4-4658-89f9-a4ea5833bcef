@echo off
title Copy Installer to Project Directory

echo =========================================================
echo    Copy Installer to Correct Project Directory
echo =========================================================
echo.

echo This will copy the installer files to the correct project directory.
echo.

REM Find the correct project directory
set "PROJECT_DIR="

REM Check common locations
if exist "C:\Users\<USER>\Documents\augment-projects\Jira dashboard\jira-dashboard\package.json" (
    set "PROJECT_DIR=C:\Users\<USER>\Documents\augment-projects\Jira dashboard\jira-dashboard"
    echo Found project at: %PROJECT_DIR%
    goto :copy_files
)

if exist "C:\Users\<USER>\Documents\jira-dashboard\package.json" (
    set "PROJECT_DIR=C:\Users\<USER>\Documents\jira-dashboard"
    echo Found project at: %PROJECT_DIR%
    goto :copy_files
)

if exist ".\package.json" (
    set "PROJECT_DIR=%CD%"
    echo Found project at: %PROJECT_DIR%
    goto :copy_files
)

echo [ERROR] Could not find the Jira Dashboard project directory!
echo.
echo Please make sure the project exists in one of these locations:
echo   - C:\Users\<USER>\Documents\augment-projects\Jira dashboard\jira-dashboard\
echo   - C:\Users\<USER>\Documents\jira-dashboard\
echo   - Current directory
echo.
pause
exit /b 1

:copy_files
echo.
echo Copying installer files to project directory...
echo.

REM Copy the installer files
copy /Y "INSTALL-EVERYTHING.bat" "%PROJECT_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] INSTALL-EVERYTHING.bat copied
) else (
    echo [ERROR] Failed to copy INSTALL-EVERYTHING.bat
)

copy /Y "simple-auto-install.ps1" "%PROJECT_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] simple-auto-install.ps1 copied
) else (
    echo [ERROR] Failed to copy simple-auto-install.ps1
)

copy /Y "auto-install-everything.ps1" "%PROJECT_DIR%\" >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] auto-install-everything.ps1 copied
) else (
    echo [SKIP] auto-install-everything.ps1 not found
)

echo.
echo [SUCCESS] Installer files copied to project directory!
echo.
echo Now you can run the installer from the correct location:
echo   1. Go to: %PROJECT_DIR%
echo   2. Right-click on INSTALL-EVERYTHING.bat
echo   3. Select "Run as administrator"
echo.

REM Ask if user wants to open the project directory
set /p open_dir="Would you like to open the project directory now? (y/n): "
if /i "%open_dir%"=="y" (
    explorer "%PROJECT_DIR%"
)

echo.
pause
