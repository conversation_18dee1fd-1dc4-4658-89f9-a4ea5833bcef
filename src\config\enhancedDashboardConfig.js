// Enhanced Dashboard Configuration with full customization support
export const DEFAULT_DASHBOARD_CONFIG = {
  title: "Dashboard Incident MCDTech - All Projects",
  subtitle: "Real-time data from ALL Jira projects",
  primaryColor: '#f39c12',
  theme: 'light',
  refreshInterval: 30, // seconds
  defaultPageSize: 25,
  showTicketsTable: true,
  enableRealTimeUpdates: true,
  showMetadata: false,

  // Filter configuration
  filters: {
    year: 'all', // 'all', '2024', '2023', etc.
    assignees: [] // Array of selected assignee names to filter by (empty = show all)
  },

  // Display fields configuration
  displayFields: ['assignee', 'components'], // Additional fields to show in table
  
  // Status cards configuration
  statusCards: [
    {
      id: 'non_traites',
      title: 'Non traités',
      color: '#6c5ce7',
      textColor: 'white',
      jiraStatuses: ['Open', 'To Do', 'New'],
      visible: true
    },
    {
      id: 'en_cours',
      title: 'En cours',
      color: '#00cec9',
      textColor: 'white',
      jiraStatuses: ['In Progress', 'In Review'],
      visible: true
    },
    {
      id: 'en_attente_client',
      title: 'En attente client',
      color: '#74b9ff',
      textColor: 'white',
      jiraStatuses: ['Waiting for customer', 'Pending Customer'],
      visible: true
    },
    {
      id: 'hardware',
      title: 'Hardware',
      color: '#fdcb6e',
      textColor: 'black',
      jiraStatuses: ['Hardware Issue', 'Equipment', 'En attente du fournissuer'],
      visible: true
    },
    {
      id: 'resolu',
      title: 'Résolu',
      color: '#00b894',
      textColor: 'white',
      jiraStatuses: ['Resolved', 'Fixed', 'Done', 'Closed'],
      visible: true
    },
    {
      id: 'europe',
      title: 'Europe',
      color: '#e17055',
      textColor: 'white',
      jiraStatuses: ['Escalated', 'Europe Team'],
      visible: true
    }
  ],

  // Priority cards configuration
  priorityCards: [
    {
      id: 'incident_tres_urgent',
      title: 'Incident Très Urgent',
      color: '#d63031',
      textColor: 'white',
      jiraPriorities: ['Highest', 'Critical', 'Urgent'],
      visible: true
    },
    {
      id: 'incident_urgent',
      title: 'Incident urgent',
      color: '#e17055',
      textColor: 'white',
      jiraPriorities: ['High', 'Moyen'],
      visible: true
    },
    {
      id: 'incident_moyen',
      title: 'Incident moyen',
      color: '#636e72',
      textColor: 'white',
      jiraPriorities: ['Medium', 'Normal', 'Faible', 'Très faible'],
      visible: true
    }
  ],

  // Organization cards configuration
  organizationCards: [
    {
      id: 'store_tickets',
      title: 'STORE',
      color: '#e17055',
      textColor: 'white',
      jiraProjects: ['STORE'],
      visible: true
    },
    {
      id: 'it_tickets',
      title: 'IT Support',
      color: '#0984e3',
      textColor: 'white',
      jiraProjects: ['IT', 'SUPPORT', 'TECH'],
      visible: true
    },
    {
      id: 'operations_tickets',
      title: 'Operations',
      color: '#00b894',
      textColor: 'white',
      jiraProjects: ['OPS', 'OPERATIONS'],
      visible: true
    },
    {
      id: 'maintenance_tickets',
      title: 'Maintenance',
      color: '#fdcb6e',
      textColor: 'black',
      jiraProjects: ['MAINT', 'MAINTENANCE'],
      visible: true
    }
  ],

  // Charts configuration
  charts: {
    projectChart: {
      enabled: true,
      title: 'Tickets by Project',
      type: 'bar'
    },
    trendsChart: {
      enabled: true,
      title: 'Monthly Trends',
      type: 'line',
      months: 6
    }
  },

  // Table configuration
  table: {
    defaultSort: 'created',
    defaultSortOrder: 'desc',
    enableFilters: true,
    enableSearch: true,
    showDescription: true,
    compactMode: false
  }
};

// Load configuration from localStorage or use default
export const loadDashboardConfig = () => {
  try {
    const saved = localStorage.getItem('enhancedDashboardConfig');
    if (saved) {
      const parsed = JSON.parse(saved);
      // Merge with default config to ensure all properties exist
      return { ...DEFAULT_DASHBOARD_CONFIG, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to load dashboard config from localStorage:', error);
  }
  return DEFAULT_DASHBOARD_CONFIG;
};

// Save configuration to localStorage
export const saveDashboardConfig = (config) => {
  try {
    localStorage.setItem('enhancedDashboardConfig', JSON.stringify(config));
    return true;
  } catch (error) {
    console.error('Failed to save dashboard config to localStorage:', error);
    return false;
  }
};

// Reset configuration to default
export const resetDashboardConfig = () => {
  try {
    localStorage.removeItem('enhancedDashboardConfig');
    return DEFAULT_DASHBOARD_CONFIG;
  } catch (error) {
    console.error('Failed to reset dashboard config:', error);
    return DEFAULT_DASHBOARD_CONFIG;
  }
};

// Helper function to map Jira status to dashboard status
export const mapJiraStatusToCard = (jiraStatus, config = null) => {
  const currentConfig = config || loadDashboardConfig();
  for (const card of currentConfig.statusCards) {
    if (card.visible && card.jiraStatuses.some(status => 
      status.toLowerCase() === jiraStatus.toLowerCase()
    )) {
      return card.id;
    }
  }
  return null; // Status not mapped
};

// Helper function to map Jira priority to dashboard priority
export const mapJiraPriorityToCard = (jiraPriority, config = null) => {
  const currentConfig = config || loadDashboardConfig();
  for (const card of currentConfig.priorityCards) {
    if (card.visible && card.jiraPriorities.some(priority => 
      priority.toLowerCase() === jiraPriority.toLowerCase()
    )) {
      return card.id;
    }
  }
  return null; // Priority not mapped
};

// Get visible status cards
export const getVisibleStatusCards = (config = null) => {
  const currentConfig = config || loadDashboardConfig();
  return currentConfig.statusCards.filter(card => card.visible);
};

// Get visible priority cards
export const getVisiblePriorityCards = (config = null) => {
  const currentConfig = config || loadDashboardConfig();
  return currentConfig.priorityCards.filter(card => card.visible);
};

// Get visible organization cards
export const getVisibleOrganizationCards = (config = null) => {
  const currentConfig = config || loadDashboardConfig();
  return currentConfig.organizationCards.filter(card => card.visible);
};

// Export current configuration (dynamic)
export const getCurrentConfig = loadDashboardConfig;

// Validate configuration
export const validateConfig = (config) => {
  const errors = [];
  
  if (!config.title || config.title.trim() === '') {
    errors.push('Dashboard title is required');
  }
  
  if (!config.statusCards || config.statusCards.length === 0) {
    errors.push('At least one status card is required');
  }
  
  if (!config.priorityCards || config.priorityCards.length === 0) {
    errors.push('At least one priority card is required');
  }
  
  // Check for duplicate card IDs
  const statusIds = config.statusCards?.map(card => card.id) || [];
  const priorityIds = config.priorityCards?.map(card => card.id) || [];
  
  if (new Set(statusIds).size !== statusIds.length) {
    errors.push('Duplicate status card IDs found');
  }
  
  if (new Set(priorityIds).size !== priorityIds.length) {
    errors.push('Duplicate priority card IDs found');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Export presets
export const CONFIG_PRESETS = {
  default: DEFAULT_DASHBOARD_CONFIG,
  minimal: {
    ...DEFAULT_DASHBOARD_CONFIG,
    statusCards: [
      {
        id: 'open',
        title: 'Open',
        color: '#2196f3',
        textColor: 'white',
        jiraStatuses: ['Open', 'To Do', 'New'],
        visible: true
      },
      {
        id: 'in_progress',
        title: 'In Progress',
        color: '#ff9800',
        textColor: 'white',
        jiraStatuses: ['In Progress'],
        visible: true
      },
      {
        id: 'done',
        title: 'Done',
        color: '#4caf50',
        textColor: 'white',
        jiraStatuses: ['Done', 'Resolved', 'Closed'],
        visible: true
      }
    ],
    priorityCards: [
      {
        id: 'high',
        title: 'High Priority',
        color: '#f44336',
        textColor: 'white',
        jiraPriorities: ['Highest', 'High', 'Urgent'],
        visible: true
      },
      {
        id: 'normal',
        title: 'Normal Priority',
        color: '#9e9e9e',
        textColor: 'white',
        jiraPriorities: ['Medium', 'Normal', 'Low', 'Lowest'],
        visible: true
      }
    ]
  }
};
