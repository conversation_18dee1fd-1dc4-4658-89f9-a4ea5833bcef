// Incremental Sync Service for Jira Dashboard
// Handles intelligent syncing between Jira API and MongoDB

import mongoDbService from './mongoDbService.js';
import axios from 'axios';

class IncrementalSyncService {
  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 30000,
    });
    this.isInitialized = false;
    this.lastFullSync = null;
    this.syncInProgress = false;
  }

  // Initialize the sync service
  async initialize() {
    try {
      console.log('🔄 Initializing Incremental Sync Service...');
      
      // Connect to MongoDB
      const mongoConnected = await mongoDbService.connect();
      if (!mongoConnected) {
        console.warn('⚠️ MongoDB not available, falling back to API-only mode');
        return false;
      }

      this.isInitialized = true;
      console.log('✅ Incremental Sync Service initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize sync service:', error.message);
      return false;
    }
  }

  // Get all tickets with intelligent syncing
  async getAllTickets() {
    if (!this.isInitialized) {
      console.log('🔄 Sync service not initialized, attempting to initialize...');
      await this.initialize();
    }

    try {
      // If MongoDB is available, use incremental sync
      if (await mongoDbService.isAvailable()) {
        return await this.performIncrementalSync();
      } else {
        // Fallback to direct API call
        console.log('📡 MongoDB unavailable, fetching directly from Jira API...');
        return await this.fetchAllFromApi();
      }
    } catch (error) {
      console.error('❌ Sync operation failed:', error.message);
      throw error;
    }
  }

  // Perform intelligent incremental sync
  async performIncrementalSync() {
    if (this.syncInProgress) {
      console.log('⏳ Sync already in progress, skipping...');
      return null;
    }

    this.syncInProgress = true;

    try {
      console.log('🔄 Starting incremental sync...');

      // Step 1: Load existing tickets from MongoDB
      const existingTickets = await mongoDbService.loadAllTickets();
      const existingCount = existingTickets ? existingTickets.length : 0;
      
      console.log('📊 Found ' + existingCount + ' existing tickets in database');

      // Step 2: Get latest update time from database
      const latestUpdateTime = await mongoDbService.getLatestUpdateTime();
      console.log('📅 Latest update time in database:', latestUpdateTime);

      // Step 3: Determine sync strategy
      // If we have significantly fewer tickets than expected (less than 70,000), do a full sync
      if (existingCount < 70000) {
        console.log('🆕 Database has only ' + existingCount + ' tickets - performing full sync to get all 77,000+ tickets...');
        return await this.performFullSync();
      } else {
        // Incremental sync - fetch only new/updated tickets
        console.log('🔄 Performing incremental sync...');
        return await this.performDeltaSync(latestUpdateTime, existingTickets);
      }

    } finally {
      this.syncInProgress = false;
    }
  }

  // Perform full sync (first time or forced refresh)
  async performFullSync() {
    try {
      console.log('📡 Fetching ALL tickets from Jira API...');
      
      // Get total count first with a query that gets ALL tickets
      const countResponse = await this.api.get('/jira/search', {
        params: {
          jql: 'project is not EMPTY ORDER BY created DESC', // This gets ALL tickets from ALL projects
          maxResults: 0
        }
      });

      const totalTickets = countResponse.data.total;
      console.log('📊 Total tickets in Jira: ' + totalTickets.toLocaleString());

      if (totalTickets === 0) {
        console.warn('⚠️ No tickets found in Jira. Check your JQL query and permissions.');
        return [];
      }

      // Fetch all tickets in batches - Jira API seems to limit to 100 per request
      const allTickets = [];
      const batchSize = 100; // Adjusted to match Jira API actual limit
      const totalBatches = Math.ceil(totalTickets / batchSize);

      console.log('🚀 Starting batch loading (' + totalBatches + ' batches of ' + batchSize + ' tickets each - adjusted for Jira API limits)');

      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;
        console.log('📥 Fetching batch ' + (i + 1) + '/' + totalBatches + ' (tickets ' + (startAt + 1) + '-' + Math.min(startAt + batchSize, totalTickets) + ')');

        const response = await this.api.get('/jira/search', {
          params: {
            jql: 'project is not EMPTY ORDER BY created DESC', // Gets ALL tickets from ALL projects
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: batchSize,
            startAt: startAt
          }
        });

        const tickets = response.data.issues || [];
        const actualReturned = tickets.length;
        const requestedAmount = batchSize;

        console.log('📊 Batch ' + (i + 1) + ' Details:');
        console.log('   - Requested: ' + requestedAmount + ' tickets');
        console.log('   - Actually received: ' + actualReturned + ' tickets');
        console.log('   - Start at: ' + startAt);

        if (actualReturned < requestedAmount && actualReturned > 0) {
          console.log('⚠️ Jira API returned fewer tickets than requested - this is normal for the last batch or due to API limits');
        }

        allTickets.push(...tickets);

        // Save to MongoDB immediately after each batch to avoid memory issues
        if (tickets.length > 0) {
          // For the first batch of a full sync, use force insert to ensure new tickets are inserted
          if (i === 0 && allTickets.length === 0) {
            await mongoDbService.forceInsertTickets(tickets);
            console.log('💾 Force inserted batch ' + (i + 1) + ' to MongoDB (' + tickets.length + ' tickets)');
          } else {
            await mongoDbService.saveTickets(tickets);
            console.log('💾 Saved batch ' + (i + 1) + ' to MongoDB (' + tickets.length + ' tickets)');
          }
        }

        // Progress indicator every 50 batches (since we have more batches now)
        if ((i + 1) % 50 === 0 || i === totalBatches - 1) {
          const percentage = ((i + 1) / totalBatches * 100).toFixed(1);
          console.log('📊 Progress: ' + percentage + '% (' + allTickets.length.toLocaleString() + '/' + totalTickets.toLocaleString() + ' tickets)');
        }
      }

      console.log('✅ Fetched and saved ' + allTickets.length.toLocaleString() + ' tickets to MongoDB');
      this.lastFullSync = new Date();

      return allTickets;

    } catch (error) {
      console.error('❌ Full sync failed:', error.message);
      throw error;
    }
  }

  // Perform delta sync (incremental updates)
  async performDeltaSync(latestUpdateTime, existingTickets) {
    try {
      // Calculate sync window (fetch tickets updated since last sync)
      const syncSince = latestUpdateTime || new Date(Date.now() - 24 * 60 * 60 * 1000); // Default: last 24 hours
      const jqlQuery = 'updated >= "' + this.formatDateForJira(syncSince) + '" ORDER BY updated DESC';
      
      console.log('🔍 Checking for updates since: ' + syncSince.toISOString());
      console.log('🔍 JQL Query: ' + jqlQuery);

      // Get count of potentially updated tickets
      const countResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          maxResults: 0
        }
      });

      const potentialUpdates = countResponse.data.total;
      console.log('📊 Found ' + potentialUpdates + ' potentially updated tickets');

      if (potentialUpdates === 0) {
        console.log('✅ No updates found, using existing database tickets');
        return existingTickets;
      }

      // Fetch updated tickets
      const updatedTickets = [];
      const batchSize = 100;
      const totalBatches = Math.ceil(potentialUpdates / batchSize);

      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;
        console.log('📥 Fetching update batch ' + (i + 1) + '/' + totalBatches);

        const response = await this.api.get('/jira/search', {
          params: {
            jql: jqlQuery,
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: batchSize,
            startAt: startAt
          }
        });

        const tickets = response.data.issues || [];
        updatedTickets.push(...tickets);
      }

      console.log('📥 Fetched ' + updatedTickets.length + ' updated tickets from Jira');

      // Save updates to MongoDB
      if (updatedTickets.length > 0) {
        const saveSuccess = await mongoDbService.saveTickets(updatedTickets);
        if (saveSuccess) {
          console.log('💾 Updated tickets saved to MongoDB');
        }
      }

      // Return combined dataset (existing + updates)
      const allTickets = await mongoDbService.loadAllTickets();
      console.log('✅ Incremental sync completed. Total tickets: ' + allTickets.length.toLocaleString());
      
      return allTickets;

    } catch (error) {
      console.error('❌ Delta sync failed:', error.message);
      // Fallback to existing tickets
      console.log('🔄 Falling back to existing database tickets');
      return existingTickets;
    }
  }

  // Fallback: Fetch directly from API (when MongoDB unavailable)
  async fetchAllFromApi() {
    try {
      console.log('📡 Fetching tickets directly from Jira API (MongoDB unavailable)...');
      
      const response = await this.api.get('/jira/search', {
        params: {
          jql: 'ORDER BY created DESC',
          fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
          maxResults: 1000 // Limited batch when MongoDB unavailable
        }
      });

      const tickets = response.data.issues || [];
      console.log('📥 Fetched ' + tickets.length + ' tickets from API (limited mode)');
      
      return tickets;

    } catch (error) {
      console.error('❌ API fallback failed:', error.message);
      throw error;
    }
  }

  // Force a full resync
  async forceFullSync() {
    console.log('🔄 Forcing complete full resync - clearing database and fetching ALL tickets...');
    this.lastFullSync = null;

    // Clear existing data first
    if (await mongoDbService.isAvailable()) {
      console.log('🗑️ Clearing existing MongoDB data...');
      await mongoDbService.clearAllTickets();
      console.log('✅ MongoDB cleared, starting fresh full sync...');
    }

    return await this.performFullSync();
  }

  // Get comprehensive ticket count from Jira
  async getTotalTicketCount() {
    try {
      const response = await this.api.get('/jira/search', {
        params: {
          jql: 'project is not EMPTY',
          maxResults: 0
        }
      });
      return response.data.total;
    } catch (error) {
      console.error('❌ Failed to get total ticket count:', error.message);
      return 0;
    }
  }

  // Get sync status and statistics
  async getSyncStatus() {
    const mongoHealth = await mongoDbService.getHealthCheck();
    const mongoStats = await mongoDbService.getStats();
    
    return {
      isInitialized: this.isInitialized,
      syncInProgress: this.syncInProgress,
      lastFullSync: this.lastFullSync,
      mongoDb: mongoHealth,
      stats: mongoStats,
      strategy: mongoHealth.available ? 'Incremental Sync (MongoDB)' : 'Direct API (Fallback)'
    };
  }

  // Check for new tickets (real-time updates)
  async checkForNewTickets() {
    if (!this.isInitialized || this.syncInProgress) {
      return null;
    }

    try {
      const latestUpdateTime = await mongoDbService.getLatestUpdateTime();
      if (!latestUpdateTime) {
        return null;
      }

      // Check for tickets updated in the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      const checkSince = latestUpdateTime > fiveMinutesAgo ? latestUpdateTime : fiveMinutesAgo;
      
      const jqlQuery = 'updated >= "' + this.formatDateForJira(checkSince) + '" ORDER BY updated DESC';
      
      const response = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key,summary,status,priority,assignee,project,created,updated',
          maxResults: 50
        }
      });

      const newTickets = response.data.issues || [];
      
      if (newTickets.length > 0) {
        console.log('🆕 Found ' + newTickets.length + ' new/updated tickets');
        await mongoDbService.saveTickets(newTickets);
        return newTickets;
      }

      return null;

    } catch (error) {
      console.warn('⚠️ New ticket check failed:', error.message);
      return null;
    }
  }

  // Format date for Jira API (YYYY-MM-DD HH:mm format)
  formatDateForJira(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
}

// Create singleton instance
const incrementalSyncService = new IncrementalSyncService();
export default incrementalSyncService;
