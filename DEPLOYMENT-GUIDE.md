# 🚀 Jira Dashboard - Multi-PC Deployment Guide

## 📦 Creating Portable Installer

### Step 1: Create Portable Package
```bash
# Run this on your development PC
CREATE-PORTABLE-INSTALLER.bat
```

This creates a complete portable installer package that contains:
- ✅ **Complete source code**
- ✅ **All configuration files**
- ✅ **Installation scripts**
- ✅ **Auto-start setup**
- ✅ **Documentation**
- ✅ **DBSYS logo and assets**

## 🖥️ Deploying to Other PCs

### Method 1: USB Drive Deployment
```
1. Copy "Jira-Dashboard-Portable-Installer" folder to USB drive
2. Go to target PC
3. Copy folder from USB to PC (Desktop, Documents, etc.)
4. Right-click "INSTALL-EVERYTHING.bat"
5. Select "Run as administrator"
6. Wait 5-10 minutes
7. Done! Dashboard starts automatically
```

### Method 2: Network Share Deployment
```
1. Place "Jira-Dashboard-Portable-Installer" on network share
2. On each target PC:
   - Access network share
   - Copy folder locally
   - Run "INSTALL-EVERYTHING.bat" as administrator
```

### Method 3: Remote Desktop Deployment
```
1. Connect to target PC via Remote Desktop
2. Transfer installer folder
3. Run installation remotely
4. Dashboard will be ready when users log in
```

## 🎯 What Gets Installed on Each PC

### Automatic Installation Includes:
- ✅ **Node.js 20.11.0** (if not already installed)
- ✅ **MongoDB 7.0.5** (local installation)
- ✅ **All npm dependencies**
- ✅ **Production build** (optimized)
- ✅ **Windows Service** (auto-start on boot)
- ✅ **Database setup** (users, collections)
- ✅ **Full screen configuration**

### Dashboard Features:
- ✅ **Real-time updates** every 10 seconds
- ✅ **RESTAURANTS chart** (not "Organizations")
- ✅ **Priority cards** (Très Urgent, Urgent, Moyen)
- ✅ **Professional DBSYS logo**
- ✅ **Auto-restart** on crashes
- ✅ **Full screen kiosk mode**

## 📋 System Requirements (Per PC)

### Minimum Requirements:
- **OS**: Windows 10/11
- **RAM**: 4GB minimum (8GB recommended)
- **Storage**: 2GB free space
- **Network**: Internet connection (for initial setup)
- **Access**: Administrator privileges required

### Recommended for Best Performance:
- **OS**: Windows 11
- **RAM**: 8GB or more
- **Storage**: SSD with 5GB free space
- **Network**: Stable internet connection
- **Browser**: Chrome or Edge (latest version)

## 🔧 Installation Process (Per PC)

### Automated Installation Steps:
1. **Pre-check**: Verifies administrator rights
2. **Node.js**: Downloads and installs if needed
3. **Dependencies**: Installs all npm packages
4. **MongoDB**: Downloads, installs, and configures locally
5. **Database**: Creates users and collections
6. **Build**: Creates optimized production build
7. **Service**: Installs Windows Service for auto-start
8. **Test**: Verifies all components working
9. **Launch**: Opens dashboard in full screen

### Installation Time:
- **First PC**: 10-15 minutes (downloads components)
- **Subsequent PCs**: 5-10 minutes (cached downloads)

## 🌐 Network Configuration

### Local Installation (Recommended):
- **URL**: `http://localhost:3000`
- **Database**: Local MongoDB on each PC
- **Security**: No network exposure
- **Performance**: Best (no network latency)

### Centralized Database (Advanced):
If you want all PCs to share the same data:
1. Set up one PC as MongoDB server
2. Configure other PCs to connect to central database
3. Update connection strings in `server.js`

## 🔍 Verification & Testing

### After Installation on Each PC:
```bash
# Check if service is running
setup-auto-start.bat → Option 4

# Test dashboard access
Open: http://localhost:3000

# Verify auto-start
Restart PC and check if dashboard opens automatically
```

### Success Indicators:
- ✅ **Service running** in Task Manager → Services
- ✅ **Dashboard loads** at http://localhost:3000
- ✅ **Full screen mode** works (F11 key)
- ✅ **Data updates** every 10 seconds
- ✅ **Auto-start** after PC restart

## 🛠️ Troubleshooting Common Issues

### Installation Fails:
1. **Check administrator rights**
   ```
   Right-click → "Run as administrator"
   ```

2. **Check internet connection**
   ```
   ping *******
   ```

3. **Disable antivirus temporarily**
   - Add installer folder to exclusions

### Dashboard Won't Load:
1. **Check Windows Firewall**
   - Allow Node.js through firewall

2. **Check port availability**
   ```
   netstat -an | findstr :3000
   ```

3. **Restart service**
   ```
   setup-auto-start.bat → Option 6 → Option 2
   ```

### Auto-Start Not Working:
1. **Verify service installation**
   ```
   services.msc → Look for "Jira Dashboard Service"
   ```

2. **Check startup programs**
   ```
   Task Manager → Startup tab
   ```

3. **Reinstall auto-start**
   ```
   setup-auto-start.bat → Option 6 → Option 2
   ```

## 📊 Management & Maintenance

### Service Management (Per PC):
```bash
# Check status
setup-auto-start.bat → Option 4

# Start service
setup-auto-start.bat → Option 2

# Stop service  
setup-auto-start.bat → Option 6

# Restart service
setup-auto-start.bat → Option 6 → Option 2
```

### Log Files (Per PC):
- **Installation**: `logs\auto-install.log`
- **Production**: `logs\production.log`
- **Service**: `logs\service.log`
- **MongoDB**: `mongodb\logs\mongod.log`

### Updates & Maintenance:
1. **Update source code** on development PC
2. **Create new portable installer**
3. **Deploy to target PCs** (overwrites existing)
4. **Services restart automatically**

## 🎉 Deployment Checklist

### Before Deployment:
- [ ] Test installer on development PC
- [ ] Create portable installer package
- [ ] Verify all files included
- [ ] Test on one target PC first

### During Deployment:
- [ ] Copy installer to each PC
- [ ] Run as administrator
- [ ] Wait for installation to complete
- [ ] Verify dashboard loads
- [ ] Test auto-start (restart PC)

### After Deployment:
- [ ] All PCs show dashboard at startup
- [ ] Full screen mode working
- [ ] Data updates every 10 seconds
- [ ] Services running in Task Manager
- [ ] No error messages in logs

## 🚀 Ready for Enterprise Deployment!

Your Jira Dashboard is now ready for deployment to multiple PCs with:

- 🎯 **Zero-configuration** installation
- 🔄 **Automatic everything** (install, start, restart)
- 🛡️ **Production-grade** reliability
- 📊 **Professional** dashboard display
- 🖥️ **Perfect for** dedicated monitors

**Just run `CREATE-PORTABLE-INSTALLER.bat` and deploy to any Windows PC!** 🎉
