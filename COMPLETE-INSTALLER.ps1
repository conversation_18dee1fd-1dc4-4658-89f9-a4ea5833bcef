# Jira Dashboard - Complete One-Script Installer
# This single script installs and builds everything needed

param([switch]$Force)

# Require administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ Administrator privileges required!" -ForegroundColor Red
    Write-Host "Right-click this script and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Jira Dashboard - Complete Installer" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green

# Get script directory and ensure we're in the right place
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $scriptDir

# Find package.json to ensure we're in the project directory
if (-not (Test-Path "package.json")) {
    Write-Host "❌ package.json not found in current directory!" -ForegroundColor Red
    Write-Host "Please run this script from the Jira Dashboard project directory." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found project directory: $scriptDir" -ForegroundColor Green

# Create logs directory
$logDir = Join-Path $scriptDir "logs"
if (-not (Test-Path $logDir)) { New-Item -ItemType Directory -Path $logDir -Force | Out-Null }

function Write-Step($message) {
    Write-Host ""
    Write-Host "🔄 $message" -ForegroundColor Cyan
    Write-Host "=" * 50 -ForegroundColor Gray
}

function Write-Success($message) { Write-Host "✅ $message" -ForegroundColor Green }
function Write-Error($message) { Write-Host "❌ $message" -ForegroundColor Red }
function Write-Warning($message) { Write-Host "⚠️ $message" -ForegroundColor Yellow }

# STEP 1: Install Node.js
Write-Step "Installing Node.js"
try {
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Success "Node.js already installed: $nodeVersion"
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "Downloading Node.js..." -ForegroundColor Yellow
    $nodeUrl = "https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi"
    $nodeInstaller = "$env:TEMP\nodejs-installer.msi"
    
    Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
    Write-Host "Installing Node.js..." -ForegroundColor Yellow
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", "`"$nodeInstaller`"", "/quiet", "/norestart" -Wait
    
    # Refresh PATH
    $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
    Start-Sleep -Seconds 5
    
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Success "Node.js installed: $nodeVersion"
        Remove-Item $nodeInstaller -Force -ErrorAction SilentlyContinue
    } else {
        Write-Error "Node.js installation failed"
        exit 1
    }
}

# STEP 2: Install Dependencies
Write-Step "Installing Dependencies"
if (Test-Path "node_modules") {
    Write-Host "Cleaning existing node_modules..." -ForegroundColor Yellow
    Remove-Item "node_modules" -Recurse -Force
}

Write-Host "Running npm install..." -ForegroundColor Yellow
$installProcess = Start-Process -FilePath "npm" -ArgumentList "install" -Wait -PassThru -NoNewWindow
if ($installProcess.ExitCode -eq 0) {
    Write-Success "Dependencies installed successfully"
} else {
    Write-Error "Failed to install dependencies"
    exit 1
}

# STEP 3: Install MongoDB
Write-Step "Installing MongoDB"
$mongoDir = Join-Path $scriptDir "mongodb"
$mongoBinDir = Join-Path $mongoDir "bin"
$mongoDataDir = Join-Path $mongoDir "data"
$mongoLogDir = Join-Path $mongoDir "logs"

# Create MongoDB directories
@($mongoDir, $mongoBinDir, $mongoDataDir, $mongoLogDir) | ForEach-Object {
    if (-not (Test-Path $_)) { New-Item -ItemType Directory -Path $_ -Force | Out-Null }
}

# Check if MongoDB already exists
$mongodExe = Join-Path $mongoBinDir "mongod.exe"
if (-not (Test-Path $mongodExe)) {
    Write-Host "Downloading MongoDB..." -ForegroundColor Yellow
    $mongoUrl = "https://fastdl.mongodb.org/windows/mongodb-windows-x86_64-7.0.5.zip"
    $mongoZip = "$env:TEMP\mongodb.zip"
    
    Invoke-WebRequest -Uri $mongoUrl -OutFile $mongoZip -UseBasicParsing
    Write-Host "Extracting MongoDB..." -ForegroundColor Yellow
    
    Add-Type -AssemblyName System.IO.Compression.FileSystem
    $zip = [System.IO.Compression.ZipFile]::OpenRead($mongoZip)
    foreach ($entry in $zip.Entries) {
        if ($entry.Name -like "*.exe" -or $entry.Name -like "*.dll") {
            $destinationPath = Join-Path $mongoBinDir $entry.Name
            [System.IO.Compression.ZipFileExtensions]::ExtractToFile($entry, $destinationPath, $true)
        }
    }
    $zip.Dispose()
    Remove-Item $mongoZip -Force
}

# Create MongoDB config
$mongoConfig = @"
storage:
  dbPath: "$($mongoDataDir -replace '\\', '/')"
systemLog:
  destination: file
  path: "$($mongoLogDir -replace '\\', '/')/mongod.log"
net:
  port: 27017
  bindIp: 127.0.0.1
"@
$configPath = Join-Path $mongoDir "mongod.conf"
$mongoConfig | Out-File -FilePath $configPath -Encoding UTF8

# Start MongoDB
Write-Host "Starting MongoDB..." -ForegroundColor Yellow
$mongoProcess = Start-Process -FilePath $mongodExe -ArgumentList "--config", "`"$configPath`"" -WindowStyle Hidden -PassThru

# Wait for MongoDB to start
$timeout = 30
for ($i = 0; $i -lt $timeout; $i++) {
    try {
        $connection = Test-NetConnection -ComputerName "localhost" -Port 27017 -InformationLevel Quiet -WarningAction SilentlyContinue
        if ($connection) {
            Write-Success "MongoDB started successfully"
            break
        }
    } catch { }
    Start-Sleep -Seconds 2
}

# Setup MongoDB users
Start-Sleep -Seconds 5
$mongoShell = Join-Path $mongoBinDir "mongosh.exe"
if (-not (Test-Path $mongoShell)) { $mongoShell = Join-Path $mongoBinDir "mongo.exe" }

if (Test-Path $mongoShell) {
    $setupScript = @"
use admin
try { db.createUser({user: "admin", pwd: "admin_pass_2025", roles: ["root"]}) } catch(e) { print("Admin user exists") }
use jira-dashboard
try { db.createUser({user: "jira_user", pwd: "jira_pass_2025", roles: ["readWrite"]}) } catch(e) { print("Jira user exists") }
db.createCollection("tickets")
db.createCollection("config")
print("Database setup complete")
"@
    $setupScriptPath = "$env:TEMP\mongo-setup.js"
    $setupScript | Out-File -FilePath $setupScriptPath -Encoding UTF8
    
    try {
        Start-Process -FilePath $mongoShell -ArgumentList $setupScriptPath -Wait -WindowStyle Hidden
        Write-Success "Database users created"
    } catch {
        Write-Warning "Database setup completed with warnings"
    }
    Remove-Item $setupScriptPath -Force -ErrorAction SilentlyContinue
}

# STEP 4: Build Production
Write-Step "Building Production Version"
Write-Host "Running npm run build..." -ForegroundColor Yellow
$buildProcess = Start-Process -FilePath "npm" -ArgumentList "run", "build" -Wait -PassThru -NoNewWindow
if ($buildProcess.ExitCode -eq 0) {
    Write-Success "Production build completed"
} else {
    Write-Error "Production build failed"
    exit 1
}

# STEP 5: Create Windows Service
Write-Step "Creating Windows Service"
$serviceName = "JiraDashboardService"
$serviceScript = Join-Path $scriptDir "service-wrapper.js"

# Create service wrapper
$wrapperContent = @"
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const projectDir = __dirname;
const logFile = path.join(projectDir, 'logs', 'service.log');

function log(message) {
    const timestamp = new Date().toISOString();
    const logMessage = `[`${timestamp}`] `${message}`\n`;
    console.log(logMessage.trim());
    fs.appendFileSync(logFile, logMessage);
}

function startDashboard() {
    log('🚀 Starting Jira Dashboard Service');
    const child = spawn('npm', ['run', 'production'], {
        cwd: projectDir,
        stdio: ['ignore', 'pipe', 'pipe']
    });
    
    child.stdout.on('data', (data) => log(`STDOUT: `${data.toString().trim()}`));
    child.stderr.on('data', (data) => log(`STDERR: `${data.toString().trim()}`));
    child.on('close', (code) => {
        log(`Process exited with code `${code}`);
        if (code !== 0) setTimeout(startDashboard, 10000);
    });
}

process.on('SIGTERM', () => process.exit(0));
process.on('SIGINT', () => process.exit(0));
startDashboard();
"@
$wrapperContent | Out-File -FilePath $serviceScript -Encoding UTF8

# Install Windows Service
$nodeExe = (Get-Command node).Source
$servicePath = "`"$nodeExe`" `"$serviceScript`""

try {
    # Remove existing service
    & sc.exe delete $serviceName 2>$null
    
    # Create new service
    & sc.exe create $serviceName binPath= $servicePath DisplayName= "Jira Dashboard Service" start= auto
    & sc.exe description $serviceName "Jira Dashboard Production Service"
    & sc.exe failure $serviceName reset= 86400 actions= restart/5000/restart/10000/restart/30000
    
    # Start service
    Start-Service -Name $serviceName
    Write-Success "Windows Service installed and started"
} catch {
    Write-Warning "Service installation failed, trying scheduled task"
    
    # Fallback to scheduled task
    $taskName = "Jira Dashboard Auto-Start"
    $action = New-ScheduledTaskAction -Execute "powershell.exe" -Argument "-WindowStyle Hidden -ExecutionPolicy Bypass -Command `"cd '$scriptDir'; npm run production`""
    $trigger = New-ScheduledTaskTrigger -AtStartup
    $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
    $trigger.Delay = "PT2M"
    
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Force
    Write-Success "Scheduled task created for auto-start"
}

# STEP 6: Test Installation
Write-Step "Testing Installation"

# Test components
try { $nodeVer = & node --version; Write-Success "Node.js: $nodeVer" } catch { Write-Error "Node.js test failed" }
try { $npmVer = & npm --version; Write-Success "npm: $npmVer" } catch { Write-Error "npm test failed" }

if (Test-Path "dist") { Write-Success "Production build: Available" } else { Write-Error "Production build: Missing" }

try {
    $mongoTest = Test-NetConnection -ComputerName "localhost" -Port 27017 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($mongoTest) { Write-Success "MongoDB: Running on port 27017" } else { Write-Warning "MongoDB: Not responding" }
} catch { Write-Warning "MongoDB: Connection test failed" }

# STEP 7: Start Dashboard
Write-Step "Starting Dashboard"
Write-Host "Starting production server..." -ForegroundColor Yellow

# Start the dashboard
Start-Process -FilePath "npm" -ArgumentList "run", "production" -WindowStyle Hidden

# Wait for server to start
Start-Sleep -Seconds 10
for ($i = 0; $i -lt 30; $i++) {
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
        if ($response.StatusCode -eq 200) {
            Write-Success "Dashboard server started successfully!"
            break
        }
    } catch { }
    Start-Sleep -Seconds 2
}

# Open browser in full screen
Write-Host "Opening dashboard in full screen..." -ForegroundColor Yellow
$browsers = @("chrome.exe", "msedge.exe", "firefox.exe")
$opened = $false

foreach ($browser in $browsers) {
    try {
        if (Get-Command $browser -ErrorAction SilentlyContinue) {
            if ($browser -eq "chrome.exe" -or $browser -eq "msedge.exe") {
                Start-Process $browser -ArgumentList "--start-fullscreen", "--kiosk", "http://localhost:3000"
            } else {
                Start-Process $browser -ArgumentList "-kiosk", "http://localhost:3000"
            }
            $opened = $true
            Write-Success "Dashboard opened with $browser"
            break
        }
    } catch { continue }
}

if (-not $opened) {
    Start-Process "http://localhost:3000"
    Write-Success "Dashboard opened with default browser"
}

# Final success message
Write-Host ""
Write-Host "🎉 INSTALLATION COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Green
Write-Host ""
Write-Host "✅ Node.js installed and working" -ForegroundColor Green
Write-Host "✅ MongoDB installed and running" -ForegroundColor Green
Write-Host "✅ Dependencies installed" -ForegroundColor Green
Write-Host "✅ Production build created" -ForegroundColor Green
Write-Host "✅ Windows Service/Auto-start configured" -ForegroundColor Green
Write-Host "✅ Dashboard running in full screen" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Dashboard URL: http://localhost:3000" -ForegroundColor Cyan
Write-Host "📁 Project Directory: $scriptDir" -ForegroundColor Gray
Write-Host "📝 Logs Directory: $logDir" -ForegroundColor Gray
Write-Host ""
Write-Host "🚀 Your Jira Dashboard is now ready for production!" -ForegroundColor Green
Write-Host "   - Starts automatically with Windows" -ForegroundColor White
Write-Host "   - Opens in full screen mode" -ForegroundColor White
Write-Host "   - Updates every 10 seconds" -ForegroundColor White
Write-Host "   - Auto-restarts on crashes" -ForegroundColor White
Write-Host ""

Read-Host "Press Enter to exit"
