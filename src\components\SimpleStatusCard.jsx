import React from 'react';

const SimpleStatusCard = ({ title, count, color = '#3498db', textColor = '#ffffff', onClick, className = '' }) => {
  return (
    <div 
      className={`rounded-lg shadow-lg p-6 cursor-pointer transition-all duration-200 hover:shadow-xl hover:scale-105 ${className}`}
      style={{ backgroundColor: color, color: textColor }}
      onClick={onClick}
    >
      <h3 className="text-lg font-semibold mb-2 opacity-90">
        {title}
      </h3>
      <div className="text-3xl font-bold">
        {typeof count === 'number' ? count.toLocaleString() : count}
      </div>
    </div>
  );
};

export default SimpleStatusCard;
