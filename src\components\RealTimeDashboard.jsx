import React, { useState, useEffect, useRef } from 'react';
import StatusCard from './StatusCard';

const RealTimeDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    tickets: [],
    stats: null,
    totalCount: 0
  });
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [newTicketsCount, setNewTicketsCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [performance, setPerformance] = useState(null);
  
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Initialize WebSocket connection
  useEffect(() => {
    connectWebSocket();
    
    // Cleanup on unmount
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  const connectWebSocket = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/dashboard`;
      
      console.log('🔌 Connecting to WebSocket:', wsUrl);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('✅ WebSocket connected');
        setConnectionStatus('connected');
        reconnectAttempts.current = 0;
        
        // Send ping to keep connection alive
        const pingInterval = setInterval(() => {
          if (wsRef.current?.readyState === WebSocket.OPEN) {
            wsRef.current.send(JSON.stringify({ type: 'ping' }));
          } else {
            clearInterval(pingInterval);
          }
        }, 30000); // Ping every 30 seconds
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('🔌 WebSocket disconnected');
        setConnectionStatus('disconnected');
        
        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);
          console.log(`🔄 Reconnecting in ${delay / 1000} seconds...`);
          
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectAttempts.current++;
            connectWebSocket();
          }, delay);
        } else {
          console.error('❌ Max reconnection attempts reached');
          setConnectionStatus('failed');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
        setConnectionStatus('error');
      };

    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      setConnectionStatus('failed');
    }
  };

  const handleWebSocketMessage = (message) => {
    switch (message.type) {
      case 'initial_data':
        console.log('📊 Received initial data');
        setDashboardData(prevData => ({
          ...prevData,
          stats: message.data
        }));
        setIsLoading(false);
        setLastUpdate(new Date(message.timestamp));
        break;

      case 'dashboard_update':
        console.log('🔄 Received dashboard update');
        const updateData = message.data;
        
        setDashboardData(prevData => ({
          ...prevData,
          stats: updateData.stats,
          totalCount: updateData.stats?.totalCount || prevData.totalCount
        }));
        
        if (updateData.newTickets && updateData.newTickets.length > 0) {
          setNewTicketsCount(updateData.newTickets.length);
          
          // Show notification for new tickets
          showNewTicketNotification(updateData.newTickets.length);
          
          // Clear notification after 5 seconds
          setTimeout(() => setNewTicketsCount(0), 5000);
        }
        
        setLastUpdate(new Date(message.timestamp));
        break;

      case 'pong':
        // Keep-alive response
        break;

      default:
        console.warn('⚠️ Unknown message type:', message.type);
    }
  };

  const showNewTicketNotification = (count) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(`${count} new ticket${count > 1 ? 's' : ''} received`, {
        icon: '/favicon.ico',
        badge: '/favicon.ico'
      });
    }
  };

  // Request notification permission
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Load initial data via HTTP if WebSocket fails
  useEffect(() => {
    if (connectionStatus === 'failed') {
      loadInitialDataHttp();
    }
  }, [connectionStatus]);

  const loadInitialDataHttp = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/mongodb/dashboard-tickets?stats=true');
      const data = await response.json();
      
      if (data.success) {
        setDashboardData({
          tickets: data.tickets || [],
          stats: data.stats,
          totalCount: data.count || 0
        });
        setPerformance(data.performance);
      }
    } catch (error) {
      console.error('❌ Error loading initial data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'connecting': return 'text-yellow-500';
      case 'disconnected': return 'text-orange-500';
      case 'error':
      case 'failed': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getConnectionStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected': return '🟢';
      case 'connecting': return '🟡';
      case 'disconnected': return '🟠';
      case 'error':
      case 'failed': return '🔴';
      default: return '⚪';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">Loading real-time dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold text-gray-900">Real-Time Jira Dashboard</h1>
          
          <div className="flex items-center space-x-4">
            {/* New tickets notification */}
            {newTicketsCount > 0 && (
              <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium animate-pulse">
                🆕 {newTicketsCount} new ticket{newTicketsCount > 1 ? 's' : ''}
              </div>
            )}
            
            {/* Connection status */}
            <div className={`flex items-center space-x-2 ${getConnectionStatusColor()}`}>
              <span>{getConnectionStatusIcon()}</span>
              <span className="text-sm font-medium capitalize">{connectionStatus}</span>
            </div>
            
            {/* Last update */}
            {lastUpdate && (
              <div className="text-sm text-gray-500">
                Last update: {lastUpdate.toLocaleTimeString()}
              </div>
            )}
          </div>
        </div>
        
        {/* Performance metrics */}
        {performance && (
          <div className="mt-2 text-sm text-gray-600">
            Loaded in {performance.loadTimeMs}ms ({performance.ticketsPerSecond} tickets/sec)
          </div>
        )}
      </div>

      {/* Dashboard content */}
      <div className="space-y-6">
        {/* Status cards */}
        {dashboardData.stats?.statusCounts && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
            {Object.entries(dashboardData.stats.statusCounts).map(([status, count]) => (
              <StatusCard
                key={status}
                title={status}
                count={count}
                color="bg-blue-500"
                textColor="text-white"
              />
            ))}
          </div>
        )}

        {/* Priority cards */}
        {dashboardData.stats?.priorityCounts && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(dashboardData.stats.priorityCounts).map(([priority, count]) => (
              <StatusCard
                key={priority}
                title={priority}
                count={count}
                color="bg-orange-500"
                textColor="text-white"
              />
            ))}
          </div>
        )}

        {/* Recent activity */}
        {dashboardData.stats?.recentActivity && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Recent Activity</h2>
            <div className="space-y-2">
              {dashboardData.stats.recentActivity.map((ticket, index) => (
                <div key={ticket.key || index} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                  <div>
                    <span className="font-medium">{ticket.key}</span>
                    <span className="ml-2 text-gray-600">{ticket.summary}</span>
                  </div>
                  <div className="text-sm text-gray-500">
                    {ticket.status} • {new Date(ticket.updated).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RealTimeDashboard;
