// In-memory storage service as MongoDB alternative for testing
// This provides the same interface as MongoDB but stores data in memory

class InMemoryStorage {
  constructor() {
    this.tickets = new Map(); // key -> ticket
    this.isConnected = false;
    this.lastSyncTime = null;
  }

  // Simulate MongoDB connection
  async connect() {
    this.isConnected = true;
    console.log('✅ Connected to in-memory storage (MongoDB simulation)');
    return true;
  }

  async disconnect() {
    this.isConnected = false;
    console.log('✅ Disconnected from in-memory storage');
  }

  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: this.isConnected ? 1 : 0,
      host: 'localhost',
      port: 'memory',
      name: 'jira-dashboard-memory'
    };
  }

  // Simulate ticket operations
  async bulkWrite(operations) {
    let upsertedCount = 0;
    let modifiedCount = 0;

    for (const op of operations) {
      if (op.updateOne) {
        const { filter, update, upsert } = op.updateOne;
        const key = filter.key;
        const ticketData = update.$set;

        if (this.tickets.has(key)) {
          // Update existing
          this.tickets.set(key, { ...this.tickets.get(key), ...ticketData });
          modifiedCount++;
        } else if (upsert) {
          // Insert new
          this.tickets.set(key, ticketData);
          upsertedCount++;
        }
      }
    }

    return {
      upsertedCount,
      modifiedCount,
      matchedCount: modifiedCount,
      deletedCount: 0,
      insertedCount: upsertedCount
    };
  }

  async countDocuments(filter = {}) {
    let count = 0;
    for (const ticket of this.tickets.values()) {
      if (this.matchesFilter(ticket, filter)) {
        count++;
      }
    }
    return count;
  }

  async find(filter = {}, options = {}) {
    let results = [];
    
    for (const ticket of this.tickets.values()) {
      if (this.matchesFilter(ticket, filter)) {
        results.push(ticket);
      }
    }

    // Apply sorting
    if (options.sort) {
      results = this.applySorting(results, options.sort);
    }

    // Apply pagination
    if (options.skip) {
      results = results.slice(options.skip);
    }
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return {
      sort: () => ({ limit: () => ({ lean: () => results }) }),
      limit: () => ({ lean: () => results }),
      lean: () => results
    };
  }

  async findOne(filter = {}, projection = {}, options = {}) {
    for (const ticket of this.tickets.values()) {
      if (this.matchesFilter(ticket, filter)) {
        if (options.sort) {
          // For findOne with sort, we need to sort all matching documents first
          const allMatching = [];
          for (const t of this.tickets.values()) {
            if (this.matchesFilter(t, filter)) {
              allMatching.push(t);
            }
          }
          const sorted = this.applySorting(allMatching, options.sort);
          return sorted[0] || null;
        }
        return ticket;
      }
    }
    return null;
  }

  async aggregate(pipeline) {
    // Simple aggregation simulation
    let results = Array.from(this.tickets.values());

    for (const stage of pipeline) {
      if (stage.$match) {
        results = results.filter(doc => this.matchesFilter(doc, stage.$match));
      } else if (stage.$group) {
        results = this.performGrouping(results, stage.$group);
      }
    }

    return results;
  }

  // Helper methods
  matchesFilter(document, filter) {
    if (!filter || Object.keys(filter).length === 0) {
      return true;
    }

    for (const [key, value] of Object.entries(filter)) {
      if (key === '$or') {
        // Handle $or operator
        const orConditions = value;
        let matchesOr = false;
        for (const condition of orConditions) {
          if (this.matchesFilter(document, condition)) {
            matchesOr = true;
            break;
          }
        }
        if (!matchesOr) return false;
      } else if (key === '$gte' || key === '$lte' || key === '$gt' || key === '$lt') {
        // Handle comparison operators (simplified)
        continue;
      } else if (typeof value === 'object' && value !== null) {
        if (value.$regex) {
          // Handle regex
          const regex = new RegExp(value.$regex, value.$options || '');
          if (!regex.test(document[key] || '')) return false;
        } else if (value.$gte !== undefined) {
          if (new Date(document[key]) < new Date(value.$gte)) return false;
        } else {
          // Handle nested object matching
          if (document[key] !== value) return false;
        }
      } else {
        // Simple equality check
        if (document[key] !== value) return false;
      }
    }

    return true;
  }

  applySorting(results, sortOptions) {
    return results.sort((a, b) => {
      for (const [field, direction] of Object.entries(sortOptions)) {
        const aVal = a[field];
        const bVal = b[field];
        
        if (aVal < bVal) return direction === 1 ? -1 : 1;
        if (aVal > bVal) return direction === 1 ? 1 : -1;
      }
      return 0;
    });
  }

  performGrouping(results, groupStage) {
    const groups = new Map();
    const groupBy = groupStage._id;
    
    for (const doc of results) {
      const groupKey = doc[groupBy.replace('$', '')];
      
      if (!groups.has(groupKey)) {
        groups.set(groupKey, { _id: groupKey, count: 0 });
      }
      
      const group = groups.get(groupKey);
      if (groupStage.count && groupStage.count.$sum === 1) {
        group.count++;
      }
    }
    
    return Array.from(groups.values());
  }

  // Get all tickets as array
  getAllTickets() {
    return Array.from(this.tickets.values());
  }

  // Clear all data
  clear() {
    this.tickets.clear();
    console.log('🧹 In-memory storage cleared');
  }

  // Get storage stats
  getStats() {
    return {
      totalTickets: this.tickets.size,
      memoryUsage: process.memoryUsage(),
      lastSyncTime: this.lastSyncTime
    };
  }
}

// Create singleton instance
const inMemoryStorage = new InMemoryStorage();

// Mock Ticket model interface
const MockTicket = {
  bulkWrite: (operations, options) => inMemoryStorage.bulkWrite(operations),
  countDocuments: (filter) => inMemoryStorage.countDocuments(filter),
  find: (filter, projection, options) => inMemoryStorage.find(filter, { ...projection, ...options }),
  findOne: (filter, projection, options) => inMemoryStorage.findOne(filter, projection, options),
  aggregate: (pipeline) => inMemoryStorage.aggregate(pipeline)
};

// Mock mongo service interface
const mockMongoService = {
  connect: () => inMemoryStorage.connect(),
  disconnect: () => inMemoryStorage.disconnect(),
  getConnectionStatus: () => inMemoryStorage.getConnectionStatus(),
  isConnected: true
};

export { mockMongoService, MockTicket, inMemoryStorage };
export default { mongoService: mockMongoService, Ticket: MockTicket };
