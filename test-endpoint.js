import axios from 'axios';

async function testEndpoint() {
  try {
    console.log('Testing dashboard-tickets endpoint...');
    const response = await axios.get('http://localhost:3001/api/mongodb/dashboard-tickets?sample=5');
    
    console.log('✅ Success!');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Count:', response.data.count);
    console.log('Source:', response.data.source);
    
    if (response.data.tickets && response.data.tickets.length > 0) {
      console.log('Sample ticket:', JSON.stringify(response.data.tickets[0], null, 2));

      // Check status and priority values in first 10 tickets
      console.log('\n📊 Status and Priority Analysis:');
      const statusCounts = {};
      const priorityCounts = {};

      response.data.tickets.slice(0, 100).forEach(ticket => {
        const status = ticket.status;
        const priority = ticket.priority;

        statusCounts[status] = (statusCounts[status] || 0) + 1;
        priorityCounts[priority] = (priorityCounts[priority] || 0) + 1;
      });

      console.log('Status values found:', Object.keys(statusCounts));
      console.log('Status counts:', statusCounts);
      console.log('Priority values found:', Object.keys(priorityCounts));
      console.log('Priority counts:', priorityCounts);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testEndpoint();
