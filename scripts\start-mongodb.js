#!/usr/bin/env node

import { spawn } from 'child_process';
import { existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// MongoDB configuration
const MONGODB_PORT = 27017;
const MONGODB_DATA_DIR = join(__dirname, '..', 'mongodb-data');
const MONGODB_LOG_DIR = join(__dirname, '..', 'mongodb-logs');

// Ensure directories exist
if (!existsSync(MONGODB_DATA_DIR)) {
  mkdirSync(MONGODB_DATA_DIR, { recursive: true });
  console.log(`📁 Created MongoDB data directory: ${MONGODB_DATA_DIR}`);
}

if (!existsSync(MONGODB_LOG_DIR)) {
  mkdirSync(MONGODB_LOG_DIR, { recursive: true });
  console.log(`📁 Created MongoDB log directory: ${MONGODB_LOG_DIR}`);
}

// Check if MongoDB is already running
function checkMongoDBRunning() {
  return new Promise((resolve) => {
    const testConnection = spawn('mongosh', [
      '--host', `localhost:${MONGODB_PORT}`,
      '--eval', 'db.runCommand("ping")',
      '--quiet'
    ]);

    testConnection.on('close', (code) => {
      resolve(code === 0);
    });

    testConnection.on('error', () => {
      resolve(false);
    });
  });
}

// Start MongoDB
async function startMongoDB() {
  console.log('🔍 Checking if MongoDB is already running...');
  
  const isRunning = await checkMongoDBRunning();
  
  if (isRunning) {
    console.log('✅ MongoDB is already running on port', MONGODB_PORT);
    return;
  }

  console.log('🚀 Starting MongoDB...');

  const mongodArgs = [
    '--dbpath', MONGODB_DATA_DIR,
    '--port', MONGODB_PORT.toString(),
    '--logpath', join(MONGODB_LOG_DIR, 'mongodb.log'),
    '--logappend',
    '--bind_ip', '127.0.0.1'
  ];

  const mongod = spawn('mongod', mongodArgs, {
    detached: true,
    stdio: 'ignore'
  });

  mongod.unref();

  // Wait a moment for MongoDB to start
  console.log('⏳ Waiting for MongoDB to start...');
  
  let attempts = 0;
  const maxAttempts = 30;
  
  while (attempts < maxAttempts) {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const isNowRunning = await checkMongoDBRunning();
    if (isNowRunning) {
      console.log('✅ MongoDB started successfully on port', MONGODB_PORT);
      console.log('📊 Database: jira-dashboard');
      console.log('🔗 Connection string: mongodb://localhost:27017/jira-dashboard');
      return;
    }
    
    attempts++;
    process.stdout.write('.');
  }

  console.log('\n❌ Failed to start MongoDB after', maxAttempts, 'seconds');
  console.log('💡 Please ensure MongoDB is installed and accessible in your PATH');
  console.log('💡 You can install MongoDB from: https://www.mongodb.com/try/download/community');
}

// Main execution
if (import.meta.url === `file://${process.argv[1]}`) {
  startMongoDB().catch(error => {
    console.error('❌ Error starting MongoDB:', error);
    process.exit(1);
  });
}

export { startMongoDB, checkMongoDBRunning };
