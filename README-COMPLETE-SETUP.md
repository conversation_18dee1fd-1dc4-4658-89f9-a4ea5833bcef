# 🚀 Jira Dashboard - Complete Auto-Installation Guide

## 🎯 One-Click Production Setup

Your Jira Dashboard now includes a **complete auto-installer** that sets up everything automatically without any manual intervention!

## ⚡ Quick Start (Recommended)

### 🖱️ **Method 1: Double-Click Installation**
```
1. Right-click on "INSTALL-EVERYTHING.bat"
2. Select "Run as administrator"
3. Click "Yes" when prompted
4. Wait 5-10 minutes for complete installation
5. Done! Dashboard starts automatically
```

### 💻 **Method 2: PowerShell Installation**
```powershell
# Right-click PowerShell and "Run as administrator"
.\auto-install-everything.ps1 -InstallNodeJS
```

## 🔧 What Gets Automatically Installed

### ✅ **Complete Software Stack:**
1. **Node.js 20.11.0** (if not already installed)
2. **MongoDB 7.0.5** (local installation with auto-configuration)
3. **All npm dependencies** (React, Express, MongoDB drivers, etc.)
4. **Production build** (optimized and minified)
5. **Windows Service** (auto-start on boot)
6. **Database setup** (users, collections, permissions)

### ✅ **Production Features:**
- 🔄 **Auto-restart** on crashes
- 🌐 **Full screen kiosk mode**
- 📊 **Health monitoring** every 30 seconds
- 📝 **Comprehensive logging**
- 🚀 **Starts with Windows** automatically
- 🔒 **Secure local-only access**

## 📋 System Requirements

- **Windows 10/11** (Administrator access required)
- **Internet connection** (for downloading components)
- **4GB RAM minimum** (8GB recommended)
- **2GB free disk space**
- **Modern browser** (Chrome, Edge, Firefox)

## 🎮 Installation Options

### 🚀 **Option 1: Complete Auto-Install (Recommended)**
```bash
# Double-click this file (Run as Administrator)
INSTALL-EVERYTHING.bat
```
**What it does:**
- ✅ Installs Node.js automatically
- ✅ Downloads and configures MongoDB
- ✅ Installs all dependencies
- ✅ Builds production version
- ✅ Sets up Windows Service
- ✅ Configures auto-start

### ⚙️ **Option 2: Manual Step-by-Step**
```bash
# 1. Install Node.js manually (if needed)
# Download from: https://nodejs.org

# 2. Install dependencies
npm install

# 3. Setup MongoDB
powershell -ExecutionPolicy Bypass -File "scripts\auto-install-mongodb.ps1"

# 4. Build production
npm run build

# 5. Install Windows Service
setup-auto-start.bat → Option 2
```

### 🔧 **Option 3: Scheduled Task (Alternative)**
```bash
# Run as Administrator
setup-auto-start.bat → Option 1
```

## 📁 Installation Files

```
jira-dashboard/
├── INSTALL-EVERYTHING.bat           # 🎯 Main auto-installer
├── auto-install-everything.ps1      # Complete PowerShell installer
├── setup-auto-start.bat            # Service management
├── start-production.bat             # Manual production start
├── scripts/
│   ├── auto-install-mongodb.ps1    # MongoDB auto-installer
│   ├── production-start.ps1        # Production startup script
│   ├── install-windows-service.ps1 # Windows Service installer
│   └── install-auto-start.ps1      # Scheduled task installer
├── logs/
│   ├── auto-install.log            # Installation logs
│   ├── production.log              # Production runtime logs
│   └── service.log                 # Windows Service logs
└── mongodb/                        # Local MongoDB installation
    ├── bin/                        # MongoDB executables
    ├── data/                       # Database files
    └── logs/                       # MongoDB logs
```

## 🔍 Verification & Testing

### ✅ **Check Installation Status:**
```bash
# Run as Administrator
setup-auto-start.bat → Option 4
```

### 🧪 **Test Components:**
```bash
# Test Node.js
node --version

# Test npm
npm --version

# Test MongoDB
mongosh --eval "db.runCommand('ping')"

# Test Dashboard
# Open: http://localhost:3000
```

### 📊 **Monitor Logs:**
```bash
# View installation log
type logs\auto-install.log

# View production log
type logs\production.log

# View service log
type logs\service.log
```

## 🛠️ Troubleshooting

### ❌ **Installation Failed**

1. **Check Administrator Rights:**
   ```
   Right-click → "Run as administrator"
   ```

2. **Check Internet Connection:**
   ```
   ping *******
   ```

3. **Check Antivirus:**
   - Temporarily disable antivirus
   - Add project folder to exclusions

4. **Check Windows Version:**
   - Requires Windows 10/11
   - PowerShell 5.0+ required

### ❌ **Service Won't Start**

1. **Check Service Status:**
   ```bash
   setup-auto-start.bat → Option 4
   ```

2. **Check Logs:**
   ```bash
   type logs\service.log
   ```

3. **Manual Start:**
   ```bash
   start-production.bat
   ```

4. **Reinstall Service:**
   ```bash
   setup-auto-start.bat → Option 6 (Uninstall)
   setup-auto-start.bat → Option 2 (Install)
   ```

### ❌ **Dashboard Not Loading**

1. **Check URL:**
   ```
   http://localhost:3000
   ```

2. **Check Port:**
   ```bash
   netstat -an | findstr :3000
   ```

3. **Check Firewall:**
   - Allow Node.js through Windows Firewall

4. **Check Browser:**
   - Try different browser
   - Clear cache and cookies

## 🔒 Security & Performance

### 🛡️ **Security Features:**
- ✅ **Local-only access** (localhost:3000)
- ✅ **No external network exposure**
- ✅ **Encrypted MongoDB authentication**
- ✅ **Windows Service isolation**
- ✅ **Secure file permissions**

### ⚡ **Performance Optimizations:**
- ✅ **Production build** (50-70% smaller files)
- ✅ **Asset compression** and minification
- ✅ **Efficient MongoDB queries**
- ✅ **Memory leak prevention**
- ✅ **Auto-restart** on high memory usage

## 📊 Production Dashboard Features

### 🎯 **What You Get:**
- ✅ **Real-time ticket updates** (every 10 seconds)
- ✅ **RESTAURANTS chart** (not "Organizations")
- ✅ **Priority cards** (Très Urgent, Urgent, Moyen)
- ✅ **Status cards** (configurable)
- ✅ **Full screen mode** with F11/Esc support
- ✅ **Professional DBSYS logo** (properly sized)
- ✅ **Dark/Light theme** support
- ✅ **Responsive design** for all screen sizes

### 🔄 **Auto-Features:**
- ✅ **Auto-start** on Windows boot
- ✅ **Auto-restart** on crashes
- ✅ **Auto-update** data every 10 seconds
- ✅ **Auto-save** configuration changes
- ✅ **Auto-recovery** from network issues

## 🎉 Success Indicators

When everything is working correctly:

✅ **Installation completed** without errors  
✅ **Windows Service running** (check Task Manager → Services)  
✅ **Dashboard accessible** at http://localhost:3000  
✅ **Full screen mode** working (F11 key)  
✅ **Data updating** every 10 seconds  
✅ **MongoDB connected** and responding  
✅ **Logs updating** in logs/ directory  
✅ **Auto-start enabled** (survives PC restart)  

## 🆘 Support & Maintenance

### 📞 **Getting Help:**
1. **Check logs first:** `logs\auto-install.log`
2. **Verify status:** `setup-auto-start.bat → Option 4`
3. **Try manual start:** `start-production.bat`
4. **Reinstall if needed:** `INSTALL-EVERYTHING.bat`

### 🔧 **Maintenance:**
- **Update dependencies:** `npm update`
- **Rebuild production:** `npm run build`
- **Restart service:** `setup-auto-start.bat`
- **View logs:** Check `logs/` directory

## 🚀 Ready for Production!

Your Jira Dashboard is now **enterprise-ready** with:

🎯 **Zero-configuration setup**  
🔄 **Automatic everything** (install, start, restart, update)  
🛡️ **Production-grade reliability**  
📊 **Professional dashboard display**  
🖥️ **Perfect for dedicated monitors**  

**Just double-click `INSTALL-EVERYTHING.bat` and you're done!** 🎉
