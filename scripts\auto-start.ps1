# Jira Dashboard Auto-Start PowerShell Script
# This script starts the Jira Dashboard in full screen mode

Write-Host "Starting Jira Dashboard Auto-Start..." -ForegroundColor Green

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir

# Change to project directory
Set-Location $projectDir

# Check if Node.js is available
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if npm is available
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Host "Error: npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Wait for network connectivity
Write-Host "Waiting for network connectivity..." -ForegroundColor Yellow
$timeout = 30
$elapsed = 0
while ($elapsed -lt $timeout) {
    try {
        $response = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet
        if ($response) {
            Write-Host "Network is available" -ForegroundColor Green
            break
        }
    }
    catch {
        # Continue waiting
    }
    Start-Sleep -Seconds 2
    $elapsed += 2
}

# Check if server is already running
$serverRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        $serverRunning = $true
        Write-Host "Server is already running" -ForegroundColor Green
    }
}
catch {
    Write-Host "Server is not running, starting production mode..." -ForegroundColor Yellow
}

# Start the server if not running
if (-not $serverRunning) {
    Write-Host "Starting Jira Dashboard in Production Mode..." -ForegroundColor Yellow

    # Start production server using the production script
    $productionScript = Join-Path $scriptDir "production-start.ps1"
    $serverProcess = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", "`"$productionScript`"" -WindowStyle Hidden -PassThru

    # Wait for server to start
    Write-Host "Waiting for production server to start..." -ForegroundColor Yellow
    $serverStarted = $false
    $maxWait = 120
    $waited = 0

    while ($waited -lt $maxWait -and -not $serverStarted) {
        Start-Sleep -Seconds 3
        $waited += 3

        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $serverStarted = $true
                Write-Host "Production server started successfully!" -ForegroundColor Green
            }
        }
        catch {
            # Continue waiting
        }

        # Show progress every 15 seconds
        if ($waited % 15 -eq 0) {
            Write-Host "Still waiting for server... ($waited/$maxWait seconds)" -ForegroundColor Yellow
        }
    }

    if (-not $serverStarted) {
        Write-Host "Error: Production server failed to start within $maxWait seconds" -ForegroundColor Red
        exit 1
    }
}

# Open browser in full screen mode
Write-Host "Opening dashboard in full screen mode..." -ForegroundColor Green

# Try different browsers
$browsers = @(
    "chrome.exe",
    "msedge.exe",
    "firefox.exe",
    "iexplore.exe"
)

$browserOpened = $false
foreach ($browser in $browsers) {
    try {
        if (Get-Command $browser -ErrorAction SilentlyContinue) {
            if ($browser -eq "chrome.exe") {
                Start-Process $browser -ArgumentList "--start-fullscreen", "--kiosk", "http://localhost:3000?autostart=true&fullscreen=true"
            }
            elseif ($browser -eq "msedge.exe") {
                Start-Process $browser -ArgumentList "--start-fullscreen", "--kiosk", "http://localhost:3000?autostart=true&fullscreen=true"
            }
            elseif ($browser -eq "firefox.exe") {
                Start-Process $browser -ArgumentList "-kiosk", "http://localhost:3000?autostart=true&fullscreen=true"
            }
            else {
                Start-Process $browser -ArgumentList "http://localhost:3000?autostart=true&fullscreen=true"
            }
            
            $browserOpened = $true
            Write-Host "Dashboard opened with $browser" -ForegroundColor Green
            break
        }
    }
    catch {
        continue
    }
}

if (-not $browserOpened) {
    # Fallback to default browser
    Start-Process "http://localhost:3000?autostart=true&fullscreen=true"
    Write-Host "Dashboard opened with default browser" -ForegroundColor Green
}

Write-Host "Jira Dashboard Auto-Start completed successfully!" -ForegroundColor Green
