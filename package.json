{"name": "jira-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server.js", "start": "concurrently \"npm run server\" \"npm run dev\"", "production": "npm run build && concurrently \"npm run server\" \"npm run preview -- --host 0.0.0.0 --port 3000\"", "production:server": "npm run build && npm run server", "production:preview": "npm run preview -- --host 0.0.0.0 --port 3000", "setup:mongodb": "node scripts/setup-mongodb.js", "docker:mongodb": "docker-compose up -d mongodb", "docker:full": "docker-compose up -d", "docker:stop": "docker-compose down", "docker:logs": "docker-compose logs -f mongodb", "start:mongodb": "node scripts/start-mongodb.js", "start:full": "npm run start:mongodb && npm start", "start:with-docker": "npm run docker:mongodb && npm start", "mongo:status": "mongosh --username jira_user --password jira_pass_2025 --authenticationDatabase jira-dashboard --eval 'db.runCommand(\"ping\")' --quiet", "mongo:stop": "mongosh --username admin --password admin_pass_2025 --authenticationDatabase admin --eval 'db.shutdownServer()' admin --quiet"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "axios": "^1.11.0", "chart.js": "^4.5.0", "concurrently": "^9.2.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "mongodb": "^6.18.0", "mongoose": "^8.17.0", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^5.4.0"}}