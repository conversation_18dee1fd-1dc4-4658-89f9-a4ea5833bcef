@echo off
echo ========================================
echo Jira Dashboard Auto-Start Setup
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Running as Administrator... Good!
    echo.
) else (
    echo This script requires Administrator privileges.
    echo Please right-click and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Choose an option:
echo 1. Install Auto-Start (Scheduled Task)
echo 2. Install Windows Service (Most Reliable)
echo 3. Start Production Mode (Manual)
echo 4. Check Status
echo 5. Uninstall Auto-Start
echo 6. Uninstall Windows Service
echo 7. Exit
echo.

set /p choice="Enter your choice (1-7): "

if "%choice%"=="1" (
    echo.
    echo Installing Auto-Start (Scheduled Task)...
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-auto-start.ps1" -Install
    echo.
    echo Installation completed!
    echo The Jira Dashboard will now start automatically when Windows boots.
) else if "%choice%"=="2" (
    echo.
    echo Installing Windows Service (Most Reliable)...
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-windows-service.ps1" -Install
    echo.
    echo Windows Service installation completed!
    echo The dashboard is now running as a Windows Service.
) else if "%choice%"=="3" (
    echo.
    echo Starting Production Mode...
    start-production.bat
) else if "%choice%"=="4" (
    echo.
    echo Checking Status...
    echo.
    echo === Auto-Start Status ===
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-auto-start.ps1" -Status
    echo.
    echo === Windows Service Status ===
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-windows-service.ps1" -Status
) else if "%choice%"=="5" (
    echo.
    echo Uninstalling Auto-Start...
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-auto-start.ps1" -Uninstall
    echo.
    echo Auto-start uninstallation completed!
) else if "%choice%"=="6" (
    echo.
    echo Uninstalling Windows Service...
    powershell.exe -ExecutionPolicy Bypass -File "scripts\install-windows-service.ps1" -Uninstall
    echo.
    echo Windows Service uninstallation completed!
) else if "%choice%"=="7" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice. Please try again.
)

echo.
pause
