import React, { useRef, useEffect } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const OrganizationChart = ({ data, config = {} }) => {
  const ref = useRef(null);

  const colorSchemes = {
    blue: { primary: 'rgba(59,130,246,0.7)', border: '#2563eb' },
    green: { primary: 'rgba(16,185,129,0.7)', border: '#059669' },
    red: { primary: 'rgba(239,68,68,0.7)', border: '#dc2626' },
    purple: { primary: 'rgba(139,92,246,0.7)', border: '#7c3aed' },
    orange: { primary: 'rgba(245,158,11,0.7)', border: '#d97706' }
  };

  const colors = colorSchemes[config.colorScheme] || colorSchemes.blue;

  const chartData = {
    labels: data.map(item => `${item.name} - ${item.count}`),
    datasets: [
      {
        label: 'Tickets',
        data: data.map(item => item.count),
        backgroundColor: colors.primary,
        borderColor: colors.border,
        borderWidth: 0,
        maxBarThickness: 24,
        minBarLength: 2,
        borderRadius: config.type === 'vertical-bar' ? { topLeft: 4, topRight: 4 } : { topRight: 4, bottomRight: 4 },
        borderSkipped: false,
      },
    ],
  };

  const options = {
    indexAxis: config.type === 'vertical-bar' ? 'x' : 'y',
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
      title: { display: false },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: colors.border,
        borderWidth: 1
      },
    },
    layout: {
      padding: {
        right: 8,
        left: 8,
        top: 8,
        bottom: 8
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        grid: {
          display: config.type === 'vertical-bar',
          color: 'rgba(148,163,184,0.25)'
        },
        ticks: {
          color: '#64748b',
          font: { size: 11 }
        },
        border: { display: false }
      },
      y: {
        grid: { display: false },
        ticks: {
          color: '#374151',
          font: { size: 12, weight: '500' },
          padding: 8
        },
        border: { display: false }
      },
    },
    animation: { duration: 300 },
    elements: {
      bar: {
        borderRadius: config.type === 'vertical-bar' ? { topLeft: 4, topRight: 4 } : { topRight: 4, bottomRight: 4 }
      }
    }
  };

  // Values are now displayed in the labels, so no need for overlay text

  return (
    <div ref={ref} style={{ height: '300px' }}>
      <Bar data={chartData} options={options} />
    </div>
  );
};

export default OrganizationChart;
