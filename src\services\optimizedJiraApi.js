import axios from 'axios';
import {
  loadDashboardConfig,
  mapJiraStatusToCard,
  mapJiraPriorityToCard,
  getVisibleStatusCards,
  getVisiblePriorityCards,
  getVisibleOrganizationCards
} from '../config/enhancedDashboardConfig.js';
import persistentStorage from './persistentStorage.js';

class OptimizedJiraApiService {
  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 15000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    this.lastUpdateTime = null;
    this.cachedTickets = [];
    this.isLoading = false;
    this.retryCount = 0;
    this.maxRetries = 2;
    this.isMongoAvailable = false;
    this.mongoInitialized = false;

    this.STORAGE_KEYS = {
      TICKETS: 'jira_dashboard_tickets',
      LAST_UPDATE: 'jira_dashboard_last_update',
      METADATA: 'jira_dashboard_metadata'
    };

    this.initializeStorage();
  }

  async initializeStorage() {
    try {
      await this.loadFromStorage();
      await this.checkMongoAvailability();
    } catch (error) {
      console.warn('⚠️ Storage initialization failed:', error.message);
    }
  }

  // Check MongoDB availability
  async checkMongoAvailability() {
    try {
      const response = await this.api.get('/mongodb/health');
      this.isMongoAvailable = response.data.success && response.data.available;
      this.mongoInitialized = true;

      if (this.isMongoAvailable) {
        console.log('✅ MongoDB is available for incremental sync');
      } else {
        console.log('📊 MongoDB not available, response:', response.data);
      }

      return this.isMongoAvailable;
    } catch (error) {
      console.warn('⚠️ MongoDB health check failed:', error.message);
      this.isMongoAvailable = false;
      this.mongoInitialized = true;
      return false;
    }
  }

  // Get data from MongoDB (incremental sync)
  async getDataFromMongo() {
    try {
      console.log('📊 Requesting tickets from server-side MongoDB...');
      const response = await this.api.get('/mongodb/dashboard-tickets');

      if (response.data.success) {
        const tickets = response.data.tickets || [];
        console.log('📊 Loaded ' + tickets.length + ' tickets from server-side MongoDB');

        // Debug: Check sample ticket format
        if (tickets.length > 0) {
          console.log('🔍 Sample ticket format:', tickets[0]);
        }

        this.cachedTickets = tickets;
        this.lastUpdateTime = new Date();

        const processedData = await this.processAllTicketsData(tickets);

        // Debug: Check processed data
        console.log('🔍 Processed data status counts:', processedData.status);
        console.log('🔍 Processed data priority counts:', processedData.priority);

        return processedData;
      } else {
        console.warn('⚠️ Server-side MongoDB request failed:', response.data.error);
        console.log('🔄 Falling back to direct API...');
        return await this.fallbackToDirectApi();
      }
    } catch (error) {
      console.error('❌ Failed to get tickets from server-side MongoDB:', error.message);
      console.log('🔄 Falling back to direct API...');
      return await this.fallbackToDirectApi();
    }
  }

  // Force full sync through MongoDB
  async forceMongoSync() {
    try {
      console.log('🔄 Forcing full MongoDB sync...');

      const response = await this.api.post('/mongodb/force-sync');

      if (response.data.success) {
        console.log('✅ Full MongoDB sync completed:', response.data.message);

        // Now get the synced data
        return await this.getDataFromMongo();
      } else {
        console.error('❌ Full sync failed:', response.data.error);
        return await this.fallbackToDirectApi();
      }
    } catch (error) {
      console.error('❌ Force sync request failed:', error.message);
      return await this.fallbackToDirectApi();
    }
  }

  // Fallback to direct API when MongoDB unavailable
  async fallbackToDirectApi() {
    console.log('📡 Falling back to direct Jira API (MongoDB unavailable from frontend)...');
    this.isMongoAvailable = false;

    // Try to load from existing cache first
    if (this.cachedTickets.length > 0) {
      console.log('📂 Using existing cached tickets (' + this.cachedTickets.length + ' tickets)');
      return await this.processAllTicketsData(this.cachedTickets);
    }

    // Use direct API calls as last resort
    return await this.loadAllTicketsFromApi();
  }

  // Load tickets directly from API (existing logic)
  async loadAllTicketsFromApi() {
    try {
      console.log('📡 Loading tickets directly from Jira API...');

      const currentConfig = loadDashboardConfig();
      const jqlQuery = this.buildJQLQuery(currentConfig, 'ORDER BY created DESC');

      const response = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
          maxResults: 1000 // Limited for fallback
        }
      });

      const tickets = response.data.issues || [];
      console.log('📡 Fetched ' + tickets.length + ' tickets from Jira API (fallback mode)');

      this.cachedTickets = tickets;
      this.lastUpdateTime = new Date();
      this.saveToStorage();

      return await this.processAllTicketsData(tickets);
    } catch (error) {
      console.error('❌ Direct API fallback failed:', error.message);
      throw error;
    }
  }

  buildJQLQuery(config, baseJQL) {
    baseJQL = baseJQL || 'ORDER BY created DESC';
    let jql = baseJQL;

    if (config.filters && config.filters.year && config.filters.year !== 'all') {
      const year = config.filters.year;
      const yearStart = year + '-01-01';
      const yearEnd = year + '-12-31';

      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', 'AND created >= "' + yearStart + '" AND created <= "' + yearEnd + '" ORDER BY');
      } else {
        jql = jql + ' AND created >= "' + yearStart + '" AND created <= "' + yearEnd + '"';
      }
    }

    if (config.jql && config.jql.baseProject) {
      const projectFilter = 'project = "' + config.jql.baseProject + '"';
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', 'AND ' + projectFilter + ' ORDER BY');
      } else {
        jql = jql + ' AND ' + projectFilter;
      }
    }

    if (config.jql && config.jql.additionalFilters) {
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', config.jql.additionalFilters + ' ORDER BY');
      } else {
        jql = jql + ' ' + config.jql.additionalFilters;
      }
    }

    // Add assignee filter if specified
    if (config.filters && config.filters.assignees && config.filters.assignees.length > 0) {
      const assigneeFilter = 'assignee in (' + config.filters.assignees.map(function(assignee) {
        return '"' + assignee + '"';
      }).join(', ') + ')';

      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', 'AND ' + assigneeFilter + ' ORDER BY');
      } else {
        jql = jql + ' AND ' + assigneeFilter;
      }
    }

    return jql;
  }

  async loadFromStorage() {
    try {
      console.log('📂 Loading tickets from MongoDB...');

      if (!this.isMongoAvailable) {
        console.log('📂 MongoDB not available, no tickets to load');
        return;
      }

      // Load directly from MongoDB
      const response = await this.api.get('/mongodb/dashboard-tickets');

      if (response.data.success && response.data.tickets) {
        this.cachedTickets = response.data.tickets;
        this.lastUpdateTime = new Date();
        console.log('📂 Loaded ' + this.cachedTickets.length + ' tickets from MongoDB');
        return;
      }

      console.log('📂 No tickets found in MongoDB');
    } catch (error) {
      console.warn('⚠️ Failed to load from MongoDB:', error.message);
    }
  }



  async saveToStorage() {
    try {
      console.log('💾 Saving ' + this.cachedTickets.length + ' tickets to MongoDB...');

      if (!this.isMongoAvailable) {
        console.warn('⚠️ MongoDB not available, cannot save tickets');
        return false;
      }

      // Save directly to MongoDB only
      const response = await this.api.post('/mongodb/force-sync', {
        tickets: this.cachedTickets
      });

      if (response.data.success) {
        console.log('✅ All tickets saved to MongoDB successfully');
        return true;
      } else {
        console.error('❌ Failed to save tickets to MongoDB:', response.data.error);
        return false;
      }

    } catch (error) {
      console.error('❌ Error saving to MongoDB:', error.message);
      return false;
    }
  }

  saveToLocalStorage(chunks) {
    try {
      // Clear old chunks
      let chunkIndex = 0;
      while (localStorage.getItem(this.STORAGE_KEYS.TICKETS + '_chunk_' + chunkIndex)) {
        localStorage.removeItem(this.STORAGE_KEYS.TICKETS + '_chunk_' + chunkIndex);
        chunkIndex++;
      }

      // Save new chunks
      chunks.forEach(function(chunk, index) {
        try {
          localStorage.setItem(this.STORAGE_KEYS.TICKETS + '_chunk_' + index, JSON.stringify(chunk));
        } catch (chunkError) {
          console.warn('⚠️ Failed to save chunk ' + index + ':', chunkError.message);
        }
      }.bind(this));

      // Save metadata
      localStorage.setItem(this.STORAGE_KEYS.TICKETS + '_chunks_count', chunks.length.toString());
      const updateTime = (this.lastUpdateTime && this.lastUpdateTime.toISOString()) || new Date().toISOString();
      localStorage.setItem(this.STORAGE_KEYS.LAST_UPDATE, updateTime);

      console.log('💾 Saved ' + this.cachedTickets.length + ' tickets to localStorage in ' + chunks.length + ' chunks');
    } catch (error) {
      console.warn('⚠️ Error saving to localStorage:', error.message);
      // Fallback: try to save without chunking (for smaller datasets)
      try {
        localStorage.setItem(this.STORAGE_KEYS.TICKETS, JSON.stringify(this.cachedTickets.slice(0, 1000)));
        console.log('💾 Saved first 1000 tickets as localStorage fallback');
      } catch (fallbackError) {
        console.error('❌ Complete localStorage failure:', fallbackError.message);
      }
    }
  }

  async saveToIndexedDB(tickets) {
    try {
      if (!window.indexedDB) {
        console.log('📱 IndexedDB not supported');
        return;
      }

      const dbName = 'JiraDashboardDB';
      const dbVersion = 1;
      const storeName = 'tickets';

      const request = indexedDB.open(dbName, dbVersion);

      request.onerror = function() {
        console.warn('⚠️ IndexedDB open failed');
      };

      request.onupgradeneeded = function(event) {
        const db = event.target.result;
        if (!db.objectStoreNames.contains(storeName)) {
          const objectStore = db.createObjectStore(storeName, { keyPath: 'key' });
          objectStore.createIndex('created', 'fields.created', { unique: false });
          console.log('🗄️ Created IndexedDB object store');
        }
      };

      request.onsuccess = function(event) {
        const db = event.target.result;
        const transaction = db.transaction([storeName], 'readwrite');
        const objectStore = transaction.objectStore(storeName);

        // Clear existing data
        objectStore.clear();

        // Save all tickets
        let savedCount = 0;
        tickets.forEach(function(ticket) {
          if (ticket.key) {
            const saveRequest = objectStore.add(ticket);
            saveRequest.onsuccess = function() {
              savedCount++;
              if (savedCount === tickets.length) {
                console.log('💾 Saved ' + savedCount + ' tickets to IndexedDB');
              }
            };
          }
        });

        // Save metadata
        const metadataStore = db.transaction([storeName], 'readwrite').objectStore(storeName);
        const metadata = {
          key: '_metadata',
          totalTickets: tickets.length,
          lastUpdate: new Date().toISOString(),
          version: '1.0'
        };
        metadataStore.add(metadata);
      };

    } catch (error) {
      console.warn('⚠️ IndexedDB save failed:', error.message);
    }
  }

  async loadFromIndexedDB() {
    return new Promise((resolve) => {
      try {
        if (!window.indexedDB) {
          console.log('📱 IndexedDB not supported');
          resolve();
          return;
        }

        const dbName = 'JiraDashboardDB';
        const storeName = 'tickets';
        const request = indexedDB.open(dbName);

        request.onerror = function() {
          console.warn('⚠️ IndexedDB open failed');
          resolve();
        };

        request.onsuccess = function(event) {
          const db = event.target.result;

          if (!db.objectStoreNames.contains(storeName)) {
            console.log('📂 No IndexedDB store found');
            resolve();
            return;
          }

          const transaction = db.transaction([storeName], 'readonly');
          const objectStore = transaction.objectStore(storeName);
          const getAllRequest = objectStore.getAll();

          getAllRequest.onsuccess = function() {
            const allRecords = getAllRequest.result;
            const tickets = allRecords.filter(function(record) {
              return record.key !== '_metadata';
            });

            const metadata = allRecords.find(function(record) {
              return record.key === '_metadata';
            });

            if (tickets.length > 0) {
              this.cachedTickets = tickets;
              if (metadata && metadata.lastUpdate) {
                this.lastUpdateTime = new Date(metadata.lastUpdate);
              }
              console.log('📂 Loaded ' + tickets.length + ' tickets from IndexedDB');
            }
            resolve();
          }.bind(this);

          getAllRequest.onerror = function() {
            console.warn('⚠️ IndexedDB read failed');
            resolve();
          };
        }.bind(this);

      } catch (error) {
        console.warn('⚠️ IndexedDB load failed:', error.message);
        resolve();
      }
    });
  }

  async clearStorage() {
    // Clear any remaining localStorage data
    this.clearLocalStorageCompletely();

    // Clear MongoDB data
    if (this.isMongoAvailable) {
      try {
        const response = await this.api.delete('/mongodb/clear');
        if (response.data.success) {
          console.log('🗑️ Cleared MongoDB data');
        }
      } catch (error) {
        console.warn('⚠️ Failed to clear MongoDB:', error.message);
      }
    }

    this.cachedTickets = [];
    this.lastUpdateTime = null;
    console.log('🗑️ Cleared all cached data (MongoDB-only mode)');
  }

  clearLocalStorageCompletely() {
    // Clear all localStorage keys related to Jira dashboard
    const keysToRemove = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.includes('jira_dashboard') || key.includes('JIRA_DASHBOARD'))) {
        keysToRemove.push(key);
      }
    }

    keysToRemove.forEach(key => localStorage.removeItem(key));
    console.log('🗑️ Cleared ' + keysToRemove.length + ' localStorage keys');
  }

  async clearIndexedDB() {
    try {
      if (!window.indexedDB) return;

      const dbName = 'JiraDashboardDB';
      const deleteRequest = indexedDB.deleteDatabase(dbName);

      deleteRequest.onsuccess = function() {
        console.log('🗑️ IndexedDB cleared successfully');
      };

      deleteRequest.onerror = function() {
        console.warn('⚠️ Failed to clear IndexedDB');
      };
    } catch (error) {
      console.warn('⚠️ IndexedDB clear error:', error.message);
    }
  }

  getStorageInfo() {
    const ticketsData = localStorage.getItem(this.STORAGE_KEYS.TICKETS);
    const ticketsSize = (ticketsData && ticketsData.length) || 0;
    const lastUpdate = localStorage.getItem(this.STORAGE_KEYS.LAST_UPDATE);
    return {
      ticketsCount: this.cachedTickets.length,
      storageSize: (ticketsSize / 1024 / 1024).toFixed(2) + ' MB',
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleString() : 'Never',
      cacheAge: this.lastUpdateTime ? ((new Date() - this.lastUpdateTime) / (1000 * 60)).toFixed(1) + ' minutes' : 'N/A'
    };
  }

  async getComprehensiveStorageInfo() {
    const localStorage = this.getStorageInfo();
    const serverStats = await persistentStorage.getStorageStats();
    const mongoStats = await this.getMongoStats();
    const syncStatus = await this.getSyncStatus();

    return {
      localStorage: localStorage,
      serverStorage: serverStats,
      mongodb: {
        available: this.isMongoAvailable,
        stats: mongoStats,
        syncStatus: syncStatus
      },
      totalTicketsInMemory: this.cachedTickets.length,
      lastUpdateTime: this.lastUpdateTime ? this.lastUpdateTime.toLocaleString() : 'Never',
      storageStrategy: this.isMongoAvailable ? 'Enterprise (MongoDB + Files + Browser)' : 'Multi-tier (localStorage + IndexedDB + Server Files)',
      persistenceLevel: this.isMongoAvailable ? 'Enterprise (MongoDB + File + Browser)' : 'High (survives browser and server restarts)'
    };
  }

  // Get MongoDB statistics
  async getMongoStats() {
    try {
      if (!this.isMongoAvailable) return null;

      const response = await this.api.get('/mongodb/stats');
      return response.data.success ? response.data.stats : null;
    } catch (error) {
      console.warn('⚠️ Failed to get MongoDB stats:', error.message);
      return null;
    }
  }

  // Get sync status
  async getSyncStatus() {
    try {
      if (!this.isMongoAvailable) return null;

      const response = await this.api.get('/mongodb/sync-status');
      return response.data.success ? response.data : null;
    } catch (error) {
      console.warn('⚠️ Failed to get sync status:', error.message);
      return null;
    }
  }

  async retryRequest(requestFn, retries) {
    retries = retries || this.maxRetries;
    for (let i = 0; i <= retries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        console.log('Attempt ' + (i + 1) + '/' + (retries + 1) + ' failed:', error.message);
        if (i === retries) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }

  async getAllDashboardData(forceRefresh = false) {
    console.log('🚀 MONGODB-ENHANCED API: getAllDashboardData() called');

    // Check MongoDB availability if not initialized
    if (!this.mongoInitialized) {
      await this.checkMongoAvailability();
    }

    // Always try to get data from server-side MongoDB first
    console.log('📊 Attempting to load data from server-side MongoDB...');

    try {
      // If force refresh is requested, force full sync
      if (forceRefresh) {
        console.log('🔄 Force refresh requested - performing full MongoDB sync');
        return await this.forceMongoSync();
      }

      // Try to get data from server-side MongoDB
      return await this.getDataFromMongo();
    } catch (error) {
      console.warn('⚠️ Server-side MongoDB failed, using direct API fallback:', error.message);
      return await this.fallbackToDirectApi();
    }

    if (this.cachedTickets.length > 0 && this.lastUpdateTime && !forceRefresh) {
      const hoursSinceUpdate = (new Date() - this.lastUpdateTime) / (1000 * 60 * 60);
      if (hoursSinceUpdate < 24) { // Use cached data for 24 hours instead of 1 hour
        console.log('Using cached data (' + this.cachedTickets.length + ' tickets, ' + hoursSinceUpdate.toFixed(1) + 'h old)');

        // Check for new tickets in the background
        this.checkForNewTickets().then(function(newData) {
          if (newData) {
            console.log('Found new tickets, data updated in background');
          }
        }).catch(function(error) {
          console.warn('Background update failed:', error.message);
        });

        return await this.processAllTicketsData(this.cachedTickets);
      } else {
        console.log('Cached data is ' + hoursSinceUpdate.toFixed(1) + 'h old, fetching updates...');
      }
    }

    if (this.isLoading) {
      console.log('Full load already in progress, skipping...');
      return null;
    }

    try {
      this.isLoading = true;
      console.log('Fetching ALL tickets from Jira...');

      const currentConfig = loadDashboardConfig();
      const jqlQuery = this.buildJQLQuery(currentConfig);

      const totalCountResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key',
          maxResults: 0
        }
      });

      const totalTickets = totalCountResponse.data.total || 0;
      console.log('Total tickets in Jira: ' + totalTickets.toLocaleString());

      const batchSize = 100;
      const maxTicketsToLoad = totalTickets; // Load ALL tickets
      const totalBatches = Math.ceil(maxTicketsToLoad / batchSize);
      let allTickets = [];
      let failedBatches = [];

      console.log('Total tickets available: ' + totalTickets.toLocaleString() + ', Loading: ' + maxTicketsToLoad.toLocaleString() + ', Batches: ' + totalBatches.toLocaleString());

      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;

        try {
          const response = await this.retryRequest(async () => {
            return await this.api.get('/jira/search', {
              params: {
                jql: jqlQuery,
                fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                maxResults: batchSize,
                startAt: startAt
              }
            });
          });

          const tickets = response.data.issues || [];

          if (tickets.length === 0) {
            console.log('Batch ' + (i + 1) + '/' + totalBatches + ': Empty response, continuing to next batch...');
          } else {
            allTickets = allTickets.concat(tickets);
            console.log('📥 Batch ' + (i + 1) + '/' + totalBatches + ': ' + tickets.length + ' tickets loaded (Total: ' + allTickets.length.toLocaleString() + ')');

            // Save progress every 10 batches to prevent data loss
            if ((i + 1) % 10 === 0) {
              console.log('💾 Saving progress: ' + allTickets.length.toLocaleString() + ' tickets...');
              // Don't cleanup during progress saves - just save the raw data
              this.cachedTickets = allTickets;
              this.lastUpdateTime = new Date();
              this.saveToStorage();
            }

            if ((i + 1) % 5 === 0 || i === totalBatches - 1) {
              const percentage = ((i + 1) / totalBatches * 100).toFixed(1);
              console.log('📊 Progress: ' + percentage + '% (' + allTickets.length.toLocaleString() + '/' + totalTickets.toLocaleString() + ' tickets)');
            }
          }

          if (i === totalBatches - 1 && allTickets.length < totalTickets * 0.9) {
            console.warn('Warning: Only loaded ' + allTickets.length + ' out of ' + totalTickets + ' expected tickets (' + ((allTickets.length/totalTickets)*100).toFixed(1) + '%)');
          }

          if (i < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }

        } catch (error) {
          console.error('Batch ' + (i + 1) + '/' + totalBatches + ' failed:', error.message);

          if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || (error.response && error.response.status >= 500)) {
            console.log('Retrying batch ' + (i + 1) + '/' + totalBatches + ' in 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            try {
              const retryResponse = await this.api.get('/jira/search', {
                params: {
                  jql: 'ORDER BY created DESC',
                  fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                  maxResults: batchSize,
                  startAt: startAt
                }
              });

              const tickets = retryResponse.data.issues || [];
              allTickets = allTickets.concat(tickets);
              console.log('Batch ' + (i + 1) + '/' + totalBatches + ' succeeded on retry: ' + tickets.length + ' tickets loaded (Total: ' + allTickets.length + ')');

            } catch (retryError) {
              console.error('Batch ' + (i + 1) + '/' + totalBatches + ' failed on retry:', retryError.message);
              failedBatches.push({ batchIndex: i, startAt: startAt, batchSize: batchSize });
            }
          } else {
            failedBatches.push({ batchIndex: i, startAt: startAt, batchSize: batchSize });
          }
        }
      }

      console.log('✅ Successfully loaded ' + allTickets.length.toLocaleString() + ' out of ' + totalTickets.toLocaleString() + ' tickets');

      const loadingPercentage = ((allTickets.length / totalTickets) * 100).toFixed(1);
      console.log('📊 Final Progress: ' + loadingPercentage + '% complete (' + allTickets.length.toLocaleString() + '/' + totalTickets.toLocaleString() + ' tickets)');

      if (allTickets.length < totalTickets) {
        const remaining = totalTickets - allTickets.length;
        console.log('⚠️ ' + remaining.toLocaleString() + ' tickets remaining to load');

        if (failedBatches.length > 0) {
          console.log('❌ ' + failedBatches.length + ' batches failed during loading');
          console.log('💡 Consider retrying failed batches or reducing batch size further');
        }
      } else {
        console.log('🎉 All tickets loaded successfully!');
      }

      if (failedBatches.length > 0) {
        console.log('📋 Loading Summary: ' + allTickets.length.toLocaleString() + '/' + totalTickets.toLocaleString() + ' tickets loaded (' + failedBatches.length + ' batches failed)');
      }

      console.log('🔧 Before cleanup: ' + allTickets.length + ' tickets');
      this.cachedTickets = this.cleanupCachedTickets(allTickets);
      console.log('🔧 After cleanup: ' + this.cachedTickets.length + ' tickets');
      this.lastUpdateTime = new Date();

      this.saveToStorage();

      return await this.processAllTicketsData(this.cachedTickets);

    } catch (error) {
      console.error('Error fetching optimized dashboard data:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  async checkForNewTickets() {
    try {
      if (!this.lastUpdateTime) {
        console.log('No previous update time, performing full refresh...');
        return await this.getAllDashboardData();
      }

      const currentConfig = loadDashboardConfig();
      const jqlQuery = this.buildJQLQuery(currentConfig, 'created >= -2m ORDER BY created DESC');

      const newTicketsResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
          maxResults: 100
        }
      });

      const newTickets = newTicketsResponse.data.issues || [];

      if (newTickets.length > 0) {
        console.log('Found ' + newTickets.length + ' new tickets!');
        this.cachedTickets = newTickets.concat(this.cachedTickets);
        this.lastUpdateTime = new Date();
        this.saveToStorage();
        return await this.processAllTicketsData(this.cachedTickets);
      } else {
        console.log('No new tickets found');
        return null;
      }

    } catch (error) {
      console.error('Error checking for new tickets:', error.message);

      try {
        const fallbackJQL = 'created >= -5m ORDER BY created DESC';
        const alternativeResponse = await this.api.get('/jira/search', {
          params: {
            jql: fallbackJQL,
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: 50
          }
        });

        const recentTickets = alternativeResponse.data.issues || [];

        if (recentTickets.length > 0) {
          console.log('Found ' + recentTickets.length + ' recent tickets using alternative method!');
          const self = this;
          const newTickets = recentTickets.filter(function(ticket) {
            if (!ticket.fields || !ticket.fields.created) return false;
            const ticketDate = new Date(ticket.fields.created);
            return ticketDate > self.lastUpdateTime;
          });

          if (newTickets.length > 0) {
            this.cachedTickets = newTickets.concat(this.cachedTickets);
            this.lastUpdateTime = new Date();
            this.saveToStorage();
            return await this.processAllTicketsData(this.cachedTickets);
          }
        }

        console.log('No new tickets found with alternative method');
        return null;

      } catch (altError) {
        console.error('Alternative method also failed:', altError.message);
      }

      const timeSinceLastUpdate = Date.now() - ((this.lastUpdateTime && this.lastUpdateTime.getTime()) || 0);
      if (timeSinceLastUpdate > 300000) {
        console.log('Performing full refresh due to extended time since last update...');
        return await this.getAllDashboardData();
      }

      console.log('Skipping update due to recent full refresh');
      return null;
    }
  }

  async processAllTicketsData(allTickets) {
    const currentConfig = loadDashboardConfig();
    const scaleFactor = 1;
    const totalTickets = allTickets.length;

    const statusCounts = this.processStatusCounts(allTickets, currentConfig, scaleFactor);
    const priorityCounts = this.processPriorityCounts(allTickets, currentConfig, scaleFactor);
    const organizationCounts = this.processOrganizationCounts(allTickets, currentConfig, scaleFactor);
    const organizationData = this.processOrganizationData(allTickets);
    const trendsData = this.generateTrendsData(allTickets);
    const dashboardStats = this.calculateDashboardStats(allTickets, totalTickets);

    return {
      status: statusCounts,
      priority: priorityCounts,
      organization: organizationCounts,
      organizationData: organizationData,
      trends: trendsData,
      stats: dashboardStats,
      allTickets: allTickets, // Include the actual tickets array
      metadata: {
        totalTickets: totalTickets,
        actualTickets: allTickets.length,
        scaleFactor: scaleFactor.toFixed(2),
        lastUpdated: new Date().toLocaleString(),
        isRealTimeUpdate: true
      }
    };
  }

  formatDateForJira(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
  }

  extractOrganizationFromTicket(ticket) {
    const fields = ticket.fields || {};
    let organization = 'Other';

    // Handle both old format (ticket.fields.project.key) and new lightweight format (ticket.key prefix)
    const projectKey = (fields.project && fields.project.key) || ticket.key?.split('-')[0] || '';
    const projectName = ticket.project || (fields.project && fields.project.name) || '';

    if (Math.random() < 0.05) {
      console.log('Debug ticket ' + ticket.key + ': projectKey="' + projectKey + '", project="' + ((fields.project && fields.project.name) || '') + '"');
      console.log('   Available fields:', Object.keys(fields));
      console.log('   Labels:', fields.labels);
      console.log('   Components:', (fields.components && fields.components.map(function(c) { return c.name; })) || []);
    }

    if (projectKey === 'STORE' || projectKey.includes('STORE')) {
      organization = 'STORE';
    } else if (['IT', 'SUPPORT', 'TECH', 'HELPDESK', 'SERVICE'].includes(projectKey.toUpperCase())) {
      organization = 'IT Support';
    } else if (['OPS', 'OPERATIONS', 'OPERATION'].includes(projectKey.toUpperCase())) {
      organization = 'Operations';
    } else if (['MAINT', 'MAINTENANCE', 'REPAIR'].includes(projectKey.toUpperCase())) {
      organization = 'Maintenance';
    }

    const projectNameLower = projectName.toLowerCase();
    if (organization === 'Other' && projectNameLower) {
      if (projectNameLower.includes('store') || projectNameLower.includes('magasin') || projectNameLower.includes('shop')) {
        organization = 'STORE';
      } else if (projectNameLower.includes('it') || projectNameLower.includes('support') || projectNameLower.includes('tech') || projectNameLower.includes('service')) {
        organization = 'IT Support';
      } else if (projectNameLower.includes('ops') || projectNameLower.includes('operation')) {
        organization = 'Operations';
      } else if (projectName.includes('maint') || projectName.includes('maintenance') || projectName.includes('repair')) {
        organization = 'Maintenance';
      }
    }

    const customFields = [
      fields.customfield_10000,
      fields.customfield_10001,
      fields.customfield_10002,
      fields.customfield_10003,
      fields.customfield_10004,
      fields.customfield_10005
    ];

    for (let i = 0; i < customFields.length; i++) {
      const customField = customFields[i];
      if (customField) {
        const value = typeof customField === 'string' ? customField : customField.value || customField.name || '';
        const lowerValue = value.toLowerCase();

        if (lowerValue.includes('store') || lowerValue.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerValue.includes('it') || lowerValue.includes('support') || lowerValue.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerValue.includes('ops') || lowerValue.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerValue.includes('maint') || lowerValue.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    if (fields.labels && Array.isArray(fields.labels)) {
      for (let i = 0; i < fields.labels.length; i++) {
        const label = fields.labels[i];
        const lowerLabel = label.toLowerCase();
        if (lowerLabel.includes('store') || lowerLabel.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerLabel.includes('it') || lowerLabel.includes('support') || lowerLabel.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerLabel.includes('ops') || lowerLabel.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerLabel.includes('maint') || lowerLabel.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    if (fields.components && Array.isArray(fields.components)) {
      for (let i = 0; i < fields.components.length; i++) {
        const component = fields.components[i];
        const componentName = (component.name || '').toLowerCase();
        if (componentName.includes('store') || componentName.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (componentName.includes('it') || componentName.includes('support') || componentName.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (componentName.includes('ops') || componentName.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (componentName.includes('maint') || componentName.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    if (Math.random() < 0.01) {
      console.log('Debug ticket ' + ticket.key + ': Final organization="' + organization + '"');
    }

    return organization;
  }

  cleanupCachedTickets(tickets) {
    const uniqueTickets = [];
    const seenKeys = new Set();

    for (let i = 0; i < tickets.length; i++) {
      const ticket = tickets[i];
      if (ticket.key && !seenKeys.has(ticket.key)) {
        seenKeys.add(ticket.key);
        uniqueTickets.push(ticket);
      }
    }

    uniqueTickets.sort(function(a, b) {
      const dateA = new Date((a.fields && a.fields.created) || 0);
      const dateB = new Date((b.fields && b.fields.created) || 0);
      return dateB - dateA;
    });

    const maxTickets = 50000;
    if (uniqueTickets.length > maxTickets) {
      console.log('Cleaning up cached tickets: ' + uniqueTickets.length + ' -> ' + maxTickets);
      return uniqueTickets.slice(0, maxTickets);
    }

    return uniqueTickets;
  }

  processStatusCounts(tickets, config, scaleFactor) {
    const statusCounts = {};

    getVisibleStatusCards(config).forEach(function(card) {
      statusCounts[card.id] = 0;
    });

    tickets.forEach(function(ticket) {
      // Handle both old format (ticket.fields.status.name) and new lightweight format (ticket.status)
      const status = ticket.status || (ticket.fields && ticket.fields.status && ticket.fields.status.name);
      if (status) {
        const cardId = mapJiraStatusToCard(status, config);
        if (cardId && statusCounts.hasOwnProperty(cardId)) {
          statusCounts[cardId]++;
        } else {
          // Debug: log unmapped statuses
          if (Math.random() < 0.001) { // Log 0.1% of tickets for debugging
            console.log('🔍 Debug: Unmapped status "' + status + '" for ticket ' + ticket.key + ', cardId: ' + cardId);
          }
        }
      }
    });

    const result = {};
    getVisibleStatusCards(config).forEach(function(card) {
      const originalCount = statusCounts[card.id] || 0;
      result[card.title] = Math.round(originalCount * scaleFactor);
    });

    return result;
  }

  processPriorityCounts(tickets, config, scaleFactor) {
    const priorityCounts = {};

    getVisiblePriorityCards(config).forEach(function(card) {
      priorityCounts[card.id] = 0;
    });

    tickets.forEach(function(ticket) {
      // Handle both old format (ticket.fields.priority.name) and new lightweight format (ticket.priority)
      const priority = ticket.priority || (ticket.fields && ticket.fields.priority && ticket.fields.priority.name);
      if (priority) {
        const cardId = mapJiraPriorityToCard(priority, config);
        if (cardId && priorityCounts.hasOwnProperty(cardId)) {
          priorityCounts[cardId]++;
        } else {
          // Debug: log unmapped priorities
          if (Math.random() < 0.001) { // Log 0.1% of tickets for debugging
            console.log('🔍 Debug: Unmapped priority "' + priority + '" for ticket ' + ticket.key + ', cardId: ' + cardId);
          }
        }
      }
    });

    const result = {};
    getVisiblePriorityCards(config).forEach(function(card) {
      const originalCount = priorityCounts[card.id] || 0;
      result[card.title] = Math.round(originalCount * scaleFactor);
    });

    return result;
  }

  processOrganizationCounts(tickets, config, scaleFactor) {
    const organizationCounts = {};

    getVisibleOrganizationCards(config).forEach(function(card) {
      organizationCounts[card.title] = 0;
    });

    const self = this;
    tickets.forEach(function(ticket) {
      const orgName = self.extractOrganizationFromTicket(ticket);
      if (orgName && organizationCounts.hasOwnProperty(orgName)) {
        organizationCounts[orgName] += scaleFactor;
      }
    });

    Object.keys(organizationCounts).forEach(function(key) {
      organizationCounts[key] = Math.round(organizationCounts[key]);
    });

    return organizationCounts;
  }

  processOrganizationData(tickets) {
    const projectCounts = {};

    tickets.forEach(function(ticket) {
      const project = (ticket.fields && ticket.fields.project && ticket.fields.project.key) || (ticket.key && ticket.key.split('-')[0]);
      if (project) {
        projectCounts[project] = (projectCounts[project] || 0) + 1;
      }
    });

    return Object.entries(projectCounts)
      .map(function(entry) { return { name: entry[0], count: entry[1] }; })
      .sort(function(a, b) { return b.count - a.count; })
      .slice(0, 10);
  }

  generateTrendsData(tickets) {
    tickets = tickets || [];
    const trends = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 0);

      const monthTickets = tickets.filter(function(ticket) {
        if (!ticket.fields || !ticket.fields.created) return false;
        const createdDate = new Date(ticket.fields.created);
        return createdDate >= monthStart && createdDate <= monthEnd;
      });

      trends.push({
        name: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        count: monthTickets.length
      });
    }

    return trends;
  }

  calculateDashboardStats(tickets, totalTickets) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const ticketsToday = tickets.filter(function(ticket) {
      const created = new Date((ticket.fields && ticket.fields.created) || 0);
      return created >= today;
    }).length;

    return {
      totalTickets: totalTickets,
      ticketsToday: ticketsToday,
      recentTickets: tickets.slice(0, 10).map(function(ticket) {
        return {
          key: ticket.key,
          summary: (ticket.fields && ticket.fields.summary),
          status: (ticket.fields && ticket.fields.status && ticket.fields.status.name),
          priority: (ticket.fields && ticket.fields.priority && ticket.fields.priority.name),
          created: (ticket.fields && ticket.fields.created)
        };
      })
    };
  }

  async getTableTickets(page, pageSize, filters) {
    page = page || 0;
    pageSize = pageSize || 25;
    filters = filters || {};
    
    try {
      let jql = 'ORDER BY created DESC';
      
      if (filters.status) {
        jql = 'status = "' + filters.status + '" AND ' + jql;
      }
      if (filters.priority) {
        jql = 'priority = "' + filters.priority + '" AND ' + jql;
      }
      if (filters.project) {
        jql = 'project = "' + filters.project + '" AND ' + jql;
      }

      const response = await this.api.get('/jira/search', {
        params: {
          jql: jql,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description',
          maxResults: pageSize,
          startAt: page * pageSize
        }
      });

      const issues = response.data.issues || [];
      const transformedTickets = issues.map(function(issue) {
        return {
          key: issue.key,
          summary: (issue.fields && issue.fields.summary) || 'No summary',
          status: (issue.fields && issue.fields.status && issue.fields.status.name) || 'Unknown',
          priority: (issue.fields && issue.fields.priority && issue.fields.priority.name) || 'None',
          assignee: (issue.fields && issue.fields.assignee && issue.fields.assignee.displayName) || 'Unassigned',
          projectKey: (issue.fields && issue.fields.project && issue.fields.project.key) || (issue.key && issue.key.split('-')[0]) || 'Unknown',
          project: (issue.fields && issue.fields.project && issue.fields.project.name) || (issue.fields && issue.fields.project && issue.fields.project.key) || 'Unknown',
          created: (issue.fields && issue.fields.created) ? new Date(issue.fields.created).toLocaleDateString() : 'Unknown',
          updated: (issue.fields && issue.fields.updated) ? new Date(issue.fields.updated).toLocaleDateString() : 'Unknown',
          description: (issue.fields && issue.fields.description) || '',
          originalIssue: issue
        };
      });

      return {
        tickets: transformedTickets,
        total: response.data.total || 0,
        page: page,
        pageSize: pageSize
      };
    } catch (error) {
      console.error('Error fetching table tickets:', error);
      throw error;
    }
  }

  async getAllAssignees() {
    try {
      // Get unique assignees from cached tickets
      if (this.cachedTickets.length > 0) {
        const assignees = new Set();

        this.cachedTickets.forEach(function(ticket) {
          // Handle both old format (ticket.fields.assignee.displayName) and new lightweight format (ticket.assignee)
          const assignee = ticket.assignee || (ticket.fields && ticket.fields.assignee && ticket.fields.assignee.displayName);
          if (assignee && assignee !== 'Unassigned') {
            assignees.add(assignee);
          }
        });

        return Array.from(assignees).sort();
      }

      // Fallback: fetch from API
      const response = await this.api.get('/jira/search', {
        params: {
          jql: 'assignee is not EMPTY ORDER BY assignee',
          fields: 'assignee',
          maxResults: 1000
        }
      });

      const assignees = new Set();
      const issues = response.data.issues || [];

      issues.forEach(function(issue) {
        const assignee = (issue.fields && issue.fields.assignee && issue.fields.assignee.displayName);
        if (assignee) {
          assignees.add(assignee);
        }
      });

      return Array.from(assignees).sort();

    } catch (error) {
      console.error('Error fetching assignees:', error);
      return [];
    }
  }
}

const optimizedJiraApi = new OptimizedJiraApiService();
export default optimizedJiraApi;
