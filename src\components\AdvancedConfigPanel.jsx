import React, { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';

const AdvancedConfigPanel = ({ isOpen, onClose, onSave }) => {
  const [config, setConfig] = useState({
    statusCards: [],
    priorityCards: [],
    assigneeFilters: [],
    theme: 'light',
    refreshInterval: 10000,
    enableNotifications: true,
    enableAnimations: true
  });
  
  const [activeTab, setActiveTab] = useState('status');
  const [previewMode, setPreviewMode] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  // Load configuration on mount
  useEffect(() => {
    loadConfiguration();
  }, []);

  const loadConfiguration = async () => {
    try {
      const response = await fetch('/api/config/dashboard');
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config || getDefaultConfig());
      } else {
        setConfig(getDefaultConfig());
      }
    } catch (error) {
      console.error('Error loading configuration:', error);
      setConfig(getDefaultConfig());
    }
  };

  const getDefaultConfig = () => ({
    statusCards: [
      {
        id: 'non_traites',
        title: 'Non traités',
        color: '#5e72e4',
        textColor: '#ffffff',
        jiraStatuses: ['Open'],
        visible: true,
        order: 0,
        assigneeFilter: null
      },
      {
        id: 'en_cours',
        title: 'En cours',
        color: '#11cdef',
        textColor: '#ffffff',
        jiraStatuses: ['In Progress'],
        visible: true,
        order: 1,
        assigneeFilter: null
      },
      {
        id: 'resolu',
        title: 'Résolu',
        color: '#2dce89',
        textColor: '#ffffff',
        jiraStatuses: ['Resolved', 'Closed'],
        visible: true,
        order: 2,
        assigneeFilter: null
      }
    ],
    priorityCards: [
      {
        id: 'tres_urgent',
        title: 'Incident Très Urgent',
        color: '#dc3545',
        textColor: '#ffffff',
        jiraPriorities: ['Urgent'],
        visible: true,
        order: 0,
        assigneeFilter: null
      },
      {
        id: 'urgent',
        title: 'Incident urgent',
        color: '#fd7e14',
        textColor: '#ffffff',
        jiraPriorities: ['Moyen'],
        visible: true,
        order: 1,
        assigneeFilter: null
      },
      {
        id: 'moyen',
        title: 'Incident moyen',
        color: '#6c757d',
        textColor: '#ffffff',
        jiraPriorities: ['Faible', 'Très faible'],
        visible: true,
        order: 2,
        assigneeFilter: null
      }
    ],
    assigneeFilters: [],
    theme: 'light',
    refreshInterval: 10000,
    enableNotifications: true,
    enableAnimations: true
  });

  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const { source, destination, type } = result;
    
    if (type === 'statusCards') {
      const newCards = Array.from(config.statusCards);
      const [reorderedCard] = newCards.splice(source.index, 1);
      newCards.splice(destination.index, 0, reorderedCard);
      
      // Update order property
      newCards.forEach((card, index) => {
        card.order = index;
      });
      
      setConfig(prev => ({ ...prev, statusCards: newCards }));
      setUnsavedChanges(true);
    } else if (type === 'priorityCards') {
      const newCards = Array.from(config.priorityCards);
      const [reorderedCard] = newCards.splice(source.index, 1);
      newCards.splice(destination.index, 0, reorderedCard);
      
      // Update order property
      newCards.forEach((card, index) => {
        card.order = index;
      });
      
      setConfig(prev => ({ ...prev, priorityCards: newCards }));
      setUnsavedChanges(true);
    }
  };

  const addCard = (type) => {
    const newCard = {
      id: `${type}_${Date.now()}`,
      title: `New ${type} Card`,
      color: '#6c757d',
      textColor: '#ffffff',
      [type === 'status' ? 'jiraStatuses' : 'jiraPriorities']: [],
      visible: true,
      order: config[`${type}Cards`].length,
      assigneeFilter: null
    };

    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: [...prev[`${type}Cards`], newCard]
    }));
    setUnsavedChanges(true);
  };

  const updateCard = (type, cardId, updates) => {
    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: prev[`${type}Cards`].map(card =>
        card.id === cardId ? { ...card, ...updates } : card
      )
    }));
    setUnsavedChanges(true);
  };

  const deleteCard = (type, cardId) => {
    setConfig(prev => ({
      ...prev,
      [`${type}Cards`]: prev[`${type}Cards`].filter(card => card.id !== cardId)
    }));
    setUnsavedChanges(true);
  };

  const saveConfiguration = async () => {
    try {
      const response = await fetch('/api/config/dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ config })
      });

      if (response.ok) {
        setUnsavedChanges(false);
        onSave?.(config);
        alert('Configuration saved successfully!');
      } else {
        throw new Error('Failed to save configuration');
      }
    } catch (error) {
      console.error('Error saving configuration:', error);
      alert('Error saving configuration. Please try again.');
    }
  };

  const resetConfiguration = () => {
    if (confirm('Are you sure you want to reset to default configuration? This will lose all your changes.')) {
      setConfig(getDefaultConfig());
      setUnsavedChanges(true);
    }
  };

  const CardEditor = ({ card, type, index }) => (
    <Draggable draggableId={card.id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          className={`bg-white rounded-lg shadow-md p-4 mb-4 border-2 ${
            snapshot.isDragging ? 'border-blue-500 shadow-lg' : 'border-gray-200'
          }`}
        >
          <div className="flex items-center justify-between mb-3">
            <div {...provided.dragHandleProps} className="cursor-move text-gray-400 hover:text-gray-600">
              ⋮⋮
            </div>
            <div className="flex items-center space-x-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={card.visible}
                  onChange={(e) => updateCard(type, card.id, { visible: e.target.checked })}
                  className="mr-2"
                />
                Visible
              </label>
              <button
                onClick={() => deleteCard(type, card.id)}
                className="text-red-500 hover:text-red-700 text-sm"
              >
                🗑️
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
              <input
                type="text"
                value={card.title}
                onChange={(e) => updateCard(type, card.id, { title: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  value={card.color}
                  onChange={(e) => updateCard(type, card.id, { color: e.target.value })}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={card.color}
                  onChange={(e) => updateCard(type, card.id, { color: e.target.value })}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Text Color</label>
              <div className="flex items-center space-x-2">
                <input
                  type="color"
                  value={card.textColor}
                  onChange={(e) => updateCard(type, card.id, { textColor: e.target.value })}
                  className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                />
                <input
                  type="text"
                  value={card.textColor}
                  onChange={(e) => updateCard(type, card.id, { textColor: e.target.value })}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {type === 'status' ? 'Jira Statuses' : 'Jira Priorities'}
              </label>
              <input
                type="text"
                value={(type === 'status' ? card.jiraStatuses : card.jiraPriorities).join(', ')}
                onChange={(e) => {
                  const values = e.target.value.split(',').map(v => v.trim()).filter(v => v);
                  updateCard(type, card.id, {
                    [type === 'status' ? 'jiraStatuses' : 'jiraPriorities']: values
                  });
                }}
                placeholder="Enter comma-separated values"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Preview */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Preview</label>
            <div
              className="inline-block px-4 py-2 rounded-lg text-sm font-medium"
              style={{
                backgroundColor: card.color,
                color: card.textColor
              }}
            >
              {card.title} (42)
            </div>
          </div>
        </div>
      )}
    </Draggable>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Advanced Dashboard Configuration</h2>
          <div className="flex items-center space-x-4">
            {unsavedChanges && (
              <span className="text-orange-500 text-sm">● Unsaved changes</span>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          {[
            { id: 'status', label: 'Status Cards' },
            { id: 'priority', label: 'Priority Cards' },
            { id: 'settings', label: 'Settings' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-medium ${
                activeTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          <DragDropContext onDragEnd={handleDragEnd}>
            {activeTab === 'status' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold">Status Cards</h3>
                  <button
                    onClick={() => addCard('status')}
                    className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
                  >
                    + Add Status Card
                  </button>
                </div>
                
                <Droppable droppableId="statusCards" type="statusCards">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {config.statusCards
                        .sort((a, b) => a.order - b.order)
                        .map((card, index) => (
                          <CardEditor
                            key={card.id}
                            card={card}
                            type="status"
                            index={index}
                          />
                        ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
            )}

            {activeTab === 'priority' && (
              <div>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-semibold">Priority Cards</h3>
                  <button
                    onClick={() => addCard('priority')}
                    className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
                  >
                    + Add Priority Card
                  </button>
                </div>
                
                <Droppable droppableId="priorityCards" type="priorityCards">
                  {(provided) => (
                    <div {...provided.droppableProps} ref={provided.innerRef}>
                      {config.priorityCards
                        .sort((a, b) => a.order - b.order)
                        .map((card, index) => (
                          <CardEditor
                            key={card.id}
                            card={card}
                            type="priority"
                            index={index}
                          />
                        ))}
                      {provided.placeholder}
                    </div>
                  )}
                </Droppable>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold">General Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Theme
                    </label>
                    <select
                      value={config.theme}
                      onChange={(e) => setConfig(prev => ({ ...prev, theme: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                      <option value="auto">Auto</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Refresh Interval (ms)
                    </label>
                    <input
                      type="number"
                      value={config.refreshInterval}
                      onChange={(e) => setConfig(prev => ({ ...prev, refreshInterval: parseInt(e.target.value) }))}
                      min="1000"
                      max="60000"
                      step="1000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.enableNotifications}
                        onChange={(e) => setConfig(prev => ({ ...prev, enableNotifications: e.target.checked }))}
                        className="mr-2"
                      />
                      Enable Notifications
                    </label>
                  </div>

                  <div>
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.enableAnimations}
                        onChange={(e) => setConfig(prev => ({ ...prev, enableAnimations: e.target.checked }))}
                        className="mr-2"
                      />
                      Enable Animations
                    </label>
                  </div>
                </div>
              </div>
            )}
          </DragDropContext>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <button
            onClick={resetConfiguration}
            className="text-red-600 hover:text-red-800 font-medium"
          >
            Reset to Default
          </button>
          
          <div className="flex space-x-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={saveConfiguration}
              className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 font-medium"
            >
              Save Configuration
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedConfigPanel;
