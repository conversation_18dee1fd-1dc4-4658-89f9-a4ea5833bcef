import React, { useState } from 'react';

const AssigneeGroupManager = ({ config, onConfigChange, theme, dashboardData, getAllAssignees }) => {
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingGroup, setEditingGroup] = useState(null);
  const [selectedDateRange, setSelectedDateRange] = useState('all');
  const [customDateFrom, setCustomDateFrom] = useState('');
  const [customDateTo, setCustomDateTo] = useState('');
  const [newGroupName, setNewGroupName] = useState('');
  const [newGroupDescription, setNewGroupDescription] = useState('');
  const [selectedAssignees, setSelectedAssignees] = useState([]);
  const [assigneeSearch, setAssigneeSearch] = useState('');
  const [loadingAssignees, setLoadingAssignees] = useState(false);
  const [apiAssignees, setApiAssignees] = useState([]);

  // Fetch assignees from API if needed
  React.useEffect(() => {
    const fetchAssignees = async () => {
      if (getAllAssignees && typeof getAllAssignees === 'function') {
        const dashboardAssignees = getAllAssignees();
        if (dashboardAssignees && dashboardAssignees.length > 0) {
          return; // We have assignees from dashboard data
        }
      }

      // Try to fetch from API
      setLoadingAssignees(true);
      try {
        const response = await fetch('/api/dashboard/assignees');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.assignees) {
            setApiAssignees(data.assignees.map(assignee => ({
              id: assignee.id || assignee.name.toLowerCase().replace(/\s+/g, '.'),
              name: assignee.name,
              email: assignee.email || `${assignee.name.toLowerCase().replace(/\s+/g, '.')}@company.com`
            })));
          }
        }
      } catch (error) {
        console.log('Could not fetch assignees from API, using fallback data');
      } finally {
        setLoadingAssignees(false);
      }
    };

    fetchAssignees();
  }, [getAllAssignees]);

  // Get real assignees from Jira data
  const availableAssignees = React.useMemo(() => {
    if (getAllAssignees && typeof getAllAssignees === 'function') {
      const dashboardAssignees = getAllAssignees();
      if (dashboardAssignees && dashboardAssignees.length > 0) {
        // Use the real assignees from dashboard data
        return dashboardAssignees.map(assignee => ({
          id: assignee.key || assignee.displayName.toLowerCase().replace(/\s+/g, '.'),
          name: assignee.displayName,
          email: assignee.emailAddress || `${assignee.displayName.toLowerCase().replace(/\s+/g, '.')}@company.com`
        }));
      }
    }

    // Use API assignees if available
    if (apiAssignees.length > 0) {
      return apiAssignees;
    }

    // Fallback to mock data if no real assignees are available
    return [
      { id: 'john.doe', name: 'John Doe', email: '<EMAIL>' },
      { id: 'jane.smith', name: 'Jane Smith', email: '<EMAIL>' },
      { id: 'mike.johnson', name: 'Mike Johnson', email: '<EMAIL>' },
      { id: 'sarah.wilson', name: 'Sarah Wilson', email: '<EMAIL>' },
      { id: 'david.brown', name: 'David Brown', email: '<EMAIL>' }
    ];
  }, [getAllAssignees, apiAssignees]);

  const filteredAssignees = availableAssignees.filter(assignee =>
    assignee.name.toLowerCase().includes(assigneeSearch.toLowerCase()) ||
    assignee.email.toLowerCase().includes(assigneeSearch.toLowerCase())
  );

  const createAssigneeGroup = () => {
    if (!newGroupName.trim()) return;

    // Convert assignee IDs to display names for filtering compatibility
    const assigneeNames = selectedAssignees.map(assigneeId => {
      const assignee = availableAssignees.find(a => a.id === assigneeId);
      return assignee ? assignee.name : assigneeId;
    });

    const newGroup = {
      id: `group-${Date.now()}`,
      name: newGroupName.trim(),
      assignees: assigneeNames, // Store display names for filtering compatibility
      assigneeIds: [...selectedAssignees], // Keep IDs for editing
      description: newGroupDescription.trim()
    };

    console.log('Creating assignee group:', newGroup);
    console.log('Current config:', config);

    onConfigChange({
      ...config,
      assigneeGroups: [...(config.assigneeGroups || []), newGroup]
    });

    // Reset form
    setNewGroupName('');
    setNewGroupDescription('');
    setSelectedAssignees([]);
    setShowCreateForm(false);
  };

  const updateAssigneeGroup = () => {
    if (!editingGroup || !newGroupName.trim()) return;

    // Convert assignee IDs to display names for filtering compatibility
    const assigneeNames = selectedAssignees.map(assigneeId => {
      const assignee = availableAssignees.find(a => a.id === assigneeId);
      return assignee ? assignee.name : assigneeId;
    });

    onConfigChange({
      ...config,
      assigneeGroups: config.assigneeGroups.map(group =>
        group.id === editingGroup.id
          ? {
              ...group,
              name: newGroupName.trim(),
              description: newGroupDescription.trim(),
              assignees: assigneeNames, // Store display names for filtering compatibility
              assigneeIds: [...selectedAssignees] // Keep IDs for editing
            }
          : group
      )
    });

    // Reset form
    setEditingGroup(null);
    setNewGroupName('');
    setNewGroupDescription('');
    setSelectedAssignees([]);
  };

  const deleteAssigneeGroup = (groupId) => {
    if (confirm('Are you sure you want to delete this assignee group?')) {
      onConfigChange({
        ...config,
        assigneeGroups: config.assigneeGroups.filter(group => group.id !== groupId)
      });
    }
  };

  const applyAssigneeGroupToAll = (groupId) => {
    const group = config.assigneeGroups?.find(g => g.id === groupId);
    if (!group) return;

    console.log('Applying assignee group to all:', group);
    console.log('Group assignees:', group.assignees);

    const updatedConfig = { ...config };

    // Apply to status cards
    if (updatedConfig.statusCards) {
      updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: [...group.assignees]
      }));
    }

    // Apply to priority cards
    if (updatedConfig.priorityCards) {
      updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: [...group.assignees]
      }));
    }

    // Apply to organization cards
    if (updatedConfig.organizationCards) {
      updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
        ...card,
        assigneeFilter: 'specific',
        selectedAssignees: [...group.assignees]
      }));
    }

    // Apply to charts
    if (updatedConfig.charts) {
      Object.keys(updatedConfig.charts).forEach(chartId => {
        updatedConfig.charts[chartId] = {
          ...updatedConfig.charts[chartId],
          assigneeFilter: 'specific',
          selectedAssignees: [...group.assignees]
        };
      });
    }

    onConfigChange(updatedConfig);
    alert(`Applied "${group.name}" assignee group to all cards and charts!`);
  };

  const clearAllAssigneeFilters = () => {
    if (confirm('Are you sure you want to clear all assignee filters from cards and charts?')) {
      const updatedConfig = { ...config };

      // Clear status cards
      if (updatedConfig.statusCards) {
        updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
          ...card,
          assigneeFilter: 'all',
          selectedAssignees: []
        }));
      }

      // Clear priority cards
      if (updatedConfig.priorityCards) {
        updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
          ...card,
          assigneeFilter: 'all',
          selectedAssignees: []
        }));
      }

      // Clear organization cards
      if (updatedConfig.organizationCards) {
        updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
          ...card,
          assigneeFilter: 'all',
          selectedAssignees: []
        }));
      }

      // Clear charts
      if (updatedConfig.charts) {
        Object.keys(updatedConfig.charts).forEach(chartId => {
          updatedConfig.charts[chartId] = {
            ...updatedConfig.charts[chartId],
            assigneeFilter: 'all',
            selectedAssignees: []
          };
        });
      }

      onConfigChange(updatedConfig);
      alert('Cleared all assignee filters!');
    }
  };

  // Date Range Application Functions
  const applyDateRangeToAll = () => {
    if (!selectedDateRange) return;

    // Validate custom date range
    if (selectedDateRange === 'custom') {
      if (!customDateFrom || !customDateTo) {
        alert('Please select both start and end dates for custom range.');
        return;
      }
      if (new Date(customDateFrom) > new Date(customDateTo)) {
        alert('Start date must be before end date.');
        return;
      }
    }

    const dateConfig = {
      dateFilter: selectedDateRange,
      customDateFrom: selectedDateRange === 'custom' ? customDateFrom : '',
      customDateTo: selectedDateRange === 'custom' ? customDateTo : ''
    };

    const updatedConfig = { ...config };

    // Apply to status cards
    if (updatedConfig.statusCards) {
      updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
        ...card,
        ...dateConfig
      }));
    }

    // Apply to priority cards
    if (updatedConfig.priorityCards) {
      updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
        ...card,
        ...dateConfig
      }));
    }

    // Apply to organization cards
    if (updatedConfig.organizationCards) {
      updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
        ...card,
        ...dateConfig
      }));
    }

    // Apply to charts
    if (updatedConfig.charts) {
      Object.keys(updatedConfig.charts).forEach(chartId => {
        updatedConfig.charts[chartId] = {
          ...updatedConfig.charts[chartId],
          ...dateConfig
        };
      });
    }

    console.log('Applying date range to all:', dateConfig);
    console.log('Updated config:', updatedConfig);

    onConfigChange(updatedConfig);

    const dateRangeText = getDateRangeText(selectedDateRange, customDateFrom, customDateTo);
    alert(`Applied "${dateRangeText}" date range to all cards and charts!`);
  };

  const clearAllDateFilters = () => {
    if (confirm('Are you sure you want to clear all date filters from cards and charts?')) {
      const updatedConfig = { ...config };

      const clearDateConfig = {
        dateFilter: 'all',
        customDateFrom: '',
        customDateTo: ''
      };

      // Clear status cards
      if (updatedConfig.statusCards) {
        updatedConfig.statusCards = updatedConfig.statusCards.map(card => ({
          ...card,
          ...clearDateConfig
        }));
      }

      // Clear priority cards
      if (updatedConfig.priorityCards) {
        updatedConfig.priorityCards = updatedConfig.priorityCards.map(card => ({
          ...card,
          ...clearDateConfig
        }));
      }

      // Clear organization cards
      if (updatedConfig.organizationCards) {
        updatedConfig.organizationCards = updatedConfig.organizationCards.map(card => ({
          ...card,
          ...clearDateConfig
        }));
      }

      // Clear charts
      if (updatedConfig.charts) {
        Object.keys(updatedConfig.charts).forEach(chartId => {
          updatedConfig.charts[chartId] = {
            ...updatedConfig.charts[chartId],
            ...clearDateConfig
          };
        });
      }

      onConfigChange(updatedConfig);
      alert('Cleared all date filters!');
    }
  };

  const getDateRangeText = (dateFilter, fromDate, toDate) => {
    switch (dateFilter) {
      case 'today': return 'Today';
      case 'yesterday': return 'Yesterday';
      case 'last7days': return 'Last 7 Days';
      case 'last30days': return 'Last 30 Days';
      case 'last90days': return 'Last 90 Days';
      case 'thisweek': return 'This Week';
      case 'thismonth': return 'This Month';
      case 'thisyear': return 'This Year';
      case 'lastyear': return 'Last Year';
      case 'custom': return fromDate && toDate ? `${fromDate} to ${toDate}` : 'Custom Range';
      default: return 'All Time';
    }
  };

  const startEdit = (group) => {
    setEditingGroup(group);
    setNewGroupName(group.name);
    setNewGroupDescription(group.description || '');
    // Use assigneeIds if available, otherwise convert assignee names back to IDs
    const assigneeIds = group.assigneeIds || group.assignees.map(assigneeName => {
      const assignee = availableAssignees.find(a => a.name === assigneeName);
      return assignee ? assignee.id : assigneeName;
    });
    setSelectedAssignees([...assigneeIds]);
    setShowCreateForm(true);
  };

  const cancelEdit = () => {
    setEditingGroup(null);
    setNewGroupName('');
    setNewGroupDescription('');
    setSelectedAssignees([]);
    setShowCreateForm(false);
  };

  const toggleAssigneeSelection = (assigneeId) => {
    setSelectedAssignees(prev =>
      prev.includes(assigneeId)
        ? prev.filter(id => id !== assigneeId)
        : [...prev, assigneeId]
    );
  };

  return (
    <div style={{ marginBottom: '24px' }}>
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '16px'
      }}>
        <h4 style={{
          margin: 0,
          color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
          fontSize: '16px',
          fontWeight: '600'
        }}>
          👥 Assignee Groups
        </h4>
        <div style={{ display: 'flex', gap: '8px' }}>
          <button
            onClick={() => setShowCreateForm(true)}
            style={{
              padding: '6px 12px',
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500'
            }}
          >
            ➕ Create Group
          </button>
          <button
            onClick={clearAllAssigneeFilters}
            style={{
              padding: '6px 12px',
              backgroundColor: '#ef4444',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: '500'
            }}
          >
            🗑️ Clear All Filters
          </button>
        </div>
      </div>

      {/* Existing Groups */}
      <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap', marginBottom: '16px' }}>
        {(config.assigneeGroups || []).map(group => (
          <div
            key={group.id}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              padding: '8px 12px',
              backgroundColor: theme === 'dark' ? '#374151' : '#f3f4f6',
              border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
              borderRadius: '8px'
            }}
          >
            <div style={{ flex: 1 }}>
              <div style={{
                fontSize: '14px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                {group.name}
              </div>
              <div style={{
                fontSize: '12px',
                color: theme === 'dark' ? '#9ca3af' : '#6b7280'
              }}>
                {group.assignees?.length || 0} assignees
              </div>
            </div>
            <button
              onClick={() => applyAssigneeGroupToAll(group.id)}
              style={{
                padding: '4px 8px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px'
              }}
              title="Apply to all cards and charts"
            >
              Apply
            </button>
            <button
              onClick={() => startEdit(group)}
              style={{
                padding: '4px 8px',
                backgroundColor: '#f59e0b',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Edit
            </button>
            <button
              onClick={() => deleteAssigneeGroup(group.id)}
              style={{
                padding: '4px 8px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '11px'
              }}
            >
              Delete
            </button>
          </div>
        ))}
      </div>

      {/* Global Date Range Management */}
      <div style={{ marginTop: '32px', marginBottom: '24px' }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}>
          <h4 style={{
            margin: 0,
            color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
            fontSize: '16px',
            fontWeight: '600'
          }}>
            📅 Global Date Range
          </h4>
          <div style={{ display: 'flex', gap: '8px' }}>
            <button
              onClick={applyDateRangeToAll}
              disabled={
                !selectedDateRange ||
                selectedDateRange === 'all' ||
                (selectedDateRange === 'custom' && (!customDateFrom || !customDateTo))
              }
              style={{
                padding: '6px 12px',
                backgroundColor: (
                  !selectedDateRange ||
                  selectedDateRange === 'all' ||
                  (selectedDateRange === 'custom' && (!customDateFrom || !customDateTo))
                ) ? '#9ca3af' : '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: (
                  !selectedDateRange ||
                  selectedDateRange === 'all' ||
                  (selectedDateRange === 'custom' && (!customDateFrom || !customDateTo))
                ) ? 'not-allowed' : 'pointer',
                fontSize: '12px',
                fontWeight: '500'
              }}
            >
              📊 Apply to All
            </button>
            <button
              onClick={clearAllDateFilters}
              style={{
                padding: '6px 12px',
                backgroundColor: '#ef4444',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '12px',
                fontWeight: '500'
              }}
            >
              🗑️ Clear All Dates
            </button>
          </div>
        </div>

        {/* Date Range Selection */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: selectedDateRange === 'custom' ? '1fr 1fr 1fr' : '1fr',
          gap: '12px',
          padding: '16px',
          backgroundColor: theme === 'dark' ? '#374151' : '#f8fafc',
          border: `1px solid ${theme === 'dark' ? '#4b5563' : '#e2e8f0'}`,
          borderRadius: '8px'
        }}>
          <div>
            <label style={{
              display: 'block',
              marginBottom: '6px',
              fontSize: '14px',
              fontWeight: '500',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
            }}>
              Select Date Range
            </label>
            <select
              value={selectedDateRange}
              onChange={(e) => {
                setSelectedDateRange(e.target.value);
                if (e.target.value !== 'custom') {
                  setCustomDateFrom('');
                  setCustomDateTo('');
                }
              }}
              style={{
                width: '100%',
                padding: '8px 12px',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '6px',
                backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                fontSize: '14px'
              }}
            >
              <option value="all">All Time</option>
              <option value="today">Today</option>
              <option value="yesterday">Yesterday</option>
              <option value="last7days">Last 7 Days</option>
              <option value="last30days">Last 30 Days</option>
              <option value="last90days">Last 90 Days</option>
              <option value="thisweek">This Week</option>
              <option value="thismonth">This Month</option>
              <option value="thisyear">This Year</option>
              <option value="lastyear">Last Year</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>

          {/* Custom Date Range Inputs */}
          {selectedDateRange === 'custom' && (
            <>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  From Date
                </label>
                <input
                  type="date"
                  value={customDateFrom}
                  onChange={(e) => setCustomDateFrom(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                />
              </div>
              <div>
                <label style={{
                  display: 'block',
                  marginBottom: '6px',
                  fontSize: '14px',
                  fontWeight: '500',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                }}>
                  To Date
                </label>
                <input
                  type="date"
                  value={customDateTo}
                  onChange={(e) => setCustomDateTo(e.target.value)}
                  style={{
                    width: '100%',
                    padding: '8px 12px',
                    border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                    borderRadius: '6px',
                    backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
                    color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                    fontSize: '14px'
                  }}
                />
              </div>
            </>
          )}
        </div>

        {/* Date Range Preview */}
        {selectedDateRange && selectedDateRange !== 'all' && (
          <div style={{
            marginTop: '12px',
            padding: '12px',
            backgroundColor: theme === 'dark' ? '#1f2937' : '#f0f9ff',
            border: `1px solid ${theme === 'dark' ? '#374151' : '#bae6fd'}`,
            borderRadius: '6px'
          }}>
            <div style={{
              fontSize: '14px',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
              fontWeight: '500'
            }}>
              📅 Selected Range: {getDateRangeText(selectedDateRange, customDateFrom, customDateTo)}
            </div>
            <div style={{
              fontSize: '12px',
              color: theme === 'dark' ? '#9ca3af' : '#6b7280',
              marginTop: '4px'
            }}>
              This will be applied to all status cards, priority cards, organization cards, and charts
            </div>
          </div>
        )}
      </div>

      {/* Create/Edit Form */}
      {showCreateForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }}>
          <div style={{
            backgroundColor: theme === 'dark' ? '#1f2937' : 'white',
            padding: '24px',
            borderRadius: '12px',
            maxWidth: '600px',
            width: '90%',
            maxHeight: '80vh',
            overflow: 'auto'
          }}>
            <h3 style={{
              margin: '0 0 20px 0',
              color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
              fontSize: '18px'
            }}>
              {editingGroup ? 'Edit Assignee Group' : 'Create Assignee Group'}
            </h3>

            {/* Group Name */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '14px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Group Name *
              </label>
              <input
                type="text"
                value={newGroupName}
                onChange={(e) => setNewGroupName(e.target.value)}
                placeholder="Enter group name..."
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '6px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px'
                }}
              />
            </div>

            {/* Group Description */}
            <div style={{ marginBottom: '16px' }}>
              <label style={{
                display: 'block',
                marginBottom: '4px',
                fontSize: '14px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Description
              </label>
              <textarea
                value={newGroupDescription}
                onChange={(e) => setNewGroupDescription(e.target.value)}
                placeholder="Enter group description..."
                rows={3}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '6px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px',
                  resize: 'vertical'
                }}
              />
            </div>

            {/* Assignee Selection */}
            <div style={{ marginBottom: '20px' }}>
              <label style={{
                display: 'block',
                marginBottom: '8px',
                fontSize: '14px',
                fontWeight: '500',
                color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
              }}>
                Select Assignees ({selectedAssignees.length} selected)
              </label>

              {/* Search */}
              <input
                type="text"
                value={assigneeSearch}
                onChange={(e) => setAssigneeSearch(e.target.value)}
                placeholder="Search assignees..."
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                  borderRadius: '6px',
                  backgroundColor: theme === 'dark' ? '#374151' : 'white',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  fontSize: '14px',
                  marginBottom: '12px'
                }}
              />

              {/* Assignee List */}
              <div style={{
                maxHeight: '200px',
                overflow: 'auto',
                border: `1px solid ${theme === 'dark' ? '#4b5563' : '#d1d5db'}`,
                borderRadius: '6px',
                padding: '8px'
              }}>
                {loadingAssignees ? (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '40px',
                    color: theme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}>
                    <div style={{ marginRight: '8px' }}>🔄</div>
                    Loading assignees...
                  </div>
                ) : filteredAssignees.length === 0 ? (
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '40px',
                    color: theme === 'dark' ? '#9ca3af' : '#6b7280'
                  }}>
                    {assigneeSearch ? 'No assignees match your search' : 'No assignees available'}
                  </div>
                ) : (
                  filteredAssignees.map(assignee => (
                  <label
                    key={assignee.id}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px',
                      cursor: 'pointer',
                      borderRadius: '4px',
                      backgroundColor: selectedAssignees.includes(assignee.id)
                        ? (theme === 'dark' ? '#374151' : '#f3f4f6')
                        : 'transparent'
                    }}
                  >
                    <input
                      type="checkbox"
                      checked={selectedAssignees.includes(assignee.id)}
                      onChange={() => toggleAssigneeSelection(assignee.id)}
                      style={{ marginRight: '8px' }}
                    />
                    <div>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: '500',
                        color: theme === 'dark' ? '#e5e7eb' : '#1f2937'
                      }}>
                        {assignee.name}
                      </div>
                      <div style={{
                        fontSize: '12px',
                        color: theme === 'dark' ? '#9ca3af' : '#6b7280'
                      }}>
                        {assignee.email}
                      </div>
                    </div>
                  </label>
                  ))
                )}
              </div>
            </div>

            {/* Actions */}
            <div style={{ display: 'flex', gap: '12px', justifyContent: 'flex-end' }}>
              <button
                onClick={cancelEdit}
                style={{
                  padding: '8px 16px',
                  backgroundColor: theme === 'dark' ? '#4b5563' : '#f3f4f6',
                  color: theme === 'dark' ? '#e5e7eb' : '#1f2937',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Cancel
              </button>
              <button
                onClick={editingGroup ? updateAssigneeGroup : createAssigneeGroup}
                disabled={!newGroupName.trim()}
                style={{
                  padding: '8px 16px',
                  backgroundColor: !newGroupName.trim() ? '#9ca3af' : '#10b981',
                  color: 'white',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: !newGroupName.trim() ? 'not-allowed' : 'pointer',
                  fontSize: '14px'
                }}
              >
                {editingGroup ? 'Update Group' : 'Create Group'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssigneeGroupManager;
