import axios from 'axios';
import mongoDbService from './mongoDbService.js';

class EnhancedSyncService {
  constructor() {
    this.api = null;
    this.isInitialized = false;
    this.syncInProgress = false;
    this.backgroundSyncInterval = null;
    this.backgroundSyncIntervalMs = 10000; // 10 seconds
    this.batchSize = 50; // Reduced batch size to prevent rate limiting
    this.maxRetries = 5; // Increased retries
    this.retryDelay = 10000; // Increased delay to 10 seconds
    this.lastRequestTime = 0;
    this.requestDelay = 1000; // 1 second between requests
    this.consecutiveErrors = 0;
    this.maxConsecutiveErrors = 10; // Increased tolerance
    this.circuitBreakerOpen = false;
    this.circuitBreakerOpenTime = null;
    this.circuitBreakerTimeout = 60000; // Reduced to 1 minute for faster recovery
  }

  // Rate limiting helper with circuit breaker
  async delayedRequest(requestFn) {
    // Check circuit breaker
    if (this.circuitBreakerOpen) {
      const now = Date.now();
      if (now - this.circuitBreakerOpenTime < this.circuitBreakerTimeout) {
        throw new Error('Circuit breaker is open. Service temporarily unavailable.');
      } else {
        // Try to close circuit breaker
        console.log('🔄 Attempting to close circuit breaker...');
        this.circuitBreakerOpen = false;
        this.circuitBreakerOpenTime = null;
        this.consecutiveErrors = 0;
      }
    }

    const now = Date.now();
    const timeSinceLastRequest = now - this.lastRequestTime;

    if (timeSinceLastRequest < this.requestDelay) {
      await new Promise(resolve => setTimeout(resolve, this.requestDelay - timeSinceLastRequest));
    }

    this.lastRequestTime = Date.now();

    try {
      const result = await requestFn();
      this.consecutiveErrors = 0; // Reset error count on success

      // Close circuit breaker on success
      if (this.circuitBreakerOpen) {
        console.log('✅ Circuit breaker closed - service recovered');
        this.circuitBreakerOpen = false;
        this.circuitBreakerOpenTime = null;
      }

      return result;
    } catch (error) {
      this.consecutiveErrors++;

      // Open circuit breaker if too many consecutive errors
      if (this.consecutiveErrors >= this.maxConsecutiveErrors && !this.circuitBreakerOpen) {
        console.error(`🚨 Opening circuit breaker after ${this.consecutiveErrors} consecutive errors`);
        this.circuitBreakerOpen = true;
        this.circuitBreakerOpenTime = Date.now();
      }

      // If too many consecutive errors, increase delay
      if (this.consecutiveErrors >= this.maxConsecutiveErrors) {
        console.warn(`⚠️ ${this.consecutiveErrors} consecutive errors, increasing delay to ${this.retryDelay * 2}ms`);
        await new Promise(resolve => setTimeout(resolve, this.retryDelay * 2));
      }

      throw error;
    }
  }

  // Check if service is healthy
  isServiceHealthy() {
    return !this.circuitBreakerOpen && this.consecutiveErrors < this.maxConsecutiveErrors;
  }

  // Get service status
  getServiceStatus() {
    return {
      healthy: this.isServiceHealthy(),
      circuitBreakerOpen: this.circuitBreakerOpen,
      consecutiveErrors: this.consecutiveErrors,
      lastRequestTime: this.lastRequestTime,
      circuitBreakerOpenTime: this.circuitBreakerOpenTime
    };
  }

  // Manual circuit breaker reset
  resetCircuitBreaker() {
    console.log('🔄 Manually resetting circuit breaker...');
    this.circuitBreakerOpen = false;
    this.circuitBreakerOpenTime = null;
    this.consecutiveErrors = 0;
    console.log('✅ Circuit breaker reset successfully');
  }

  // Force service recovery
  async forceServiceRecovery() {
    console.log('🔧 Forcing service recovery...');
    this.resetCircuitBreaker();

    try {
      // Test connection with a simple health check
      await this.delayedRequest(async () => {
        return await this.api.get('/health');
      });
      console.log('✅ Service recovery successful');
      return true;
    } catch (error) {
      console.error('❌ Service recovery failed:', error.message);
      return false;
    }
  }

  // Initialize the service
  async initialize() {
    try {
      console.log('🔄 Initializing Enhanced Sync Service...');
      
      // Set up API client
      this.api = axios.create({
        baseURL: process.env.JIRA_BASE_URL || 'http://localhost:3001/api',
        timeout: 60000,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Ensure MongoDB is connected
      if (!await mongoDbService.isAvailable()) {
        await mongoDbService.connect();
      }

      this.isInitialized = true;
      console.log('✅ Enhanced Sync Service initialized');
      
      // Start background sync
      this.startBackgroundSync();
      
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Enhanced Sync Service:', error.message);
      this.isInitialized = false;
      return false;
    }
  }

  // Start continuous background sync
  startBackgroundSync() {
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
    }

    console.log(`🔄 Starting background sync every ${this.backgroundSyncIntervalMs / 1000} seconds`);
    
    this.backgroundSyncInterval = setInterval(async () => {
      try {
        // Check service health before attempting sync
        if (!this.isServiceHealthy()) {
          const status = this.getServiceStatus();
          console.warn('⚠️ Service unhealthy, skipping background sync:', status);
          return;
        }

        await this.performIncrementalSync();
      } catch (error) {
        console.error('❌ Background sync error:', error.message);
      }
    }, this.backgroundSyncIntervalMs);
  }

  // Stop background sync
  stopBackgroundSync() {
    if (this.backgroundSyncInterval) {
      clearInterval(this.backgroundSyncInterval);
      this.backgroundSyncInterval = null;
      console.log('⏹️ Background sync stopped');
    }
  }

  // Get total ticket count from Jira
  async getJiraTotalCount() {
    try {
      const response = await this.delayedRequest(async () => {
        return await this.api.get('/jira/search', {
          params: {
            jql: 'project is not EMPTY ORDER BY created DESC',
            maxResults: 0
          }
        });
      });
      return response.data.total || 0;
    } catch (error) {
      console.error('❌ Failed to get Jira total count:', error.message);
      return 0;
    }
  }

  // Get all ticket keys from Jira (for differential sync)
  async getAllJiraTicketKeys() {
    try {
      console.log('📋 Fetching all ticket keys from Jira...');
      const allKeys = [];
      const batchSize = 1000;
      let startAt = 0;
      let hasMore = true;

      while (hasMore) {
        const response = await this.delayedRequest(async () => {
          return await this.api.get('/jira/search', {
            params: {
              jql: 'project is not EMPTY ORDER BY created DESC',
              fields: 'key',
              maxResults: batchSize,
              startAt: startAt
            }
          });
        });

        const tickets = response.data.issues || [];
        const keys = tickets.map(ticket => ticket.key);
        allKeys.push(...keys);

        hasMore = tickets.length === batchSize;
        startAt += batchSize;

        if (startAt % 10000 === 0) {
          console.log(`   - Fetched ${allKeys.length} ticket keys so far...`);
        }
      }

      console.log(`✅ Fetched ${allKeys.length} total ticket keys from Jira`);
      return allKeys;
    } catch (error) {
      console.error('❌ Failed to get Jira ticket keys:', error.message);
      return [];
    }
  }

  // Fetch specific tickets by keys from Jira
  async fetchTicketsByKeys(ticketKeys, retryCount = 0) {
    if (ticketKeys.length === 0) {
      return [];
    }

    try {
      console.log(`📥 Fetching ${ticketKeys.length} specific tickets from Jira...`);
      
      // Build JQL query with specific keys
      const keysChunk = ticketKeys.slice(0, this.batchSize); // Use configurable batch size
      const jql = `key in (${keysChunk.map(key => `"${key}"`).join(',')}) ORDER BY created DESC`;

      // Use rate-limited request
      const response = await this.delayedRequest(async () => {
        return await this.api.get('/jira/search', {
          params: {
            jql: jql,
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: this.batchSize
          }
        });
      });

      const tickets = response.data.issues || [];
      console.log(`✅ Fetched ${tickets.length} tickets for keys batch`);
      
      // If there are more keys, fetch them recursively
      if (ticketKeys.length > this.batchSize) {
        const remainingKeys = ticketKeys.slice(this.batchSize);
        const remainingTickets = await this.fetchTicketsByKeys(remainingKeys);
        return [...tickets, ...remainingTickets];
      }
      
      return tickets;
    } catch (error) {
      console.error(`❌ Failed to fetch tickets by keys (attempt ${retryCount + 1}):`, error.message);
      
      if (retryCount < this.maxRetries) {
        const delay = this.retryDelay * Math.pow(2, retryCount); // Exponential backoff
        console.log(`🔄 Retrying in ${delay / 1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.fetchTicketsByKeys(ticketKeys, retryCount + 1);
      }
      
      throw error;
    }
  }

  // Perform comprehensive database verification and sync
  async performComprehensiveSync() {
    if (this.syncInProgress) {
      console.log('⏳ Comprehensive sync already in progress, skipping...');
      return null;
    }

    this.syncInProgress = true;
    
    try {
      console.log('🔍 Starting comprehensive database verification and sync...');
      
      // Step 1: Get total count from Jira
      const jiraTotalCount = await this.getJiraTotalCount();
      if (jiraTotalCount === 0) {
        throw new Error('No tickets found in Jira or API error');
      }
      
      // Step 2: Verify database integrity
      const verification = await mongoDbService.verifyDatabaseIntegrity(jiraTotalCount);
      console.log('🔍 Database verification result:', verification);
      
      // Step 3: Decide sync strategy
      if (verification.isValid) {
        console.log('✅ Database is synchronized, performing incremental sync only');
        return await this.performIncrementalSync();
      } else {
        console.log('🔄 Database needs synchronization, performing differential sync');
        return await this.performDifferentialSync(jiraTotalCount);
      }
      
    } catch (error) {
      console.error('❌ Comprehensive sync failed:', error.message);
      throw error;
    } finally {
      this.syncInProgress = false;
    }
  }

  // Perform differential sync (fetch only missing tickets)
  async performDifferentialSync(jiraTotalCount) {
    try {
      console.log('🔄 Starting differential sync...');
      
      // Step 1: Get all ticket keys from Jira
      const jiraKeys = await this.getAllJiraTicketKeys();
      
      // Step 2: Find missing keys
      const missingKeys = await mongoDbService.getMissingTicketKeys(jiraKeys);
      
      if (missingKeys.length === 0) {
        console.log('✅ No missing tickets found');
        await this.updateSyncMetadata(jiraTotalCount, 'differential_complete');
        return await mongoDbService.loadAllTickets();
      }
      
      console.log(`📥 Fetching ${missingKeys.length} missing tickets in batches...`);
      
      // Step 3: Fetch missing tickets in batches
      const allMissingTickets = [];
      const batchSize = 100;
      
      for (let i = 0; i < missingKeys.length; i += batchSize) {
        const batch = missingKeys.slice(i, i + batchSize);
        console.log(`📥 Fetching batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(missingKeys.length / batchSize)} (${batch.length} tickets)`);
        
        const batchTickets = await this.fetchTicketsByKeys(batch);
        allMissingTickets.push(...batchTickets);
        
        // Save batch immediately to avoid memory issues
        if (batchTickets.length > 0) {
          await mongoDbService.saveTickets(batchTickets);
        }
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      console.log(`✅ Differential sync completed. Fetched ${allMissingTickets.length} missing tickets`);
      
      // Update sync metadata
      await this.updateSyncMetadata(jiraTotalCount, 'differential_complete');
      
      return await mongoDbService.loadAllTickets();
      
    } catch (error) {
      console.error('❌ Differential sync failed:', error.message);
      await this.updateSyncMetadata(0, 'differential_failed');
      throw error;
    }
  }

  // Perform incremental sync (fetch only new/updated tickets)
  async performIncrementalSync() {
    if (this.syncInProgress) {
      return null;
    }

    try {
      // Get last sync timestamp
      const metadata = await mongoDbService.getSyncMetadata();
      const lastSyncTime = metadata?.lastSyncTimestamp;

      if (!lastSyncTime) {
        console.log('🆕 No previous sync found, performing comprehensive sync');
        return await this.performComprehensiveSync();
      }

      // Check for tickets updated since last sync
      const syncSince = new Date(lastSyncTime.getTime() - 60000); // 1 minute buffer
      const formattedDate = this.formatDateForJira(syncSince);
      const jqlQuery = `updated >= "${formattedDate}" ORDER BY updated DESC`;

      console.log(`🔄 Checking for updates since: ${syncSince.toISOString()}`);

      const response = await this.delayedRequest(async () => {
        return await this.api.get('/jira/search', {
          params: {
            jql: jqlQuery,
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: 1000
          }
        });
      });

      const updatedTickets = response.data.issues || [];

      if (updatedTickets.length === 0) {
        console.log('✅ No new/updated tickets found');
        return null;
      }

      console.log(`📥 Found ${updatedTickets.length} updated tickets, saving to database...`);

      // Save updated tickets
      const saveSuccess = await mongoDbService.saveTickets(updatedTickets);
      if (saveSuccess) {
        await this.updateSyncMetadata(0, 'incremental_complete');
        console.log('✅ Incremental sync completed successfully');
      }

      return updatedTickets;

    } catch (error) {
      console.error('❌ Incremental sync failed:', error.message);
      return null;
    }
  }

  // Resume sync from server restart
  async resumeSyncFromRestart() {
    try {
      console.log('🔄 Resuming sync after server restart...');

      const metadata = await mongoDbService.getSyncMetadata();

      if (!metadata || !metadata.lastSyncTimestamp) {
        console.log('🆕 No previous sync state found, starting fresh comprehensive sync');
        return await this.performComprehensiveSync();
      }

      const timeSinceLastSync = Date.now() - new Date(metadata.lastSyncTimestamp).getTime();
      const hoursSinceLastSync = timeSinceLastSync / (1000 * 60 * 60);

      console.log(`⏰ Last sync was ${hoursSinceLastSync.toFixed(1)} hours ago`);

      if (hoursSinceLastSync > 24) {
        console.log('🔄 Last sync was over 24 hours ago, performing comprehensive sync');
        return await this.performComprehensiveSync();
      } else {
        console.log('🔄 Recent sync found, performing incremental sync');
        return await this.performIncrementalSync();
      }

    } catch (error) {
      console.error('❌ Failed to resume sync from restart:', error.message);
      return await this.performComprehensiveSync();
    }
  }

  // Get sync status and statistics
  async getSyncStatus() {
    try {
      const metadata = await mongoDbService.getSyncMetadata();
      const dbStats = await mongoDbService.getStats();

      return {
        isInitialized: this.isInitialized,
        syncInProgress: this.syncInProgress,
        backgroundSyncActive: !!this.backgroundSyncInterval,
        lastSyncTimestamp: metadata?.lastSyncTimestamp,
        lastSyncStatus: metadata?.lastSyncStatus,
        totalTicketsInJira: metadata?.totalTicketsInJira || 0,
        totalTicketsInDb: metadata?.totalTicketsInDb || 0,
        dbStats: dbStats
      };
    } catch (error) {
      console.error('❌ Failed to get sync status:', error.message);
      return null;
    }
  }

  // Update sync metadata
  async updateSyncMetadata(jiraTotalCount, status) {
    const dbCount = await mongoDbService.collection.countDocuments();

    await mongoDbService.updateSyncMetadata({
      lastSyncTimestamp: new Date(),
      totalTicketsInJira: jiraTotalCount || 0,
      totalTicketsInDb: dbCount,
      lastSyncStatus: status
    });
  }

  // Format date for Jira API (YYYY-MM-DD HH:mm format)
  formatDateForJira(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // Cleanup and shutdown
  async shutdown() {
    console.log('🛑 Shutting down Enhanced Sync Service...');
    this.stopBackgroundSync();
    this.isInitialized = false;
  }
}

// Create singleton instance
const enhancedSyncService = new EnhancedSyncService();
export default enhancedSyncService;
