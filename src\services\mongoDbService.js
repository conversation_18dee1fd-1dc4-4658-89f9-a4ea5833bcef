// MongoDB Service for Jira Dashboard
// Handles database connection, schema, and CRUD operations

import { MongoClient } from 'mongodb';

class MongoDbService {
  constructor() {
    this.client = null;
    this.db = null;
    this.collection = null;
    this.isConnected = false;
    this.connectionString = process.env.MONGODB_URI || 'mongodb://localhost:27017/jira-dashboard';
    this.databaseName = process.env.MONGODB_DATABASE || 'jira-dashboard';
    this.collectionName = process.env.MONGODB_COLLECTION || 'tickets';
    this.isEnabled = process.env.MONGODB_ENABLED === 'true';
    this.connectionTimeout = parseInt(process.env.MONGODB_CONNECTION_TIMEOUT) || 10000;
    this.maxPoolSize = parseInt(process.env.MONGODB_MAX_POOL_SIZE) || 20;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 5000;

    // Enhanced connection options for production
    this.clientOptions = {
      maxPoolSize: this.maxPoolSize,
      minPoolSize: 5,
      serverSelectionTimeoutMS: this.connectionTimeout,
      socketTimeoutMS: 45000,
      connectTimeoutMS: 10000,
      heartbeatFrequencyMS: 10000,
      maxIdleTimeMS: 30000,
      retryWrites: true,
      retryReads: true,
      readPreference: 'primary',
      writeConcern: { w: 'majority', j: true },
      family: 4 // Use IPv4, skip trying IPv6
    };
  }

  // Initialize MongoDB connection with retry logic
  async connect() {
    if (!this.isEnabled) {
      console.log('📊 MongoDB is disabled in configuration');
      return false;
    }

    if (this.isConnected) {
      return true;
    }

    try {
      console.log('🔌 Connecting to MongoDB...');
      console.log('📍 Connection string:', this.connectionString.replace(/\/\/.*@/, '//***:***@'));

      this.client = new MongoClient(this.connectionString, this.clientOptions);
      await this.client.connect();

      // Test the connection
      await this.client.db('admin').command({ ping: 1 });

      this.db = this.client.db(this.databaseName);
      this.collection = this.db.collection(this.collectionName);
      this.isConnected = true;
      this.reconnectAttempts = 0; // Reset on successful connection

      console.log('✅ MongoDB connected successfully');
      console.log('📊 Database:', this.databaseName);
      console.log('📋 Collection:', this.collectionName);

      // Set up connection event listeners
      this.setupConnectionListeners();

      // Create indexes for performance
      await this.createIndexes();

      return true;
    } catch (error) {
      console.error('❌ MongoDB connection failed:', error.message);
      this.isConnected = false;

      // Attempt reconnection
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${this.reconnectDelay / 1000} seconds...`);

        await new Promise(resolve => setTimeout(resolve, this.reconnectDelay));
        return this.connect();
      } else {
        console.error('❌ Max reconnection attempts reached');
        return false;
      }
    }
  }

  // Setup connection event listeners for resilience
  setupConnectionListeners() {
    if (!this.client) return;

    this.client.on('serverHeartbeatFailed', (event) => {
      console.warn('⚠️ MongoDB heartbeat failed:', event.failure?.message);
    });

    this.client.on('serverClosed', (event) => {
      console.warn('⚠️ MongoDB server closed:', event.address);
      this.isConnected = false;
    });

    this.client.on('topologyDescriptionChanged', (event) => {
      const { newDescription, previousDescription } = event;
      if (newDescription.type !== previousDescription.type) {
        console.log(`🔄 MongoDB topology changed: ${previousDescription.type} -> ${newDescription.type}`);
      }
    });
  }

  // Create database indexes for performance
  async createIndexes() {
    try {
      console.log('🔍 Creating database indexes...');
      
      // Index on ticket key (unique)
      await this.collection.createIndex({ key: 1 }, { unique: true });
      
      // Index on created date for time-based queries
      await this.collection.createIndex({ 'fields.created': 1 });
      
      // Index on updated date for incremental sync
      await this.collection.createIndex({ 'fields.updated': 1 });
      
      // Index on project for filtering
      await this.collection.createIndex({ 'fields.project.key': 1 });
      
      // Index on status for filtering
      await this.collection.createIndex({ 'fields.status.name': 1 });
      
      // Index on assignee for filtering
      await this.collection.createIndex({ 'fields.assignee.displayName': 1 });
      
      // Compound index for efficient sync queries
      await this.collection.createIndex({ 
        'fields.updated': 1, 
        'fields.created': 1 
      });
      
      console.log('✅ Database indexes created successfully');
    } catch (error) {
      console.warn('⚠️ Index creation failed:', error.message);
    }
  }

  // Check if MongoDB is available and connected
  async isAvailable() {
    if (!this.isEnabled || !this.isConnected || !this.client) {
      return false;
    }

    try {
      await this.client.db('admin').command({ ping: 1 });
      return true;
    } catch (error) {
      console.warn('⚠️ MongoDB ping failed:', error.message);
      this.isConnected = false;
      return false;
    }
  }

  // Get database statistics
  async getStats() {
    if (!await this.isAvailable()) {
      return null;
    }

    try {
      const count = await this.collection.countDocuments();
      
      // Get latest ticket date
      const latestTicket = await this.collection.findOne(
        {}, 
        { sort: { 'fields.updated': -1 } }
      );
      
      return {
        isConnected: this.isConnected,
        totalTickets: count,
        storageSize: 0, // Not available without stats command
        avgObjSize: 0, // Not available without stats command
        indexes: 0, // Not available without stats command
        latestUpdate: latestTicket ? latestTicket.fields.updated : null,
        database: this.databaseName,
        collection: this.collectionName
      };
    } catch (error) {
      console.error('❌ Failed to get MongoDB stats:', error.message);
      return null;
    }
  }

  // Save tickets to MongoDB with enhanced error handling
  async saveTickets(tickets) {
    if (!await this.isAvailable()) {
      console.warn('⚠️ MongoDB not available for saving tickets');
      return false;
    }

    if (tickets.length === 0) {
      return true;
    }

    try {
      console.log('💾 Saving ' + tickets.length + ' tickets to MongoDB...');

      // Check which tickets already exist to get accurate insert/update counts
      const existingKeys = new Set();
      if (tickets.length > 0) {
        const keys = tickets.map(t => t.key);
        const existing = await this.collection.find(
          { key: { $in: keys } },
          { projection: { key: 1 } }
        ).toArray();
        existing.forEach(doc => existingKeys.add(doc.key));
      }

      // Calculate counts
      const newTicketsCount = tickets.filter(t => !existingKeys.has(t.key)).length;
      const updatedTicketsCount = tickets.filter(t => existingKeys.has(t.key)).length;

      // Use bulk operations for efficiency
      const bulkOps = tickets.map(ticket => ({
        replaceOne: {
          filter: { key: ticket.key },
          replacement: {
            ...ticket,
            _lastSynced: new Date(),
            _syncVersion: 1,
            _dataIntegrityHash: this.generateDataHash(ticket)
          },
          upsert: true
        }
      }));

      const result = await this.collection.bulkWrite(bulkOps, {
        ordered: false
      });

      console.log('✅ MongoDB bulk operation completed:');
      console.log('   - Inserted:', newTicketsCount);
      console.log('   - Updated:', updatedTicketsCount);
      console.log('   - Total processed:', tickets.length);

      return true;
    } catch (error) {
      console.error('❌ MongoDB save failed:', error.message);
      return false;
    }
  }

  // Fallback method without transactions for compatibility
  async saveTicketsWithoutTransaction(tickets) {
    try {
      console.log('💾 Saving ' + tickets.length + ' tickets to MongoDB (fallback mode)...');

      // Check which tickets already exist
      const existingKeys = new Set();
      if (tickets.length > 0) {
        const keys = tickets.map(t => t.key);
        const existing = await this.collection.find(
          { key: { $in: keys } },
          { projection: { key: 1 } }
        ).toArray();
        existing.forEach(doc => existingKeys.add(doc.key));
      }

      // Use bulk operations for efficiency
      const bulkOps = tickets.map(ticket => ({
        replaceOne: {
          filter: { key: ticket.key },
          replacement: {
            ...ticket,
            _lastSynced: new Date(),
            _syncVersion: 1,
            _dataIntegrityHash: this.generateDataHash(ticket)
          },
          upsert: true
        }
      }));

      const result = await this.collection.bulkWrite(bulkOps, { ordered: false });

      const newTicketsCount = tickets.filter(t => !existingKeys.has(t.key)).length;
      const updatedTicketsCount = tickets.filter(t => existingKeys.has(t.key)).length;

      console.log('✅ MongoDB bulk operation completed (fallback):');
      console.log('   - Inserted:', newTicketsCount);
      console.log('   - Updated:', updatedTicketsCount);
      console.log('   - Total processed:', tickets.length);

      return true;
    } catch (error) {
      console.error('❌ MongoDB fallback save failed:', error.message);
      return false;
    }
  }

  // Generate data integrity hash for verification
  generateDataHash(ticket) {
    try {
      const crypto = require('crypto');
      const dataString = JSON.stringify({
        key: ticket.key,
        summary: ticket.fields?.summary,
        status: ticket.fields?.status?.name,
        updated: ticket.fields?.updated
      });
      return crypto.createHash('md5').update(dataString).digest('hex');
    } catch (error) {
      return null; // Return null if hash generation fails
    }
  }

  // Load all tickets from MongoDB
  async loadAllTickets() {
    if (!await this.isAvailable()) {
      console.warn('⚠️ MongoDB not available for loading tickets');
      return null;
    }

    try {
      console.log('📂 Loading all tickets from MongoDB...');
      
      const tickets = await this.collection.find({}).toArray();
      
      console.log('✅ Loaded ' + tickets.length + ' tickets from MongoDB');
      return tickets;
    } catch (error) {
      console.error('❌ MongoDB load failed:', error.message);
      return null;
    }
  }

  // Get tickets that need to be updated (modified since last sync)
  async getTicketsToUpdate(lastSyncDate) {
    if (!await this.isAvailable()) {
      return [];
    }

    try {
      const query = {
        'fields.updated': { $gt: lastSyncDate.toISOString() }
      };
      
      const tickets = await this.collection.find(query).toArray();
      console.log('🔄 Found ' + tickets.length + ' tickets to update since ' + lastSyncDate.toISOString());
      
      return tickets.map(ticket => ticket.key);
    } catch (error) {
      console.error('❌ Failed to get tickets to update:', error.message);
      return [];
    }
  }

  // Get the latest update timestamp from database
  async getLatestUpdateTime() {
    if (!await this.isAvailable()) {
      return null;
    }

    try {
      const latestTicket = await this.collection.findOne(
        {},
        {
          sort: { 'fields.updated': -1 },
          projection: { 'fields.updated': 1 }
        }
      );

      return latestTicket ? new Date(latestTicket.fields.updated) : null;
    } catch (error) {
      console.error('❌ Failed to get latest update time:', error.message);
      return null;
    }
  }

  // Get/Set persistent sync metadata
  async getSyncMetadata() {
    if (!await this.isAvailable()) {
      return null;
    }

    try {
      const metaCollection = this.db.collection('sync_metadata');
      const metadata = await metaCollection.findOne({ _id: 'sync_state' });
      return metadata || {
        _id: 'sync_state',
        lastSyncTimestamp: null,
        lastFullSyncTimestamp: null,
        totalTicketsInJira: 0,
        totalTicketsInDb: 0,
        lastSyncStatus: 'never',
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error) {
      console.error('❌ Failed to get sync metadata:', error.message);
      return null;
    }
  }

  async updateSyncMetadata(updates) {
    if (!await this.isAvailable()) {
      return false;
    }

    try {
      const metaCollection = this.db.collection('sync_metadata');
      const result = await metaCollection.replaceOne(
        { _id: 'sync_state' },
        {
          _id: 'sync_state',
          ...updates,
          updatedAt: new Date()
        },
        { upsert: true }
      );
      return result.acknowledged;
    } catch (error) {
      console.error('❌ Failed to update sync metadata:', error.message);
      return false;
    }
  }

  // Verify database integrity against Jira
  async verifyDatabaseIntegrity(jiraTotalCount) {
    if (!await this.isAvailable()) {
      return { isValid: false, reason: 'Database not available' };
    }

    try {
      const dbCount = await this.collection.countDocuments();
      const metadata = await this.getSyncMetadata();

      console.log(`🔍 Database Verification:`);
      console.log(`   - Jira Total: ${jiraTotalCount.toLocaleString()}`);
      console.log(`   - Database Total: ${dbCount.toLocaleString()}`);
      console.log(`   - Last Sync: ${metadata?.lastSyncTimestamp || 'Never'}`);

      // Consider database valid if within 5% of Jira count
      const tolerance = 0.05;
      const minExpected = jiraTotalCount * (1 - tolerance);
      const maxExpected = jiraTotalCount * (1 + tolerance);

      const isValid = dbCount >= minExpected && dbCount <= maxExpected;

      return {
        isValid,
        dbCount,
        jiraCount: jiraTotalCount,
        difference: jiraTotalCount - dbCount,
        percentageDiff: ((jiraTotalCount - dbCount) / jiraTotalCount * 100).toFixed(2),
        reason: isValid ? 'Database is synchronized' : `Database has ${jiraTotalCount - dbCount} missing tickets`
      };
    } catch (error) {
      console.error('❌ Failed to verify database integrity:', error.message);
      return { isValid: false, reason: error.message };
    }
  }

  // Get missing ticket keys by comparing with Jira keys
  async getMissingTicketKeys(jiraTicketKeys, batchSize = 1000) {
    if (!await this.isAvailable()) {
      return jiraTicketKeys; // All are missing if DB unavailable
    }

    try {
      console.log(`🔍 Checking for missing tickets in batches of ${batchSize}...`);
      const missingKeys = [];

      // Process Jira keys in batches to avoid memory issues
      for (let i = 0; i < jiraTicketKeys.length; i += batchSize) {
        const batch = jiraTicketKeys.slice(i, i + batchSize);

        // Find existing keys in this batch
        const existingKeys = await this.collection.find(
          { key: { $in: batch } },
          { projection: { key: 1 } }
        ).toArray();

        const existingKeySet = new Set(existingKeys.map(doc => doc.key));

        // Find missing keys in this batch
        const batchMissing = batch.filter(key => !existingKeySet.has(key));
        missingKeys.push(...batchMissing);

        if (i % (batchSize * 10) === 0) {
          console.log(`   - Processed ${i + batch.length}/${jiraTicketKeys.length} keys, found ${missingKeys.length} missing so far`);
        }
      }

      console.log(`✅ Found ${missingKeys.length} missing tickets out of ${jiraTicketKeys.length} total`);
      return missingKeys;
    } catch (error) {
      console.error('❌ Failed to get missing ticket keys:', error.message);
      return jiraTicketKeys; // Return all as missing on error
    }
  }

  // Check which tickets exist in database
  async getExistingTicketKeys(ticketKeys) {
    if (!await this.isAvailable()) {
      return [];
    }

    try {
      const existingTickets = await this.collection.find(
        { key: { $in: ticketKeys } },
        { projection: { key: 1 } }
      ).toArray();
      
      return existingTickets.map(ticket => ticket.key);
    } catch (error) {
      console.error('❌ Failed to check existing tickets:', error.message);
      return [];
    }
  }

  // Clear all tickets from database
  async clearAllTickets() {
    if (!await this.isAvailable()) {
      return false;
    }

    try {
      const result = await this.collection.deleteMany({});
      console.log('🗑️ Cleared ' + result.deletedCount + ' tickets from MongoDB');
      return true;
    } catch (error) {
      console.error('❌ Failed to clear MongoDB tickets:', error.message);
      return false;
    }
  }

  // Force insert new tickets (for fresh sync)
  async forceInsertTickets(tickets) {
    if (!await this.isAvailable()) {
      console.warn('⚠️ MongoDB not available for inserting tickets');
      return false;
    }

    try {
      console.log('💾 Force inserting ' + tickets.length + ' tickets to MongoDB...');

      if (tickets.length === 0) {
        return true;
      }

      // Add metadata to tickets
      const ticketsWithMeta = tickets.map(ticket => ({
        ...ticket,
        _lastSynced: new Date(),
        _syncVersion: 1
      }));

      // Use insertMany for fresh inserts
      const result = await this.collection.insertMany(ticketsWithMeta, { ordered: false });

      console.log('✅ MongoDB force insert completed:');
      console.log('   - Inserted:', result.insertedCount);
      console.log('   - Total processed:', tickets.length);

      return true;
    } catch (error) {
      console.error('❌ MongoDB force insert failed:', error.message);
      return false;
    }
  }

  // Close MongoDB connection
  async disconnect() {
    if (this.client) {
      try {
        await this.client.close();
        this.isConnected = false;
        console.log('🔌 MongoDB connection closed');
      } catch (error) {
        console.error('❌ Error closing MongoDB connection:', error.message);
      }
    }
  }

  // Verify data integrity of stored tickets
  async verifyDataIntegrity(sampleSize = 1000) {
    if (!await this.isAvailable()) {
      return { isValid: false, reason: 'Database not available' };
    }

    try {
      console.log(`🔍 Verifying data integrity for ${sampleSize} sample tickets...`);

      // Get a random sample of tickets
      const sampleTickets = await this.collection.aggregate([
        { $sample: { size: sampleSize } },
        { $project: {
          key: 1,
          _dataIntegrityHash: 1,
          fields: 1
        }}
      ]).toArray();

      let validCount = 0;
      let invalidCount = 0;
      const invalidTickets = [];

      for (const ticket of sampleTickets) {
        const expectedHash = this.generateDataHash(ticket);
        const storedHash = ticket._dataIntegrityHash;

        if (expectedHash === storedHash) {
          validCount++;
        } else {
          invalidCount++;
          invalidTickets.push({
            key: ticket.key,
            expectedHash,
            storedHash
          });
        }
      }

      const integrityPercentage = (validCount / sampleTickets.length * 100).toFixed(2);

      console.log(`✅ Data integrity check completed:`);
      console.log(`   - Valid tickets: ${validCount}/${sampleTickets.length} (${integrityPercentage}%)`);
      console.log(`   - Invalid tickets: ${invalidCount}`);

      return {
        isValid: invalidCount === 0,
        totalChecked: sampleTickets.length,
        validCount,
        invalidCount,
        integrityPercentage: parseFloat(integrityPercentage),
        invalidTickets: invalidTickets.slice(0, 10) // Return first 10 invalid tickets
      };

    } catch (error) {
      console.error('❌ Failed to verify data integrity:', error.message);
      return { isValid: false, reason: error.message };
    }
  }

  // Repair corrupted tickets
  async repairCorruptedTickets(corruptedTicketKeys) {
    if (!await this.isAvailable() || corruptedTicketKeys.length === 0) {
      return false;
    }

    try {
      console.log(`🔧 Repairing ${corruptedTicketKeys.length} corrupted tickets...`);

      // Mark corrupted tickets for re-sync
      const result = await this.collection.updateMany(
        { key: { $in: corruptedTicketKeys } },
        {
          $set: {
            _needsRepair: true,
            _repairTimestamp: new Date()
          }
        }
      );

      console.log(`✅ Marked ${result.modifiedCount} tickets for repair`);
      return result.modifiedCount > 0;

    } catch (error) {
      console.error('❌ Failed to repair corrupted tickets:', error.message);
      return false;
    }
  }

  // Health check endpoint data
  async getHealthCheck() {
    const isAvailable = await this.isAvailable();
    const stats = isAvailable ? await this.getStats() : null;

    return {
      enabled: this.isEnabled,
      connected: this.isConnected,
      available: isAvailable,
      database: this.databaseName,
      collection: this.collectionName,
      stats: stats
    };
  }
}

// Create singleton instance
const mongoDbService = new MongoDbService();
export default mongoDbService;
