import axios from 'axios';
import {
  loadDashboardConfig,
  mapJiraStatusToCard,
  mapJiraPriorityToCard,
  getVisibleStatusCards,
  getVisiblePriorityCards,
  getVisibleOrganizationCards
} from '../config/enhancedDashboardConfig.js';

class OptimizedJiraApiService {
  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 15000,
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    this.lastUpdateTime = null;
    this.cachedTickets = [];
    this.isLoading = false;
    this.retryCount = 0;
    this.maxRetries = 2;

    this.STORAGE_KEYS = {
      TICKETS: 'jira_dashboard_tickets',
      LAST_UPDATE: 'jira_dashboard_last_update',
      METADATA: 'jira_dashboard_metadata'
    };

    this.loadFromStorage();
  }

  buildJQLQuery(config, baseJQL) {
    baseJQL = baseJQL || 'ORDE<PERSON> BY created DESC';
    let jql = baseJQL;

    if (config.filters && config.filters.year && config.filters.year !== 'all') {
      const year = config.filters.year;
      const yearStart = year + '-01-01';
      const yearEnd = year + '-12-31';

      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', 'AND created >= "' + yearStart + '" AND created <= "' + yearEnd + '" ORDER BY');
      } else {
        jql = jql + ' AND created >= "' + yearStart + '" AND created <= "' + yearEnd + '"';
      }
    }

    if (config.jql && config.jql.baseProject) {
      const projectFilter = 'project = "' + config.jql.baseProject + '"';
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', 'AND ' + projectFilter + ' ORDER BY');
      } else {
        jql = jql + ' AND ' + projectFilter;
      }
    }

    if (config.jql && config.jql.additionalFilters) {
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', config.jql.additionalFilters + ' ORDER BY');
      } else {
        jql = jql + ' ' + config.jql.additionalFilters;
      }
    }

    return jql;
  }

  loadFromStorage() {
    try {
      const storedTickets = localStorage.getItem(this.STORAGE_KEYS.TICKETS);
      const storedLastUpdate = localStorage.getItem(this.STORAGE_KEYS.LAST_UPDATE);

      if (storedTickets && storedLastUpdate) {
        this.cachedTickets = JSON.parse(storedTickets);
        this.lastUpdateTime = new Date(storedLastUpdate);
        console.log('Loaded ' + this.cachedTickets.length + ' tickets from storage');
      } else {
        console.log('No cached tickets found in storage');
      }
    } catch (error) {
      console.warn('Error loading from storage:', error.message);
      this.cachedTickets = [];
      this.lastUpdateTime = null;
    }
  }

  saveToStorage() {
    try {
      localStorage.setItem(this.STORAGE_KEYS.TICKETS, JSON.stringify(this.cachedTickets));
      const updateTime = (this.lastUpdateTime && this.lastUpdateTime.toISOString()) || new Date().toISOString();
      localStorage.setItem(this.STORAGE_KEYS.LAST_UPDATE, updateTime);
      console.log('Saved ' + this.cachedTickets.length + ' tickets to storage');
    } catch (error) {
      console.warn('Error saving to storage:', error.message);
    }
  }

  clearStorage() {
    localStorage.removeItem(this.STORAGE_KEYS.TICKETS);
    localStorage.removeItem(this.STORAGE_KEYS.LAST_UPDATE);
    localStorage.removeItem(this.STORAGE_KEYS.METADATA);
    this.cachedTickets = [];
    this.lastUpdateTime = null;
    console.log('Cleared all cached data');
  }

  getStorageInfo() {
    const ticketsData = localStorage.getItem(this.STORAGE_KEYS.TICKETS);
    const ticketsSize = (ticketsData && ticketsData.length) || 0;
    const lastUpdate = localStorage.getItem(this.STORAGE_KEYS.LAST_UPDATE);
    return {
      ticketsCount: this.cachedTickets.length,
      storageSize: (ticketsSize / 1024 / 1024).toFixed(2) + ' MB',
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleString() : 'Never',
      cacheAge: this.lastUpdateTime ? ((new Date() - this.lastUpdateTime) / (1000 * 60)).toFixed(1) + ' minutes' : 'N/A'
    };
  }

  async retryRequest(requestFn, retries) {
    retries = retries || this.maxRetries;
    for (let i = 0; i <= retries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        console.log('Attempt ' + (i + 1) + '/' + (retries + 1) + ' failed:', error.message);
        if (i === retries) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }

  async getAllDashboardData() {
    console.log('OPTIMIZED API: getAllDashboardData() called - NEW VERSION');

    if (this.cachedTickets.length > 0 && this.lastUpdateTime) {
      const hoursSinceUpdate = (new Date() - this.lastUpdateTime) / (1000 * 60 * 60);
      if (hoursSinceUpdate < 1) {
        console.log('Using cached data (' + this.cachedTickets.length + ' tickets, ' + hoursSinceUpdate.toFixed(1) + 'h old)');
        return await this.processAllTicketsData(this.cachedTickets);
      } else {
        console.log('Cached data is ' + hoursSinceUpdate.toFixed(1) + 'h old, fetching updates...');
      }
    }

    if (this.isLoading) {
      console.log('Full load already in progress, skipping...');
      return null;
    }

    try {
      this.isLoading = true;
      console.log('Fetching ALL tickets from Jira...');

      const currentConfig = loadDashboardConfig();
      const jqlQuery = this.buildJQLQuery(currentConfig);

      const totalCountResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key',
          maxResults: 0
        }
      });

      const totalTickets = totalCountResponse.data.total || 0;
      console.log('Total tickets in Jira: ' + totalTickets.toLocaleString());

      const batchSize = 100;
      const maxTicketsToLoad = Math.min(totalTickets, 10000);
      const totalBatches = Math.ceil(maxTicketsToLoad / batchSize);
      let allTickets = [];
      let failedBatches = [];

      console.log('Total tickets available: ' + totalTickets.toLocaleString() + ', Loading: ' + maxTicketsToLoad.toLocaleString() + ', Batches: ' + totalBatches.toLocaleString());

      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;

        try {
          const response = await this.retryRequest(async () => {
            return await this.api.get('/jira/search', {
              params: {
                jql: jqlQuery,
                fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                maxResults: batchSize,
                startAt: startAt
              }
            });
          });

          const tickets = response.data.issues || [];

          if (tickets.length === 0) {
            console.log('Batch ' + (i + 1) + '/' + totalBatches + ': Empty response, continuing to next batch...');
          } else {
            allTickets = allTickets.concat(tickets);
            console.log('Batch ' + (i + 1) + '/' + totalBatches + ': ' + tickets.length + ' tickets loaded (Total: ' + allTickets.length + ')');

            if ((i + 1) % 5 === 0 || i === totalBatches - 1) {
              console.log('Intermediate update: Processing ' + allTickets.length + ' tickets...');
            }
          }

          if (i === totalBatches - 1 && allTickets.length < totalTickets * 0.9) {
            console.warn('Warning: Only loaded ' + allTickets.length + ' out of ' + totalTickets + ' expected tickets (' + ((allTickets.length/totalTickets)*100).toFixed(1) + '%)');
          }

          if (i < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 50));
          }

        } catch (error) {
          console.error('Batch ' + (i + 1) + '/' + totalBatches + ' failed:', error.message);

          if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || (error.response && error.response.status >= 500)) {
            console.log('Retrying batch ' + (i + 1) + '/' + totalBatches + ' in 2 seconds...');
            await new Promise(resolve => setTimeout(resolve, 2000));

            try {
              const retryResponse = await this.api.get('/jira/search', {
                params: {
                  jql: 'ORDER BY created DESC',
                  fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                  maxResults: batchSize,
                  startAt: startAt
                }
              });

              const tickets = retryResponse.data.issues || [];
              allTickets = allTickets.concat(tickets);
              console.log('Batch ' + (i + 1) + '/' + totalBatches + ' succeeded on retry: ' + tickets.length + ' tickets loaded (Total: ' + allTickets.length + ')');

            } catch (retryError) {
              console.error('Batch ' + (i + 1) + '/' + totalBatches + ' failed on retry:', retryError.message);
              failedBatches.push({ batchIndex: i, startAt: startAt, batchSize: batchSize });
            }
          } else {
            failedBatches.push({ batchIndex: i, startAt: startAt, batchSize: batchSize });
          }
        }
      }

      console.log('Successfully loaded ' + allTickets.length + ' out of ' + totalTickets + ' tickets');

      const loadingPercentage = ((allTickets.length / totalTickets) * 100).toFixed(1);
      console.log('Loading Progress: ' + loadingPercentage + '% complete (' + allTickets.length.toLocaleString() + '/' + totalTickets.toLocaleString() + ' tickets)');

      if (allTickets.length < totalTickets) {
        const remaining = totalTickets - allTickets.length;
        console.log(remaining.toLocaleString() + ' tickets remaining to load');

        if (failedBatches.length > 0) {
          console.log(failedBatches.length + ' batches failed during loading');
          console.log('Consider retrying failed batches or reducing batch size further');
        }
      }

      if (failedBatches.length > 0) {
        console.log('Loading Summary: ' + allTickets.length + '/' + totalTickets + ' tickets loaded (' + failedBatches.length + ' batches failed)');
      }

      this.cachedTickets = this.cleanupCachedTickets(allTickets);
      this.lastUpdateTime = new Date();

      this.saveToStorage();

      return await this.processAllTicketsData(this.cachedTickets);

    } catch (error) {
      console.error('Error fetching optimized dashboard data:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  async getTableTickets(page, pageSize, filters) {
    page = page || 0;
    pageSize = pageSize || 25;
    filters = filters || {};
    
    try {
      let jql = 'ORDER BY created DESC';
      
      if (filters.status) {
        jql = 'status = "' + filters.status + '" AND ' + jql;
      }
      if (filters.priority) {
        jql = 'priority = "' + filters.priority + '" AND ' + jql;
      }
      if (filters.project) {
        jql = 'project = "' + filters.project + '" AND ' + jql;
      }

      const response = await this.api.get('/jira/search', {
        params: {
          jql: jql,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description',
          maxResults: pageSize,
          startAt: page * pageSize
        }
      });

      const issues = response.data.issues || [];
      const transformedTickets = issues.map(function(issue) {
        return {
          key: issue.key,
          summary: (issue.fields && issue.fields.summary) || 'No summary',
          status: (issue.fields && issue.fields.status && issue.fields.status.name) || 'Unknown',
          priority: (issue.fields && issue.fields.priority && issue.fields.priority.name) || 'None',
          assignee: (issue.fields && issue.fields.assignee && issue.fields.assignee.displayName) || 'Unassigned',
          projectKey: (issue.fields && issue.fields.project && issue.fields.project.key) || (issue.key && issue.key.split('-')[0]) || 'Unknown',
          project: (issue.fields && issue.fields.project && issue.fields.project.name) || (issue.fields && issue.fields.project && issue.fields.project.key) || 'Unknown',
          created: (issue.fields && issue.fields.created) ? new Date(issue.fields.created).toLocaleDateString() : 'Unknown',
          updated: (issue.fields && issue.fields.updated) ? new Date(issue.fields.updated).toLocaleDateString() : 'Unknown',
          description: (issue.fields && issue.fields.description) || '',
          originalIssue: issue
        };
      });

      return {
        tickets: transformedTickets,
        total: response.data.total || 0,
        page: page,
        pageSize: pageSize
      };
    } catch (error) {
      console.error('Error fetching table tickets:', error);
      throw error;
    }
  }
}

const optimizedJiraApi = new OptimizedJiraApiService();
export default optimizedJiraApi;
