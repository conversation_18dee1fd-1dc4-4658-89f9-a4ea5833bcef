@echo off
title Jira Dashboard - Complete Auto Installer

echo =========================================================
echo    Jira Dashboard - Complete Auto Installer
echo =========================================================
echo.
echo This will automatically install and configure everything:
echo.
echo   [1] Node.js (if not installed)
echo   [2] MongoDB (local installation)
echo   [3] Application dependencies
echo   [4] Production build
echo   [5] Windows Service auto-start
echo   [6] Full screen dashboard
echo.
echo =========================================================
echo   IMPORTANT: This requires Administrator privileges
echo =========================================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as Administrator
    echo.
) else (
    echo [ERROR] This installer requires Administrator privileges.
    echo.
    echo Please:
    echo 1. Right-click on this file
    echo 2. Select "Run as administrator"
    echo 3. Click "Yes" when prompted
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo Starting complete auto-installation...
echo.
echo This may take 5-10 minutes depending on your internet speed.
echo Please be patient and do not close this window.
echo.

echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo ========================================
echo Starting installation process...
echo ========================================
echo.

REM Run the PowerShell auto-installer
powershell.exe -ExecutionPolicy Bypass -File "%~dp0simple-auto-install.ps1" -InstallNodeJS

echo.
echo ========================================
echo Installation process completed!
echo ========================================
echo.

if %errorLevel% == 0 (
    echo [SUCCESS] Installation completed successfully!
    echo.
    echo Your Jira Dashboard is now ready for production:
    echo   - Starts automatically when Windows boots
    echo   - Opens in full screen mode
    echo   - Auto-restarts on crashes
    echo   - MongoDB configured and running
    echo.
    echo Dashboard URL: http://localhost:3000
    echo.
    echo You can manage the service using: setup-auto-start.bat
    echo.
) else (
    echo [WARNING] Installation completed with some issues.
    echo Please check the log file for details.
    echo.
)

echo Press any key to exit...
pause >nul
