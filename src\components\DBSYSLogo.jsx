import React from 'react';

const DBSYSLogo = ({ width = 400, height = 130, className = '' }) => {
  return (
    <div className={`dbsys-logo ${className}`} style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: 'auto'
    }}>
      {/* Use the real DBSYS logo PNG file */}
      <img
        src="/dbsys-logo.png"
        alt="DBSYS Services Logo"
        style={{
          width: width,
          height: height,
          objectFit: 'contain',
          filter: 'drop-shadow(0 3px 6px rgba(0, 0, 0, 0.12))',
          maxWidth: '100%',
          maxHeight: '100%',
          transition: 'transform 0.2s ease-in-out',
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.02)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
        }}
      />
    </div>
  );
};

export default DBSYSLogo;
