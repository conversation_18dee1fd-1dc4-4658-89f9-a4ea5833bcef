import React, { useState } from 'react';
import {
  Drawer,
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Stack
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { DASHBOARD_CONFIG } from '../config/dashboardConfig';

const ConfigPanel = ({ open, onClose, onConfigChange }) => {
  const [config, setConfig] = useState(DASHBOARD_CONFIG);

  const handleSave = () => {
    onConfigChange(config);
    onClose();
  };

  const handleReset = () => {
    setConfig(DASHBOARD_CONFIG);
  };

  const updateConfig = (path, value) => {
    const newConfig = { ...config };
    const keys = path.split('.');
    let current = newConfig;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current = current[keys[i]];
    }
    current[keys[keys.length - 1]] = value;
    
    setConfig(newConfig);
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: 400,
          padding: 2,
        },
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Dashboard Configuration</Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      <Box sx={{ overflowY: 'auto', flex: 1 }}>
        {/* General Settings */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight="bold">General Settings</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={2}>
              <TextField
                label="Dashboard Title"
                value={config.title}
                onChange={(e) => updateConfig('title', e.target.value)}
                fullWidth
              />
              <TextField
                label="Refresh Interval (minutes)"
                type="number"
                value={config.refreshInterval / 60000}
                onChange={(e) => updateConfig('refreshInterval', e.target.value * 60000)}
                fullWidth
              />
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* JQL Configuration */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight="bold">JQL Filters</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={2}>
              <TextField
                label="Base Project Key"
                value={config.jql.baseProject}
                onChange={(e) => updateConfig('jql.baseProject', e.target.value)}
                fullWidth
                helperText="Leave empty for all projects"
              />
              <TextField
                label="Additional Filters"
                value={config.jql.additionalFilters}
                onChange={(e) => updateConfig('jql.additionalFilters', e.target.value)}
                fullWidth
                multiline
                rows={2}
                helperText="e.g., AND assignee = currentUser()"
              />
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* Chart Configuration */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight="bold">Charts</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Stack spacing={2}>
              <TextField
                label="Organization Chart Title"
                value={config.charts.organizationChart.title}
                onChange={(e) => updateConfig('charts.organizationChart.title', e.target.value)}
                fullWidth
              />
              <FormControl fullWidth>
                <InputLabel>Group By</InputLabel>
                <Select
                  value={config.charts.organizationChart.groupBy}
                  onChange={(e) => updateConfig('charts.organizationChart.groupBy', e.target.value)}
                >
                  <MenuItem value="project">Project</MenuItem>
                  <MenuItem value="assignee">Assignee</MenuItem>
                  <MenuItem value="component">Component</MenuItem>
                </Select>
              </FormControl>
              <TextField
                label="Max Items to Show"
                type="number"
                value={config.charts.organizationChart.maxItems}
                onChange={(e) => updateConfig('charts.organizationChart.maxItems', parseInt(e.target.value))}
                fullWidth
              />
            </Stack>
          </AccordionDetails>
        </Accordion>

        {/* Status Mapping */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight="bold">Status Mapping</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Configure how Jira statuses map to dashboard cards:
            </Typography>
            {config.statusCards.map((card, index) => (
              <Box key={card.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                  {card.title}
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {card.jiraStatuses.map((status, statusIndex) => (
                    <Chip
                      key={statusIndex}
                      label={status}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Stack>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Edit in dashboardConfig.js to modify status mappings
                </Typography>
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>

        {/* Priority Mapping */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1" fontWeight="bold">Priority Mapping</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Configure how Jira priorities map to dashboard cards:
            </Typography>
            {config.priorityCards.map((card, index) => (
              <Box key={card.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold" sx={{ mb: 1 }}>
                  {card.title}
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap">
                  {card.jiraPriorities.map((priority, priorityIndex) => (
                    <Chip
                      key={priorityIndex}
                      label={priority}
                      size="small"
                      variant="outlined"
                    />
                  ))}
                </Stack>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Edit in dashboardConfig.js to modify priority mappings
                </Typography>
              </Box>
            ))}
          </AccordionDetails>
        </Accordion>
      </Box>

      <Divider sx={{ my: 2 }} />
      
      <Stack direction="row" spacing={2}>
        <Button
          variant="contained"
          startIcon={<SaveIcon />}
          onClick={handleSave}
          fullWidth
        >
          Apply Changes
        </Button>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={handleReset}
          fullWidth
        >
          Reset
        </Button>
      </Stack>
    </Drawer>
  );
};

export default ConfigPanel;
