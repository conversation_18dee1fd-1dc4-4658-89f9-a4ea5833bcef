# Jira Dashboard Simple Auto-Installer
# This script automatically installs and configures everything needed

param(
    [switch]$InstallNodeJS
)

# Require administrator privileges
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires administrator privileges. Please run as Administrator." -ForegroundColor Red
    Write-Host "Right-click on the script and select 'Run as administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "🚀 Jira Dashboard Simple Auto-Installer" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = $scriptDir

# Find the correct project directory (look for package.json)
if (-not (Test-Path (Join-Path $projectDir "package.json"))) {
    Write-Host "Looking for package.json in parent directories..." -ForegroundColor Yellow

    # Check if we're in a subdirectory and need to go up
    $currentDir = $scriptDir
    $found = $false

    for ($i = 0; $i -lt 3; $i++) {
        $parentDir = Split-Path -Parent $currentDir
        if (Test-Path (Join-Path $parentDir "package.json")) {
            $projectDir = $parentDir
            $found = $true
            Write-Host "Found package.json in: $projectDir" -ForegroundColor Green
            break
        }
        $currentDir = $parentDir
    }

    if (-not $found) {
        Write-Host "❌ Could not find package.json file!" -ForegroundColor Red
        Write-Host "Please make sure you're running this script from the Jira Dashboard project directory." -ForegroundColor Yellow
        Write-Host "Current directory: $scriptDir" -ForegroundColor Gray
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Change to project directory
Set-Location $projectDir
Write-Host "Working in project directory: $projectDir" -ForegroundColor Green

function Write-Step {
    param($Message, $Color = "Yellow")
    Write-Host ""
    Write-Host "🔄 $Message" -ForegroundColor $Color
    Write-Host "----------------------------------------" -ForegroundColor Gray
}

function Write-Success {
    param($Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param($Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Warning {
    param($Message)
    Write-Host "⚠️ $Message" -ForegroundColor Yellow
}

# Step 1: Check Node.js
Write-Step "Checking Node.js installation"
try {
    $nodeVersion = & node --version 2>$null
    if ($nodeVersion) {
        Write-Success "Node.js already installed: $nodeVersion"
    } else {
        throw "Node.js not found"
    }
}
catch {
    if ($InstallNodeJS) {
        Write-Step "Installing Node.js"
        Write-Host "Downloading Node.js installer..." -ForegroundColor Yellow
        
        $nodeUrl = "https://nodejs.org/dist/v20.11.0/node-v20.11.0-x64.msi"
        $nodeInstaller = Join-Path $env:TEMP "nodejs-installer.msi"
        
        try {
            Invoke-WebRequest -Uri $nodeUrl -OutFile $nodeInstaller -UseBasicParsing
            Write-Host "Installing Node.js..." -ForegroundColor Yellow
            Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", "`"$nodeInstaller`"", "/quiet", "/norestart" -Wait
            
            # Refresh PATH
            $env:Path = [System.Environment]::GetEnvironmentVariable("Path", "Machine") + ";" + [System.Environment]::GetEnvironmentVariable("Path", "User")
            
            Start-Sleep -Seconds 5
            $nodeVersion = & node --version 2>$null
            if ($nodeVersion) {
                Write-Success "Node.js installed successfully: $nodeVersion"
                Remove-Item $nodeInstaller -Force -ErrorAction SilentlyContinue
            } else {
                Write-Error "Node.js installation failed"
                exit 1
            }
        }
        catch {
            Write-Error "Failed to install Node.js: $($_.Exception.Message)"
            exit 1
        }
    } else {
        Write-Error "Node.js is required but not installed. Run with -InstallNodeJS to install automatically."
        exit 1
    }
}

# Step 2: Install dependencies
Write-Step "Installing application dependencies"
try {
    if (Test-Path "node_modules") {
        Write-Host "Cleaning existing node_modules..." -ForegroundColor Yellow
        Remove-Item "node_modules" -Recurse -Force
    }
    
    Write-Host "Running npm install..." -ForegroundColor Yellow
    $installProcess = Start-Process -FilePath "npm" -ArgumentList "install" -Wait -PassThru -NoNewWindow
    
    if ($installProcess.ExitCode -eq 0) {
        Write-Success "Dependencies installed successfully"
    } else {
        Write-Error "Failed to install dependencies"
        exit 1
    }
}
catch {
    Write-Error "Failed to install dependencies: $($_.Exception.Message)"
    exit 1
}

# Step 3: Setup MongoDB
Write-Step "Setting up MongoDB"
try {
    $mongoScript = Join-Path $projectDir "scripts\auto-install-mongodb.ps1"
    if (Test-Path $mongoScript) {
        & powershell.exe -ExecutionPolicy Bypass -File $mongoScript
        Write-Success "MongoDB setup completed"
    } else {
        Write-Warning "MongoDB auto-installer not found, trying alternative setup"
        $setupScript = Join-Path $projectDir "scripts\setup-mongodb.js"
        if (Test-Path $setupScript) {
            & node $setupScript
            Write-Success "Alternative MongoDB setup completed"
        } else {
            Write-Warning "MongoDB setup skipped - manual setup may be required"
        }
    }
}
catch {
    Write-Warning "MongoDB setup failed: $($_.Exception.Message)"
    Write-Host "Application may still work with external MongoDB" -ForegroundColor Yellow
}

# Step 4: Build production
Write-Step "Building production version"
try {
    Write-Host "Running npm run build..." -ForegroundColor Yellow
    $buildProcess = Start-Process -FilePath "npm" -ArgumentList "run", "build" -Wait -PassThru -NoNewWindow
    
    if ($buildProcess.ExitCode -eq 0) {
        Write-Success "Production build completed successfully"
    } else {
        Write-Error "Production build failed"
        exit 1
    }
}
catch {
    Write-Error "Failed to build production version: $($_.Exception.Message)"
    exit 1
}

# Step 5: Install Windows Service
Write-Step "Installing Windows Service"
try {
    $serviceScript = Join-Path $projectDir "scripts\install-windows-service.ps1"
    if (Test-Path $serviceScript) {
        & powershell.exe -ExecutionPolicy Bypass -File $serviceScript -Install
        Write-Success "Windows Service installed successfully"
    } else {
        Write-Warning "Windows Service installer not found, trying scheduled task"
        $autoStartScript = Join-Path $projectDir "scripts\install-auto-start.ps1"
        if (Test-Path $autoStartScript) {
            & powershell.exe -ExecutionPolicy Bypass -File $autoStartScript -Install
            Write-Success "Auto-start scheduled task installed"
        } else {
            Write-Warning "Auto-start setup skipped - manual setup required"
        }
    }
}
catch {
    Write-Warning "Auto-start setup failed: $($_.Exception.Message)"
    Write-Host "You can set up auto-start manually using setup-auto-start.bat" -ForegroundColor Yellow
}

# Step 6: Test installation
Write-Step "Testing installation"

# Test Node.js
try {
    $nodeVersion = & node --version
    Write-Success "Node.js: $nodeVersion"
}
catch {
    Write-Error "Node.js test failed"
}

# Test npm
try {
    $npmVersion = & npm --version
    Write-Success "npm: $npmVersion"
}
catch {
    Write-Error "npm test failed"
}

# Test production build
if (Test-Path "dist") {
    Write-Success "Production build: Available"
} else {
    Write-Error "Production build: Missing"
}

# Test MongoDB
try {
    $mongoTest = Test-NetConnection -ComputerName "localhost" -Port 27017 -InformationLevel Quiet -WarningAction SilentlyContinue
    if ($mongoTest) {
        Write-Success "MongoDB: Running on port 27017"
    } else {
        Write-Warning "MongoDB: Not responding (may start later)"
    }
}
catch {
    Write-Warning "MongoDB: Connection test failed"
}

# Final message
Write-Host ""
Write-Host "🎉 Installation completed!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Host "Your Jira Dashboard is ready for production!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. The dashboard should start automatically with Windows" -ForegroundColor White
Write-Host "2. You can access it at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "3. Use setup-auto-start.bat to manage auto-start settings" -ForegroundColor White
Write-Host ""

# Ask if user wants to start now
$startNow = Read-Host "Would you like to start the dashboard now? (y/n)"
if ($startNow -eq "y" -or $startNow -eq "Y") {
    Write-Host "🚀 Starting dashboard..." -ForegroundColor Green
    try {
        Start-Process "http://localhost:3000"
        Write-Success "Dashboard opened in browser"
    }
    catch {
        Write-Warning "Please open http://localhost:3000 manually"
    }
}

Write-Host ""
Write-Host "Installation complete! 🎯" -ForegroundColor Green
Read-Host "Press Enter to exit"
