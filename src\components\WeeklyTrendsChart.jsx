import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler,
  Title,
  Tooltip,
  Legend
);

// Expects data: { labels: [...], created: [...] }
const WeeklyTrendsChart = ({ data, config = {} }) => {
  const { labels = [], created = [] } = data || {};

  const colorSchemes = {
    blue: { primary: '#3b82f6', background: 'rgba(59,130,246,0.12)' },
    green: { primary: '#22c55e', background: 'rgba(34,197,94,0.12)' },
    red: { primary: '#ef4444', background: 'rgba(239,68,68,0.12)' },
    purple: { primary: '#8b5cf6', background: 'rgba(139,92,246,0.12)' },
    orange: { primary: '#f59e0b', background: 'rgba(245,158,11,0.12)' }
  };

  const colors = colorSchemes[config.colorScheme] || colorSchemes.green;

  const chartData = {
    labels,
    datasets: [
      {
        label: 'Created',
        data: created,
        tension: 0.35,
        borderColor: colors.primary,
        backgroundColor: config.showFill !== false ? colors.background : 'transparent',
        pointBackgroundColor: '#ffffff',
        pointBorderColor: colors.primary,
        pointRadius: 4,
        fill: config.showFill !== false,
      }
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: true, position: 'top' },
      title: { display: false },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: (ctx) => `${ctx.dataset.label}: ${ctx.parsed.y.toLocaleString()}`,
        },
      }
    },
    interaction: { mode: 'nearest', intersect: false },
    scales: {
      y: {
        beginAtZero: true,
        grid: { color: 'rgba(148,163,184,0.25)' },
        ticks: {
          color: '#94a3b8',
          callback: (value) => value.toLocaleString(),
        },
      },
      x: {
        grid: { color: 'rgba(148,163,184,0.15)' },
        ticks: { color: '#94a3b8' },
      },
    },
  };

  return (
    <div style={{ height: '320px' }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default WeeklyTrendsChart;

