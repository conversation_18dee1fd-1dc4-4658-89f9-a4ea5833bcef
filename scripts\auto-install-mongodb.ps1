# MongoDB Auto-Installer for Jira Dashboard
# This script automatically downloads, installs, and configures MongoDB

param(
    [switch]$Force
)

Write-Host "🍃 MongoDB Auto-Installer for Jira Dashboard" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir
$mongoDir = Join-Path $projectDir "mongodb"
$mongoDataDir = Join-Path $mongoDir "data"
$mongoLogDir = Join-Path $mongoDir "logs"
$mongoBinDir = Join-Path $mongoDir "bin"

# MongoDB download URL (Windows x64)
$mongoVersion = "7.0.5"
$mongoUrl = "https://fastdl.mongodb.org/windows/mongodb-windows-x86_64-$mongoVersion.zip"
$mongoZip = Join-Path $env:TEMP "mongodb-windows.zip"

function Test-MongoDBInstalled {
    # Check if MongoDB is already installed globally
    try {
        $mongoCmd = Get-Command mongod -ErrorAction SilentlyContinue
        if ($mongoCmd) {
            Write-Host "✅ MongoDB found in system PATH: $($mongoCmd.Source)" -ForegroundColor Green
            return $true
        }
    }
    catch {
        # Continue to check local installation
    }
    
    # Check if MongoDB is installed locally
    $localMongod = Join-Path $mongoBinDir "mongod.exe"
    if (Test-Path $localMongod) {
        Write-Host "✅ MongoDB found locally: $localMongod" -ForegroundColor Green
        return $true
    }
    
    return $false
}

function Install-MongoDB {
    Write-Host "📦 Installing MongoDB locally..." -ForegroundColor Yellow
    
    # Create directories
    @($mongoDir, $mongoDataDir, $mongoLogDir, $mongoBinDir) | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
            Write-Host "📁 Created directory: $_" -ForegroundColor Gray
        }
    }
    
    # Download MongoDB
    Write-Host "⬇️ Downloading MongoDB $mongoVersion..." -ForegroundColor Yellow
    try {
        Invoke-WebRequest -Uri $mongoUrl -OutFile $mongoZip -UseBasicParsing
        Write-Host "✅ MongoDB downloaded successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to download MongoDB: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    # Extract MongoDB
    Write-Host "📂 Extracting MongoDB..." -ForegroundColor Yellow
    try {
        Add-Type -AssemblyName System.IO.Compression.FileSystem
        $zip = [System.IO.Compression.ZipFile]::OpenRead($mongoZip)
        
        foreach ($entry in $zip.Entries) {
            if ($entry.Name -like "*.exe" -or $entry.Name -like "*.dll") {
                $destinationPath = Join-Path $mongoBinDir $entry.Name
                [System.IO.Compression.ZipFileExtensions]::ExtractToFile($entry, $destinationPath, $true)
            }
        }
        
        $zip.Dispose()
        Remove-Item $mongoZip -Force
        Write-Host "✅ MongoDB extracted successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to extract MongoDB: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

function Initialize-MongoDB {
    Write-Host "🔧 Initializing MongoDB..." -ForegroundColor Yellow
    
    $mongodExe = Join-Path $mongoBinDir "mongod.exe"
    if (-not (Test-Path $mongodExe)) {
        # Try system MongoDB
        $mongodExe = "mongod"
    }
    
    # Create MongoDB configuration file
    $mongoConfig = @"
storage:
  dbPath: "$($mongoDataDir -replace '\\', '/')"
  journal:
    enabled: true

systemLog:
  destination: file
  path: "$($mongoLogDir -replace '\\', '/')/mongod.log"
  logAppend: true

net:
  port: 27017
  bindIp: 127.0.0.1

processManagement:
  windowsService:
    serviceName: MongoDB
    displayName: MongoDB
    description: MongoDB Database Server
"@
    
    $configPath = Join-Path $mongoDir "mongod.conf"
    $mongoConfig | Out-File -FilePath $configPath -Encoding UTF8
    Write-Host "✅ MongoDB configuration created: $configPath" -ForegroundColor Green
    
    # Initialize database
    Write-Host "🗄️ Initializing database..." -ForegroundColor Yellow
    try {
        $initProcess = Start-Process -FilePath $mongodExe -ArgumentList "--config", "`"$configPath`"", "--nojournal" -WindowStyle Hidden -PassThru
        Start-Sleep -Seconds 5
        
        if (-not $initProcess.HasExited) {
            $initProcess.Kill()
            Write-Host "✅ MongoDB initialized successfully" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "⚠️ MongoDB initialization completed with warnings" -ForegroundColor Yellow
    }
    
    return $true
}

function Start-MongoDBService {
    Write-Host "🚀 Starting MongoDB..." -ForegroundColor Yellow
    
    $mongodExe = Join-Path $mongoBinDir "mongod.exe"
    $configPath = Join-Path $mongoDir "mongod.conf"
    
    if (-not (Test-Path $mongodExe)) {
        $mongodExe = "mongod"
    }
    
    # Check if MongoDB is already running
    try {
        $mongoProcess = Get-Process -Name "mongod" -ErrorAction SilentlyContinue
        if ($mongoProcess) {
            Write-Host "✅ MongoDB is already running (PID: $($mongoProcess.Id))" -ForegroundColor Green
            return $true
        }
    }
    catch {
        # Continue to start MongoDB
    }
    
    # Start MongoDB
    try {
        if (Test-Path $configPath) {
            $mongoProcess = Start-Process -FilePath $mongodExe -ArgumentList "--config", "`"$configPath`"" -WindowStyle Hidden -PassThru
        } else {
            $mongoProcess = Start-Process -FilePath $mongodExe -ArgumentList "--dbpath", "`"$mongoDataDir`"", "--logpath", "`"$mongoLogDir\mongod.log`"" -WindowStyle Hidden -PassThru
        }
        
        # Wait for MongoDB to start
        $timeout = 30
        $elapsed = 0
        while ($elapsed -lt $timeout) {
            try {
                $testConnection = Test-NetConnection -ComputerName "localhost" -Port 27017 -InformationLevel Quiet
                if ($testConnection) {
                    Write-Host "✅ MongoDB started successfully on port 27017" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                # Continue waiting
            }
            Start-Sleep -Seconds 2
            $elapsed += 2
        }
        
        Write-Host "⚠️ MongoDB may have started but connection test failed" -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-Host "❌ Failed to start MongoDB: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Setup-DatabaseAndUser {
    Write-Host "👤 Setting up database and user..." -ForegroundColor Yellow

    # Wait for MongoDB to be fully ready
    Start-Sleep -Seconds 10

    $mongoExe = Join-Path $mongoBinDir "mongosh.exe"
    if (-not (Test-Path $mongoExe)) {
        $mongoExe = Join-Path $mongoBinDir "mongo.exe"
        if (-not (Test-Path $mongoExe)) {
            # Try system mongosh/mongo
            try {
                $mongoExe = (Get-Command mongosh -ErrorAction SilentlyContinue).Source
                if (-not $mongoExe) {
                    $mongoExe = (Get-Command mongo -ErrorAction SilentlyContinue).Source
                }
            }
            catch {
                Write-Host "⚠️ MongoDB client not found, skipping user setup" -ForegroundColor Yellow
                return $true
            }
        }
    }

    # JavaScript commands to setup database (with error handling)
    $setupScript = @"
try {
    use admin
    try {
        db.createUser({
          user: "admin",
          pwd: "admin_pass_2025",
          roles: ["root"]
        })
        print("✅ Admin user created")
    } catch(e) {
        if (e.code === 11000) {
            print("ℹ️ Admin user already exists")
        } else {
            print("⚠️ Admin user creation failed: " + e.message)
        }
    }

    use jira-dashboard
    try {
        db.createUser({
          user: "jira_user",
          pwd: "jira_pass_2025",
          roles: ["readWrite"]
        })
        print("✅ Jira user created")
    } catch(e) {
        if (e.code === 11000) {
            print("ℹ️ Jira user already exists")
        } else {
            print("⚠️ Jira user creation failed: " + e.message)
        }
    }

    try {
        db.createCollection("tickets")
        print("✅ Tickets collection created")
    } catch(e) {
        print("ℹ️ Tickets collection may already exist")
    }

    try {
        db.createCollection("config")
        print("✅ Config collection created")
    } catch(e) {
        print("ℹ️ Config collection may already exist")
    }

    print("✅ Database setup completed successfully")
} catch(e) {
    print("❌ Database setup error: " + e.message)
}
"@

    $setupScriptPath = Join-Path $env:TEMP "mongo-setup.js"
    $setupScript | Out-File -FilePath $setupScriptPath -Encoding UTF8

    try {
        # Try different connection methods
        $connectionMethods = @(
            @($mongoExe, $setupScriptPath),
            @($mongoExe, "--host", "localhost:27017", $setupScriptPath),
            @($mongoExe, "mongodb://localhost:27017", "--file", $setupScriptPath)
        )

        $success = $false
        foreach ($method in $connectionMethods) {
            try {
                Write-Host "🔄 Trying database setup method..." -ForegroundColor Gray
                $setupProcess = Start-Process -FilePath $method[0] -ArgumentList $method[1..($method.Length-1)] -Wait -WindowStyle Hidden -PassThru
                if ($setupProcess.ExitCode -eq 0) {
                    $success = $true
                    break
                }
            }
            catch {
                continue
            }
        }

        Remove-Item $setupScriptPath -Force -ErrorAction SilentlyContinue

        if ($success) {
            Write-Host "✅ Database and user setup completed" -ForegroundColor Green
        } else {
            Write-Host "⚠️ Database setup completed with warnings (users may already exist)" -ForegroundColor Yellow
        }
        return $true
    }
    catch {
        Write-Host "⚠️ Database setup completed with warnings: $($_.Exception.Message)" -ForegroundColor Yellow
        Remove-Item $setupScriptPath -Force -ErrorAction SilentlyContinue
        return $true
    }
}

# Main execution
Write-Host "🔍 Checking MongoDB installation..." -ForegroundColor Yellow

if ((Test-MongoDBInstalled) -and -not $Force) {
    Write-Host "✅ MongoDB is already installed and configured" -ForegroundColor Green
    
    # Just start the service
    if (Start-MongoDBService) {
        Write-Host "🎉 MongoDB is ready for use!" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "⚠️ MongoDB installed but failed to start" -ForegroundColor Yellow
    }
} else {
    Write-Host "📦 MongoDB not found, installing automatically..." -ForegroundColor Yellow
    
    if (-not (Install-MongoDB)) {
        Write-Host "❌ MongoDB installation failed" -ForegroundColor Red
        exit 1
    }
    
    if (-not (Initialize-MongoDB)) {
        Write-Host "❌ MongoDB initialization failed" -ForegroundColor Red
        exit 1
    }
}

# Start MongoDB and setup database
if (Start-MongoDBService) {
    Start-Sleep -Seconds 5
    Setup-DatabaseAndUser
    Write-Host "🎉 MongoDB installation and setup completed successfully!" -ForegroundColor Green
    Write-Host "📊 Database: jira-dashboard" -ForegroundColor Cyan
    Write-Host "👤 User: jira_user" -ForegroundColor Cyan
    Write-Host "🔌 Connection: mongodb://localhost:27017/jira-dashboard" -ForegroundColor Cyan
} else {
    Write-Host "❌ Failed to start MongoDB" -ForegroundColor Red
    exit 1
}
