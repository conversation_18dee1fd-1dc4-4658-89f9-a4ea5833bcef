# Jira Real-Time Dashboard

A modern, responsive dashboard for monitoring Jira incidents and tickets in real-time, built with React and Material-UI.

## Features

- **🎛️ Fully Configurable**: Configure everything through the settings panel or config file
- **📊 Real-time Status Cards**: Display ticket counts by status with custom mappings
- **🚨 Priority Indicators**: Show incident counts by priority levels with custom mappings
- **📈 Dynamic Charts**: Configurable charts for projects, assignees, or components
- **📅 Monthly Trends**: Customizable time-based trend analysis
- **⚡ Auto-refresh**: Configurable refresh intervals
- **📱 Responsive Design**: Works on desktop, tablet, and mobile devices
- **🔧 Live Configuration**: Change settings without restarting the application
- **🎨 Custom Styling**: Configurable colors, themes, and layouts
- **🔍 Advanced JQL**: Support for custom JQL queries and filters
- **📋 No Mock Data**: Shows only real data from your Jira instance

## Quick Start

```bash
# Install dependencies
npm install

# Configure your Jira credentials (see below)
cp .env.example .env
# Edit .env with your Jira details

# Start both the proxy server and frontend
npm start
```

The dashboard will be available at http://localhost:5173
The proxy server runs on http://localhost:3001

## Jira API Configuration

1. Copy `.env.example` to `.env`
2. Add your Jira credentials:
```env
REACT_APP_JIRA_BASE_URL=https://your-domain.atlassian.net
REACT_APP_JIRA_EMAIL=<EMAIL>
REACT_APP_JIRA_API_TOKEN=your-api-token
```

Get your API token at: https://id.atlassian.com/manage-profile/security/api-tokens

## Configuration

### 🎛️ Live Configuration Panel
Click the settings button (⚙️) in the bottom-right corner to access the configuration panel where you can:

- **General Settings**: Change dashboard title and refresh interval
- **JQL Filters**: Set project filters and additional JQL conditions
- **Chart Settings**: Configure chart titles, grouping options, and display limits
- **Status Mapping**: View how Jira statuses map to dashboard cards
- **Priority Mapping**: View how Jira priorities map to dashboard cards

### 📝 File-based Configuration
Edit `src/config/dashboardConfig.js` to customize:

#### Status Cards
```javascript
statusCards: [
  {
    id: "non_traites",
    title: "Non traités",
    color: "#5e72e4",
    textColor: "white",
    jiraStatuses: ["To Do", "Open", "New", "Backlog"]
  }
  // Add more status cards...
]
```

#### Priority Cards
```javascript
priorityCards: [
  {
    id: "tres_urgent",
    title: "Incident Très Urgent",
    color: "#dc3545",
    textColor: "white",
    jiraPriorities: ["Highest", "Critical", "Blocker"]
  }
  // Add more priority cards...
]
```

#### JQL Queries
```javascript
jql: {
  baseProject: "YOUR_PROJECT_KEY", // Leave empty for all projects
  additionalFilters: "AND assignee = currentUser()" // Optional filters
}
```

#### Chart Configuration
```javascript
charts: {
  organizationChart: {
    title: "Tickets by Project",
    maxItems: 10,
    groupBy: "project" // Options: "project", "assignee", "component"
  }
}
```

## Technologies Used

- React 19 + Vite
- Material-UI (MUI)
- Chart.js + react-chartjs-2
- Axios for API requests
