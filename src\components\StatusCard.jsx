import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { DASHBOARD_CONFIG } from '../config/dashboardConfig';

const StatusCard = ({ title, count, color, textColor = 'white', onClick, clickable = false }) => {
  return (
    <Card
      sx={{
        backgroundColor: color,
        color: textColor,
        minHeight: 120,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: DASHBOARD_CONFIG.theme.cardBorderRadius / 4,
        boxShadow: DASHBOARD_CONFIG.theme.cardShadow,
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        cursor: clickable ? 'pointer' : 'default',
        '&:hover': {
          transform: clickable ? 'translateY(-2px)' : 'none',
          boxShadow: clickable ? DASHBOARD_CONFIG.theme.cardShadow + 4 : DASHBOARD_CONFIG.theme.cardShadow + 2,
          backgroundColor: clickable ? `${color}dd` : color,
        }
      }}
      onClick={clickable ? onClick : undefined}
    >
      <CardContent sx={{ textAlign: 'center', padding: '16px !important' }}>
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: 'bold',
            fontSize: '1rem',
            marginBottom: 1,
            lineHeight: 1.2
          }}
        >
          {title}
        </Typography>
        <Typography
          variant="h3"
          component="div"
          sx={{
            fontWeight: 'bold',
            fontSize: '2.5rem'
          }}
        >
          {count.toLocaleString()}
        </Typography>
      </CardContent>
    </Card>
  );
};

export default StatusCard;
