import React, { useState, useEffect, useRef } from 'react';
import SimpleStatusCard from './SimpleStatusCard';

const ModernDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    tickets: [],
    stats: null,
    analytics: null,
    totalCount: 0
  });
  
  const [theme, setTheme] = useState('light');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: [],
    priority: [],
    assignee: []
  });
  
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [config, setConfig] = useState(null);
  const [performance, setPerformance] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('connected');
  const [isMobile, setIsMobile] = useState(false);
  
  const wsRef = useRef(null);
  const updateIntervalRef = useRef(null);

  // Initialize dashboard
  useEffect(() => {
    initializeDashboard();
    setupWebSocketConnection();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, []);

  // Handle responsive design
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Theme management
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
    localStorage.setItem('dashboard-theme', theme);
  }, [theme]);

  const initializeDashboard = async () => {
    try {
      setIsLoading(true);
      
      // Load configuration
      await loadConfiguration();
      
      // Load initial data
      await loadDashboardData();
      
      // Load analytics
      await loadAnalytics();
      
      // Set up auto-refresh
      setupAutoRefresh();
      
    } catch (error) {
      console.error('Error initializing dashboard:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadConfiguration = async () => {
    try {
      const response = await fetch('/api/config/dashboard');
      if (response.ok) {
        const data = await response.json();
        setConfig(data.config);
        setTheme(data.config?.theme || 'light');
      }
    } catch (error) {
      console.error('Error loading configuration:', error);
    }
  };

  const loadDashboardData = async () => {
    try {
      const startTime = Date.now();
      const response = await fetch('/api/mongodb/dashboard-tickets?stats=true');
      const data = await response.json();
      
      if (data.success) {
        setDashboardData({
          tickets: data.tickets || [],
          stats: data.stats,
          totalCount: data.count || 0
        });
        setPerformance(data.performance);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setConnectionStatus('error');
    }
  };

  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/dashboard/analytics');
      const data = await response.json();
      
      if (data.success) {
        setDashboardData(prev => ({
          ...prev,
          analytics: data.analytics
        }));
      }
    } catch (error) {
      console.error('Error loading analytics:', error);
    }
  };

  const setupWebSocketConnection = () => {
    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const wsUrl = `${protocol}//${window.location.host}/ws/dashboard`;
      
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setConnectionStatus('connected');
      };

      wsRef.current.onmessage = (event) => {
        const message = JSON.parse(event.data);
        handleWebSocketMessage(message);
      };

      wsRef.current.onclose = () => {
        setConnectionStatus('disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(setupWebSocketConnection, 5000);
      };

      wsRef.current.onerror = () => {
        setConnectionStatus('error');
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
      setConnectionStatus('failed');
    }
  };

  const handleWebSocketMessage = (message) => {
    if (message.type === 'dashboard_update') {
      setDashboardData(prev => ({
        ...prev,
        stats: message.data.stats
      }));
      setLastUpdate(new Date());
    }
  };

  const setupAutoRefresh = () => {
    const interval = config?.refreshInterval || 10000;
    updateIntervalRef.current = setInterval(loadDashboardData, interval);
  };

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light');
  };

  const handleConfigSave = (newConfig) => {
    setConfig(newConfig);
    setTheme(newConfig.theme);
  };

  // Simple chart data for visualization
  const getStatusData = () => {
    const statusCounts = dashboardData.stats?.statusCounts || {};
    return Object.entries(statusCounts).map(([status, count]) => ({
      label: status,
      value: count,
      percentage: dashboardData.totalCount > 0 ? (count / dashboardData.totalCount * 100).toFixed(1) : 0
    }));
  };

  const getPriorityData = () => {
    const priorityCounts = dashboardData.stats?.priorityCounts || {};
    return Object.entries(priorityCounts).map(([priority, count]) => ({
      label: priority,
      value: count,
      percentage: dashboardData.totalCount > 0 ? (count / dashboardData.totalCount * 100).toFixed(1) : 0
    }));
  };

  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center ${theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 text-lg ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            Loading modern dashboard...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-gray-50 text-gray-900'
    }`}>
      {/* Header */}
      <header className={`sticky top-0 z-40 ${
        theme === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      } border-b backdrop-blur-sm bg-opacity-95`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <h1 className={`${isMobile ? 'text-lg' : 'text-2xl'} font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent`}>
                {isMobile ? 'Jira Pro' : 'Jira Dashboard Pro'}
              </h1>

              {/* Connection Status - Hidden on mobile */}
              {!isMobile && (
                <div className={`flex items-center space-x-2 text-sm ${
                  connectionStatus === 'connected' ? 'text-green-500' :
                  connectionStatus === 'disconnected' ? 'text-yellow-500' : 'text-red-500'
                }`}>
                  <div className={`w-2 h-2 rounded-full ${
                    connectionStatus === 'connected' ? 'bg-green-500' :
                    connectionStatus === 'disconnected' ? 'bg-yellow-500' : 'bg-red-500'
                  } animate-pulse`}></div>
                  <span className="capitalize">{connectionStatus}</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {/* Mobile Menu Button */}
              {isMobile ? (
                <button
                  onClick={() => setShowMobileMenu(!showMobileMenu)}
                  className={`p-2 rounded-lg transition-colors ${
                    theme === 'dark'
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                  }`}
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              ) : (
                <>
                  {/* Search - Desktop */}
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search tickets..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={`w-64 pl-10 pr-4 py-2 rounded-lg border ${
                        theme === 'dark'
                          ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </div>

                  {/* Action Buttons - Desktop */}
                  <button
                    onClick={loadDashboardData}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      theme === 'dark'
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : 'bg-blue-500 hover:bg-blue-600 text-white'
                    }`}
                  >
                    Refresh
                  </button>

                  <button
                    onClick={toggleTheme}
                    className={`p-2 rounded-lg transition-colors ${
                      theme === 'dark'
                        ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
                    }`}
                  >
                    {theme === 'dark' ? '☀️' : '🌙'}
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Mobile Menu */}
          {isMobile && showMobileMenu && (
            <div className={`py-4 border-t ${
              theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
            }`}>
              {/* Mobile Search */}
              <div className="relative mb-4">
                <input
                  type="text"
                  placeholder="Search tickets..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 rounded-lg border ${
                    theme === 'dark'
                      ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* Mobile Action Buttons */}
              <div className="flex flex-col space-y-2">
                <button
                  onClick={() => {
                    loadDashboardData();
                    setShowMobileMenu(false);
                  }}
                  className={`w-full px-4 py-3 rounded-lg font-medium transition-colors text-left ${
                    theme === 'dark'
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-500 hover:bg-blue-600 text-white'
                  }`}
                >
                  🔄 Refresh Data
                </button>

                <button
                  onClick={toggleTheme}
                  className={`w-full px-4 py-3 rounded-lg font-medium transition-colors text-left ${
                    theme === 'dark'
                      ? 'bg-gray-700 hover:bg-gray-600 text-white'
                      : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                  }`}
                >
                  {theme === 'dark' ? '☀️ Light Mode' : '🌙 Dark Mode'}
                </button>

                {/* Connection Status - Mobile */}
                <div className={`px-4 py-2 rounded-lg text-sm ${
                  connectionStatus === 'connected' ? 'text-green-500 bg-green-50' :
                  connectionStatus === 'disconnected' ? 'text-yellow-500 bg-yellow-50' : 'text-red-500 bg-red-50'
                } ${theme === 'dark' ? 'bg-opacity-20' : ''}`}>
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      connectionStatus === 'connected' ? 'bg-green-500' :
                      connectionStatus === 'disconnected' ? 'bg-yellow-500' : 'bg-red-500'
                    } animate-pulse`}></div>
                    <span className="capitalize">Connection: {connectionStatus}</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className={`max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 ${isMobile ? 'py-4' : 'py-8'}`}>
        {/* Stats Overview */}
        <div className={`grid grid-cols-1 ${isMobile ? 'grid-cols-2' : 'md:grid-cols-2 lg:grid-cols-4'} gap-${isMobile ? '4' : '6'} mb-${isMobile ? '6' : '8'}`}>
          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    Total Tickets
                  </dt>
                  <dd className="text-lg font-medium">
                    {dashboardData.totalCount.toLocaleString()}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    Performance
                  </dt>
                  <dd className="text-lg font-medium">
                    {performance ? `${performance.loadTimeMs}ms` : 'N/A'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    Last Update
                  </dt>
                  <dd className="text-lg font-medium">
                    {lastUpdate ? lastUpdate.toLocaleTimeString() : 'Never'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    Assignees
                  </dt>
                  <dd className="text-lg font-medium">
                    {Object.keys(dashboardData.stats?.assigneeCounts || {}).length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Status Cards */}
        {config?.statusCards && (
          <div className="mb-8">
            <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Status Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              {config.statusCards
                .filter(card => card.visible)
                .sort((a, b) => a.order - b.order)
                .map(card => (
                  <SimpleStatusCard
                    key={card.id}
                    title={card.title}
                    count={dashboardData.stats?.statusCounts?.[card.jiraStatuses?.[0]] || 0}
                    color={card.color}
                    textColor={card.textColor}
                    className="transform hover:scale-105 transition-transform duration-200"
                  />
                ))}
            </div>
          </div>
        )}

        {/* Priority Cards */}
        {config?.priorityCards && (
          <div className="mb-8">
            <h2 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Priority Overview
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {config.priorityCards
                .filter(card => card.visible)
                .sort((a, b) => a.order - b.order)
                .map(card => (
                  <SimpleStatusCard
                    key={card.id}
                    title={card.title}
                    count={dashboardData.stats?.priorityCounts?.[card.jiraPriorities?.[0]] || 0}
                    color={card.color}
                    textColor={card.textColor}
                    className="transform hover:scale-105 transition-transform duration-200"
                  />
                ))}
            </div>
          </div>
        )}

        {/* Data Visualizations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Status Distribution */}
          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Status Distribution
            </h3>
            <div className="space-y-3">
              {getStatusData().map((item, index) => (
                <div key={item.label} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'][index % 6] }}
                    ></div>
                    <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>
                      {item.label}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{item.value}</div>
                    <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {item.percentage}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Priority Distribution */}
          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Priority Distribution
            </h3>
            <div className="space-y-3">
              {getPriorityData().map((item, index) => (
                <div key={item.label} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: ['#dc2626', '#ea580c', '#ca8a04', '#65a30d'][index % 4] }}
                    ></div>
                    <span className={theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}>
                      {item.label}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{item.value}</div>
                    <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                      {item.percentage}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        {dashboardData.stats?.recentActivity && (
          <div className={`${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          } rounded-xl shadow-lg p-6 border ${
            theme === 'dark' ? 'border-gray-700' : 'border-gray-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-900'}`}>
              Recent Activity
            </h3>
            <div className="space-y-3">
              {dashboardData.stats.recentActivity.map((ticket, index) => (
                <div
                  key={ticket.key || index}
                  className={`flex justify-between items-center p-3 rounded-lg ${
                    theme === 'dark' ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-50 hover:bg-gray-100'
                  } transition-colors duration-200`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      index < 3 ? 'bg-green-500' : 'bg-gray-400'
                    }`}></div>
                    <div>
                      <span className="font-medium">{ticket.key}</span>
                      <span className={`ml-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
                        {ticket.summary}
                      </span>
                    </div>
                  </div>
                  <div className={`text-sm ${theme === 'dark' ? 'text-gray-400' : 'text-gray-500'}`}>
                    {ticket.status} • {new Date(ticket.updated).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>


    </div>
  );
};

export default ModernDashboard;
