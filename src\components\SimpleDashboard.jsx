import React, { useState, useEffect } from 'react';

const SimpleDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    tickets: [],
    stats: null,
    totalCount: 0,
    loading: true,
    error: null
  });

  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    loadDashboardData();
    
    // Set up auto-refresh every 10 seconds
    const interval = setInterval(loadDashboardData, 10000);
    
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      console.log('Loading dashboard data...');
      const response = await fetch('/api/mongodb/dashboard-tickets?stats=true');
      const data = await response.json();
      
      if (data.success) {
        setDashboardData({
          tickets: data.tickets || [],
          stats: data.stats,
          totalCount: data.count || 0,
          loading: false,
          error: null
        });
        setLastUpdate(new Date());
        console.log('Dashboard data loaded:', data.count, 'tickets');
      } else {
        throw new Error(data.error || 'Failed to load data');
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setDashboardData(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
    }
  };

  const getStatusCards = () => {
    const statusCounts = dashboardData.stats?.statusCounts || {};
    return [
      { title: 'Open', count: statusCounts['Open'] || 0, color: '#3b82f6' },
      { title: 'In Progress', count: statusCounts['In Progress'] || 0, color: '#f59e0b' },
      { title: 'Resolved', count: statusCounts['Resolved'] || 0, color: '#10b981' },
      { title: 'Closed', count: statusCounts['Closed'] || 0, color: '#6b7280' },
    ];
  };

  const getPriorityCards = () => {
    const priorityCounts = dashboardData.stats?.priorityCounts || {};
    return [
      { title: 'Urgent', count: priorityCounts['Urgent'] || 0, color: '#dc2626' },
      { title: 'High', count: priorityCounts['High'] || 0, color: '#ea580c' },
      { title: 'Medium', count: priorityCounts['Medium'] || 0, color: '#ca8a04' },
      { title: 'Low', count: priorityCounts['Low'] || 0, color: '#65a30d' },
    ];
  };

  if (dashboardData.loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '50px',
            height: '50px',
            border: '3px solid #e2e8f0',
            borderTop: '3px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 20px'
          }}></div>
          <p style={{ color: '#64748b', fontSize: '18px' }}>Loading Jira Dashboard...</p>
        </div>
      </div>
    );
  }

  if (dashboardData.error) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        backgroundColor: '#f8fafc'
      }}>
        <div style={{ 
          textAlign: 'center',
          padding: '40px',
          backgroundColor: 'white',
          borderRadius: '8px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          maxWidth: '500px'
        }}>
          <h2 style={{ color: '#dc2626', marginBottom: '16px' }}>Error Loading Dashboard</h2>
          <p style={{ color: '#64748b', marginBottom: '24px' }}>{dashboardData.error}</p>
          <button 
            onClick={loadDashboardData}
            style={{
              backgroundColor: '#3b82f6',
              color: 'white',
              padding: '12px 24px',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '16px'
            }}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f8fafc',
      fontFamily: 'system-ui, -apple-system, sans-serif'
    }}>
      {/* Header */}
      <header style={{
        backgroundColor: 'white',
        borderBottom: '1px solid #e2e8f0',
        padding: '16px 24px',
        position: 'sticky',
        top: 0,
        zIndex: 10
      }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <h1 style={{ 
              margin: 0,
              fontSize: '28px',
              fontWeight: 'bold',
              background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}>
              Jira Dashboard Pro
            </h1>
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
              <div style={{ 
                display: 'flex', 
                alignItems: 'center', 
                gap: '8px',
                color: '#10b981',
                fontSize: '14px'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  backgroundColor: '#10b981',
                  borderRadius: '50%',
                  animation: 'pulse 2s infinite'
                }}></div>
                Connected
              </div>
              
              <button 
                onClick={loadDashboardData}
                style={{
                  backgroundColor: '#3b82f6',
                  color: 'white',
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Refresh
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
        {/* Stats Overview */}
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
          gap: '16px', 
          marginBottom: '32px' 
        }}>
          <div style={{
            backgroundColor: 'white',
            padding: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}>
            <h3 style={{ margin: '0 0 8px 0', color: '#64748b', fontSize: '14px' }}>Total Tickets</h3>
            <div style={{ fontSize: '32px', fontWeight: 'bold', color: '#1e293b' }}>
              {dashboardData.totalCount.toLocaleString()}
            </div>
          </div>
          
          <div style={{
            backgroundColor: 'white',
            padding: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
            textAlign: 'center'
          }}>
            <h3 style={{ margin: '0 0 8px 0', color: '#64748b', fontSize: '14px' }}>Last Update</h3>
            <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#1e293b' }}>
              {lastUpdate ? lastUpdate.toLocaleTimeString() : 'Never'}
            </div>
          </div>
        </div>

        {/* Status Cards */}
        <div style={{ marginBottom: '32px' }}>
          <h2 style={{ marginBottom: '16px', color: '#1e293b' }}>Status Overview</h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px' 
          }}>
            {getStatusCards().map((card, index) => (
              <div
                key={index}
                style={{
                  backgroundColor: card.color,
                  color: 'white',
                  padding: '24px',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease',
                }}
                onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
                onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
              >
                <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', opacity: 0.9 }}>
                  {card.title}
                </h3>
                <div style={{ fontSize: '28px', fontWeight: 'bold' }}>
                  {card.count.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Priority Cards */}
        <div style={{ marginBottom: '32px' }}>
          <h2 style={{ marginBottom: '16px', color: '#1e293b' }}>Priority Overview</h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px' 
          }}>
            {getPriorityCards().map((card, index) => (
              <div
                key={index}
                style={{
                  backgroundColor: card.color,
                  color: 'white',
                  padding: '24px',
                  borderRadius: '8px',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  textAlign: 'center',
                  cursor: 'pointer',
                  transition: 'transform 0.2s ease',
                }}
                onMouseEnter={(e) => e.target.style.transform = 'translateY(-2px)'}
                onMouseLeave={(e) => e.target.style.transform = 'translateY(0)'}
              >
                <h3 style={{ margin: '0 0 8px 0', fontSize: '16px', opacity: 0.9 }}>
                  {card.title}
                </h3>
                <div style={{ fontSize: '28px', fontWeight: 'bold' }}>
                  {card.count.toLocaleString()}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        {dashboardData.stats?.recentActivity && (
          <div style={{
            backgroundColor: 'white',
            padding: '24px',
            borderRadius: '8px',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}>
            <h2 style={{ marginBottom: '16px', color: '#1e293b' }}>Recent Activity</h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
              {dashboardData.stats.recentActivity.slice(0, 5).map((ticket, index) => (
                <div
                  key={ticket.key || index}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '12px',
                    backgroundColor: '#f8fafc',
                    borderRadius: '6px',
                    borderLeft: `4px solid ${index < 2 ? '#10b981' : '#64748b'}`
                  }}
                >
                  <div>
                    <span style={{ fontWeight: 'bold', color: '#1e293b' }}>{ticket.key}</span>
                    <span style={{ marginLeft: '8px', color: '#64748b' }}>{ticket.summary}</span>
                  </div>
                  <div style={{ fontSize: '12px', color: '#64748b' }}>
                    {ticket.status} • {new Date(ticket.updated).toLocaleTimeString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>

      {/* CSS Animations */}
      <style>{`
        @keyframes spin {
          to { transform: rotate(360deg); }
        }
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }
      `}</style>
    </div>
  );
};

export default SimpleDashboard;
