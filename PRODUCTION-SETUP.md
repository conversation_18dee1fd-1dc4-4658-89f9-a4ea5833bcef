# 🚀 Jira Dashboard Production Setup Guide

This guide will help you set up the Jira Dashboard for production use on Windows with automatic startup and full screen mode.

## 📋 Prerequisites

Before setting up production mode, ensure you have:

- ✅ **Windows 10/11** (Administrator access required)
- ✅ **Node.js 18+** installed and in PATH
- ✅ **npm** installed and working
- ✅ **MongoDB** (optional, will auto-install if needed)
- ✅ **Modern browser** (Chrome, Edge, or Firefox)

## 🎯 Quick Production Setup

### Step 1: Test Current Setup
```bash
# Test that everything works in development
npm install
npm run dev
```
Visit `http://localhost:5174` to ensure the dashboard loads correctly.

### Step 2: Install Auto-Start (One-Time Setup)
```bash
# Right-click and "Run as Administrator"
setup-auto-start.bat
```
Choose option **1** to install auto-start functionality.

### Step 3: Start Production Mode
```bash
# Double-click to start production
start-production.bat
```

## 🔧 Production Features

### ✨ What Production Mode Includes:

1. **🏗️ Optimized Build**
   - Minified JavaScript and CSS
   - Optimized assets and images
   - Better performance and loading times

2. **🔄 Auto-Restart**
   - Monitors server health every 30 seconds
   - Automatically restarts if server crashes
   - Logs all activities for debugging

3. **🌐 Full Screen Kiosk Mode**
   - Opens in full screen automatically
   - Disables browser UI elements
   - Perfect for dedicated displays

4. **📊 Enhanced Monitoring**
   - Health checks and auto-recovery
   - Detailed logging in `logs/production.log`
   - Network connectivity verification

5. **🚀 Auto-Start on Boot**
   - Starts automatically when Windows boots
   - 2-minute delay to ensure system readiness
   - Multiple startup methods for reliability

## 📁 File Structure

```
jira-dashboard/
├── start-production.bat          # Main production starter
├── setup-auto-start.bat         # Auto-start installer
├── scripts/
│   ├── production-start.ps1     # Production PowerShell script
│   ├── auto-start.ps1           # Auto-start script
│   └── install-auto-start.ps1   # Installation script
├── logs/
│   └── production.log           # Production logs
└── config/
    └── auto-start.json          # Auto-start configuration
```

## 🎮 Usage Instructions

### Manual Start
```bash
# Start production mode manually
start-production.bat
```

### Auto-Start Management
```bash
# Install auto-start (run as Administrator)
setup-auto-start.bat → Option 1

# Check auto-start status
setup-auto-start.bat → Option 3

# Disable auto-start
setup-auto-start.bat → Option 2
```

### Desktop Shortcut
After installing auto-start, you'll get a desktop shortcut:
- **"Jira Dashboard"** - Click to start manually

## 🔍 Monitoring & Logs

### Production Logs
All production activities are logged to:
```
logs/production.log
```

### What Gets Logged:
- ✅ Server startup and shutdown
- ✅ Network connectivity checks
- ✅ Health monitoring results
- ✅ Error messages and recovery attempts
- ✅ Browser launch status

### View Logs:
```bash
# View recent logs
type logs\production.log

# Monitor logs in real-time
Get-Content logs\production.log -Wait
```

## 🛠️ Troubleshooting

### Server Won't Start
1. **Check Node.js installation**
   ```bash
   node --version
   npm --version
   ```

2. **Check port availability**
   ```bash
   netstat -an | findstr :3000
   ```

3. **View production logs**
   ```bash
   type logs\production.log
   ```

### Auto-Start Not Working
1. **Verify installation**
   ```bash
   setup-auto-start.bat → Option 3
   ```

2. **Check Windows startup programs**
   - Open Task Manager → Startup tab
   - Look for "Jira Dashboard"

3. **Run as Administrator**
   ```bash
   # Right-click setup-auto-start.bat
   # Select "Run as administrator"
   ```

### Browser Issues
1. **Try different browser**
   - Chrome (recommended)
   - Microsoft Edge
   - Firefox

2. **Clear browser cache**
   - Press Ctrl+Shift+Delete
   - Clear all data

3. **Check browser permissions**
   - Allow full screen access
   - Disable popup blockers

### Network Issues
1. **Check internet connectivity**
   ```bash
   ping *******
   ```

2. **Verify local server**
   ```bash
   # Open in browser manually
   http://localhost:3000
   ```

## ⚙️ Configuration

### Auto-Start Configuration
Edit `config/auto-start.json`:
```json
{
  "enabled": true,
  "url": "http://localhost:3000",
  "fullScreen": true,
  "browser": "chrome",
  "startDelay": 120
}
```

### Production Settings
The production script automatically:
- Builds optimized version
- Starts on port 3000
- Enables health monitoring
- Opens in full screen mode

## 🔒 Security Considerations

### Network Security
- Dashboard runs on `localhost:3000` only
- No external network access by default
- All data stays on local machine

### Windows Security
- Requires Administrator privileges for auto-start
- Uses Windows Task Scheduler for reliability
- Registry entries for startup programs

## 📊 Performance Optimization

### Production Build Benefits
- **50-70% smaller file sizes**
- **Faster loading times**
- **Better browser caching**
- **Optimized asset delivery**

### Memory Usage
- **Development**: ~200-300MB
- **Production**: ~100-150MB
- **Auto-restart**: Prevents memory leaks

## 🎉 Success Indicators

When everything is working correctly:

✅ **Auto-start installed**: Task Manager shows "Jira Dashboard" in startup  
✅ **Production server running**: `http://localhost:3000` loads the dashboard  
✅ **Full screen mode**: Dashboard opens in kiosk mode  
✅ **Auto-restart working**: Server recovers from crashes automatically  
✅ **Logs updating**: `logs/production.log` shows recent activity  

## 🆘 Support

If you encounter issues:

1. **Check logs first**: `logs/production.log`
2. **Verify prerequisites**: Node.js, npm, browser
3. **Run as Administrator**: Required for auto-start features
4. **Test manually**: Use `start-production.bat` first
5. **Check network**: Ensure internet connectivity

## 🚀 Ready for Production!

Your Jira Dashboard is now ready for production use with:
- ✅ Automatic startup on Windows boot
- ✅ Full screen kiosk mode
- ✅ Auto-restart and health monitoring
- ✅ Optimized performance
- ✅ Comprehensive logging

Perfect for office dashboards, monitoring screens, and dedicated displays! 🎯
