import React, { useState, useRef, useCallback } from 'react';

const DragDropLayout = ({ children, onLayoutChange, gridCols = 3, gridRows = 4 }) => {
  const [draggedItem, setDraggedItem] = useState(null);
  const [dragOverPosition, setDragOverPosition] = useState(null);
  const containerRef = useRef(null);

  const handleDragStart = useCallback((e, item) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', e.target.outerHTML);
    e.target.style.opacity = '0.5';
  }, []);

  const handleDragEnd = useCallback((e) => {
    e.target.style.opacity = '1';
    setDraggedItem(null);
    setDragOverPosition(null);
  }, []);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    if (!containerRef.current || !draggedItem) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const cellWidth = rect.width / gridCols;
    const cellHeight = rect.height / gridRows;
    
    const col = Math.floor(x / cellWidth);
    const row = Math.floor(y / cellHeight);
    
    setDragOverPosition({ row: Math.max(0, Math.min(row, gridRows - 1)), col: Math.max(0, Math.min(col, gridCols - 1)) });
  }, [gridCols, gridRows, draggedItem]);

  const handleDrop = useCallback((e) => {
    e.preventDefault();
    
    if (!draggedItem || !dragOverPosition) return;
    
    if (onLayoutChange) {
      onLayoutChange(draggedItem.id, dragOverPosition);
    }
    
    setDraggedItem(null);
    setDragOverPosition(null);
  }, [draggedItem, dragOverPosition, onLayoutChange]);

  const renderGridOverlay = () => {
    if (!draggedItem) return null;
    
    const cells = [];
    for (let row = 0; row < gridRows; row++) {
      for (let col = 0; col < gridCols; col++) {
        const isHighlighted = dragOverPosition && dragOverPosition.row === row && dragOverPosition.col === col;
        cells.push(
          <div
            key={`${row}-${col}`}
            style={{
              position: 'absolute',
              left: `${(col / gridCols) * 100}%`,
              top: `${(row / gridRows) * 100}%`,
              width: `${100 / gridCols}%`,
              height: `${100 / gridRows}%`,
              border: '2px dashed rgba(59, 130, 246, 0.3)',
              backgroundColor: isHighlighted ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
              transition: 'background-color 0.2s ease',
              pointerEvents: 'none',
              zIndex: 1000
            }}
          />
        );
      }
    }
    return cells;
  };

  return (
    <div
      ref={containerRef}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        minHeight: '600px'
      }}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {renderGridOverlay()}
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        return React.cloneElement(child, {
          draggable: true,
          onDragStart: (e) => handleDragStart(e, child.props.item || { id: `item-${index}` }),
          onDragEnd: handleDragEnd,
          style: {
            ...child.props.style,
            cursor: 'move',
            transition: 'all 0.3s ease',
            transform: draggedItem?.id === (child.props.item?.id || `item-${index}`) ? 'scale(0.95)' : 'scale(1)'
          }
        });
      })}
    </div>
  );
};

export default DragDropLayout;
