import React, { useState, useEffect } from 'react';

const AssigneeFilterPanel = ({ isOpen, onClose, onApplyFilters }) => {
  const [assignees, setAssignees] = useState([]);
  const [selectedAssignees, setSelectedAssignees] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [filterMode, setFilterMode] = useState('include'); // 'include' or 'exclude'
  const [savedFilters, setSavedFilters] = useState([]);

  useEffect(() => {
    if (isOpen) {
      loadAssignees();
      loadSavedFilters();
    }
  }, [isOpen]);

  const loadAssignees = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/dashboard/assignees');
      if (response.ok) {
        const data = await response.json();
        setAssignees(data.assignees || []);
      }
    } catch (error) {
      console.error('Error loading assignees:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadSavedFilters = async () => {
    try {
      const response = await fetch('/api/config/assignee-filters');
      if (response.ok) {
        const data = await response.json();
        setSavedFilters(data.filters || []);
      }
    } catch (error) {
      console.error('Error loading saved filters:', error);
    }
  };

  const filteredAssignees = assignees.filter(assignee =>
    assignee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignee.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAssigneeToggle = (assigneeId) => {
    const newSelected = new Set(selectedAssignees);
    if (newSelected.has(assigneeId)) {
      newSelected.delete(assigneeId);
    } else {
      newSelected.add(assigneeId);
    }
    setSelectedAssignees(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedAssignees.size === filteredAssignees.length) {
      setSelectedAssignees(new Set());
    } else {
      setSelectedAssignees(new Set(filteredAssignees.map(a => a.id)));
    }
  };

  const handleApplyFilters = () => {
    const filterConfig = {
      mode: filterMode,
      assignees: Array.from(selectedAssignees),
      assigneeNames: assignees
        .filter(a => selectedAssignees.has(a.id))
        .map(a => a.name)
    };
    
    onApplyFilters(filterConfig);
    onClose();
  };

  const saveCurrentFilter = async () => {
    const filterName = prompt('Enter a name for this filter:');
    if (!filterName) return;

    const filterConfig = {
      name: filterName,
      mode: filterMode,
      assignees: Array.from(selectedAssignees),
      assigneeNames: assignees
        .filter(a => selectedAssignees.has(a.id))
        .map(a => a.name),
      createdAt: new Date().toISOString()
    };

    try {
      const response = await fetch('/api/config/assignee-filters', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ filter: filterConfig })
      });

      if (response.ok) {
        loadSavedFilters();
        alert('Filter saved successfully!');
      }
    } catch (error) {
      console.error('Error saving filter:', error);
      alert('Error saving filter. Please try again.');
    }
  };

  const loadSavedFilter = (filter) => {
    setFilterMode(filter.mode);
    setSelectedAssignees(new Set(filter.assignees));
  };

  const deleteSavedFilter = async (filterId) => {
    if (!confirm('Are you sure you want to delete this filter?')) return;

    try {
      const response = await fetch(`/api/config/assignee-filters/${filterId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        loadSavedFilters();
      }
    } catch (error) {
      console.error('Error deleting filter:', error);
      alert('Error deleting filter. Please try again.');
    }
  };

  const clearFilters = () => {
    setSelectedAssignees(new Set());
    setFilterMode('include');
    setSearchTerm('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold text-gray-900">Assignee Filters</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <div className="flex-1 flex overflow-hidden">
          {/* Saved Filters Sidebar */}
          <div className="w-1/3 border-r bg-gray-50 p-4 overflow-y-auto">
            <h3 className="text-lg font-semibold mb-4">Saved Filters</h3>
            
            <div className="space-y-2">
              {savedFilters.map(filter => (
                <div
                  key={filter.id}
                  className="bg-white rounded-lg p-3 shadow-sm border hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-gray-900">{filter.name}</h4>
                      <p className="text-sm text-gray-500">
                        {filter.mode === 'include' ? 'Include' : 'Exclude'} {filter.assigneeNames.length} assignees
                      </p>
                      <p className="text-xs text-gray-400">
                        {new Date(filter.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => loadSavedFilter(filter)}
                        className="text-blue-500 hover:text-blue-700 text-sm"
                      >
                        Load
                      </button>
                      <button
                        onClick={() => deleteSavedFilter(filter.id)}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  
                  {filter.assigneeNames.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs text-gray-600">
                        {filter.assigneeNames.slice(0, 3).join(', ')}
                        {filter.assigneeNames.length > 3 && ` +${filter.assigneeNames.length - 3} more`}
                      </p>
                    </div>
                  )}
                </div>
              ))}
              
              {savedFilters.length === 0 && (
                <p className="text-gray-500 text-sm">No saved filters</p>
              )}
            </div>
          </div>

          {/* Main Filter Panel */}
          <div className="flex-1 flex flex-col">
            {/* Filter Controls */}
            <div className="p-6 border-b bg-gray-50">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="filterMode"
                      value="include"
                      checked={filterMode === 'include'}
                      onChange={(e) => setFilterMode(e.target.value)}
                      className="mr-2"
                    />
                    Include only selected
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="filterMode"
                      value="exclude"
                      checked={filterMode === 'exclude'}
                      onChange={(e) => setFilterMode(e.target.value)}
                      className="mr-2"
                    />
                    Exclude selected
                  </label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={saveCurrentFilter}
                    disabled={selectedAssignees.size === 0}
                    className="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600 disabled:bg-gray-300"
                  >
                    Save Filter
                  </button>
                  <button
                    onClick={clearFilters}
                    className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
                  >
                    Clear All
                  </button>
                </div>
              </div>

              {/* Search */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search assignees..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  onClick={handleSelectAll}
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  {selectedAssignees.size === filteredAssignees.length ? 'Deselect All' : 'Select All'}
                </button>
              </div>

              {/* Selection Summary */}
              <div className="mt-2 text-sm text-gray-600">
                {selectedAssignees.size} of {filteredAssignees.length} assignees selected
                {searchTerm && ` (filtered from ${assignees.length} total)`}
              </div>
            </div>

            {/* Assignee List */}
            <div className="flex-1 overflow-y-auto p-6">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {filteredAssignees.map(assignee => (
                    <label
                      key={assignee.id}
                      className={`flex items-center p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedAssignees.has(assignee.id)
                          ? 'bg-blue-50 border-blue-300'
                          : 'bg-white border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={selectedAssignees.has(assignee.id)}
                        onChange={() => handleAssigneeToggle(assignee.id)}
                        className="mr-3"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 truncate">
                          {assignee.name}
                        </div>
                        {assignee.email && (
                          <div className="text-sm text-gray-500 truncate">
                            {assignee.email}
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          {assignee.ticketCount} tickets
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}

              {!loading && filteredAssignees.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  {searchTerm ? 'No assignees match your search' : 'No assignees found'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="text-sm text-gray-600">
            {filterMode === 'include' 
              ? `Show tickets assigned to ${selectedAssignees.size} selected assignees`
              : `Hide tickets assigned to ${selectedAssignees.size} selected assignees`
            }
          </div>
          
          <div className="flex space-x-4">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
            >
              Cancel
            </button>
            <button
              onClick={handleApplyFilters}
              className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 font-medium"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AssigneeFilterPanel;
