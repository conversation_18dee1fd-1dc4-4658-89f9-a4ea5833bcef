import axios from 'axios';
import {
  loadDashboardConfig,
  mapJiraStatusToCard,
  mapJiraPriorityToCard,
  getVisibleStatusCards,
  getVisiblePriorityCards,
  getVisibleOrganizationCards
} from '../config/enhancedDashboardConfig.js';

class OptimizedJiraApiService {
  constructor() {
    this.api = axios.create({
      baseURL: 'http://localhost:3001/api',
      timeout: 15000, // Reduced to 15 seconds for faster failure detection
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });

    // Store last update time for real-time detection
    this.lastUpdateTime = null;
    this.cachedTickets = [];
    this.isLoading = false; // Prevent concurrent full loads
    this.retryCount = 0;
    this.maxRetries = 2;

    // Persistent storage keys
    this.STORAGE_KEYS = {
      TICKETS: 'jira_dashboard_tickets',
      LAST_UPDATE: 'jira_dashboard_last_update',
      METADATA: 'jira_dashboard_metadata'
    };

    // Load cached data on initialization
    this.loadFromStorage();
  }

  // Build JQL query with filters
  buildJQLQuery(config, baseJQL = 'ORDER BY created DESC') {
    let jql = baseJQL;

    // Add year filter if specified
    if (config.filters && config.filters.year && config.filters.year !== 'all') {
      const year = config.filters.year;
      const yearStart = `${year}-01-01`;
      const yearEnd = `${year}-12-31`;

      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', `AND created >= "${yearStart}" AND created <= "${yearEnd}" ORDER BY`);
      } else {
        jql = `${jql} AND created >= "${yearStart}" AND created <= "${yearEnd}"`;
      }
    }

    // Add base project filter if specified
    if (config.jql && config.jql.baseProject) {
      const projectFilter = `project = "${config.jql.baseProject}"`;
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', `AND ${projectFilter} ORDER BY`);
      } else {
        jql = `${jql} AND ${projectFilter}`;
      }
    }

    // Add additional filters if specified
    if (config.jql && config.jql.additionalFilters) {
      if (jql.includes('ORDER BY')) {
        jql = jql.replace('ORDER BY', `${config.jql.additionalFilters} ORDER BY`);
      } else {
        jql = `${jql} ${config.jql.additionalFilters}`;
      }
    }

    return jql;
  }

  // Load data from localStorage
  loadFromStorage() {
    try {
      const storedTickets = localStorage.getItem(this.STORAGE_KEYS.TICKETS);
      const storedLastUpdate = localStorage.getItem(this.STORAGE_KEYS.LAST_UPDATE);

      if (storedTickets && storedLastUpdate) {
        this.cachedTickets = JSON.parse(storedTickets);
        this.lastUpdateTime = new Date(storedLastUpdate);
        console.log(`📦 Loaded ${this.cachedTickets.length} tickets from storage (last update: ${this.lastUpdateTime.toLocaleString()})`);
      } else {
        console.log('📦 No cached tickets found in storage');
      }
    } catch (error) {
      console.warn('⚠️ Error loading from storage:', error.message);
      this.cachedTickets = [];
      this.lastUpdateTime = null;
    }
  }

  // Save data to localStorage
  saveToStorage() {
    try {
      localStorage.setItem(this.STORAGE_KEYS.TICKETS, JSON.stringify(this.cachedTickets));
      localStorage.setItem(this.STORAGE_KEYS.LAST_UPDATE, (this.lastUpdateTime && this.lastUpdateTime.toISOString()) || new Date().toISOString());
      console.log(`💾 Saved ${this.cachedTickets.length} tickets to storage`);
    } catch (error) {
      console.warn('⚠️ Error saving to storage:', error.message);
    }
  }

  // Clear storage (for debugging)
  clearStorage() {
    localStorage.removeItem(this.STORAGE_KEYS.TICKETS);
    localStorage.removeItem(this.STORAGE_KEYS.LAST_UPDATE);
    localStorage.removeItem(this.STORAGE_KEYS.METADATA);
    this.cachedTickets = [];
    this.lastUpdateTime = null;
    console.log('🗑️ Cleared all cached data');
  }

  // Get storage info for debugging
  getStorageInfo() {
    const ticketsData = localStorage.getItem(this.STORAGE_KEYS.TICKETS);
    const ticketsSize = (ticketsData && ticketsData.length) || 0;
    const lastUpdate = localStorage.getItem(this.STORAGE_KEYS.LAST_UPDATE);
    return {
      ticketsCount: this.cachedTickets.length,
      storageSize: `${(ticketsSize / 1024 / 1024).toFixed(2)} MB`,
      lastUpdate: lastUpdate ? new Date(lastUpdate).toLocaleString() : 'Never',
      cacheAge: this.lastUpdateTime ? `${((new Date() - this.lastUpdateTime) / (1000 * 60)).toFixed(1)} minutes` : 'N/A'
    };
  }

  // Retry mechanism for failed requests
  async retryRequest(requestFn, retries = this.maxRetries) {
    for (let i = 0; i <= retries; i++) {
      try {
        return await requestFn();
      } catch (error) {
        console.log(`🔄 Attempt ${i + 1}/${retries + 1} failed:`, error.message);
        if (i === retries) {
          throw error; // Last attempt failed
        }
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, i) * 1000));
      }
    }
  }

  // Get ALL tickets with real-time updates
  async getAllDashboardData() {
    console.log('🚀 OPTIMIZED API: getAllDashboardData() called - NEW VERSION');

    // Check if we have cached data and it's recent (less than 1 hour old)
    if (this.cachedTickets.length > 0 && this.lastUpdateTime) {
      const hoursSinceUpdate = (new Date() - this.lastUpdateTime) / (1000 * 60 * 60);
      if (hoursSinceUpdate < 1) {
        console.log(`📦 Using cached data (${this.cachedTickets.length} tickets, ${hoursSinceUpdate.toFixed(1)}h old)`);
        return await this.processAllTicketsData(this.cachedTickets);
      } else {
        console.log(`🔄 Cached data is ${hoursSinceUpdate.toFixed(1)}h old, fetching updates...`);
      }
    }

    // Prevent concurrent full loads
    if (this.isLoading) {
      console.log('⏳ Full load already in progress, skipping...');
      return null;
    }

    try {
      this.isLoading = true;
      console.log('🚀 Fetching ALL tickets from Jira...');

      const currentConfig = loadDashboardConfig();
      const jqlQuery = this.buildJQLQuery(currentConfig);

      console.log('🔍 Using JQL query:', jqlQuery);

      // First, get total count to know how many tickets we need to fetch
      const totalCountResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key',
          maxResults: 0
        }
      });

      const totalTickets = totalCountResponse.data.total || 0;
      console.log(`📊 Total tickets in Jira: ${totalTickets.toLocaleString()}`);

      // Fetch tickets in batches (optimized for real-time dashboard)
      const batchSize = 100; // Increased to 100 for better efficiency
      const maxTicketsToLoad = totalTickets; // Load ALL tickets (will take much longer)

      console.log('🔧 BATCH SIZE CONFIGURATION:', { batchSize, maxTicketsToLoad });
      // Calculate limited batches for faster loading
      const ticketsToLoad = Math.min(totalTickets, maxTicketsToLoad);
      const totalBatches = Math.ceil(ticketsToLoad / batchSize);
      let allTickets = [];
      let failedBatches = [];

      console.log(`📊 Total tickets available: ${totalTickets.toLocaleString()}, Loading: ${ticketsToLoad.toLocaleString()}, Batches: ${totalBatches.toLocaleString()}`);

      // Fetch all tickets sequentially to avoid overwhelming the server
      for (let i = 0; i < totalBatches; i++) {
        const startAt = i * batchSize;

        try {
          const response = await this.retryRequest(async () => {
            return await this.api.get('/jira/search', {
              params: {
                jql: jqlQuery,
                fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                maxResults: batchSize,
                startAt: startAt
              }
            });
          });

          const tickets = response.data.issues || [];

          // Check if we got empty response (might indicate end of data)
          if (tickets.length === 0) {
            console.log(`⚠️ Batch ${i + 1}/${totalBatches}: Empty response, continuing to next batch...`);
            // Don't break here, continue to next batch as there might be more data
          } else {
            allTickets = allTickets.concat(tickets);
            console.log(`✅ Batch ${i + 1}/${totalBatches}: ${tickets.length} tickets loaded (Total: ${allTickets.length})`);

            // Update dashboard every 5 batches for real-time feel
            if ((i + 1) % 5 === 0 || i === totalBatches - 1) {
              console.log(`🔄 Intermediate update: Processing ${allTickets.length} tickets...`);
              // This will be used by the dashboard to show partial data
            }
          }

          // If we've loaded significantly fewer tickets than expected, log a warning
          if (i === totalBatches - 1 && allTickets.length < totalTickets * 0.9) {
            console.warn(`⚠️ Warning: Only loaded ${allTickets.length} out of ${totalTickets} expected tickets (${((allTickets.length/totalTickets)*100).toFixed(1)}%)`);
          }

          // Minimal delay to prevent rate limiting
          if (i < totalBatches - 1) {
            await new Promise(resolve => setTimeout(resolve, 50)); // Reduced to 50ms delay
          }

        } catch (error) {
          console.error(`❌ Batch ${i + 1}/${totalBatches} failed:`, error.message);

          // Retry once for network errors
          if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || (error.response && error.response.status >= 500)) {
            console.log(`🔄 Retrying batch ${i + 1}/${totalBatches} in 2 seconds...`);
            await new Promise(resolve => setTimeout(resolve, 2000));

            try {
              const retryResponse = await this.api.get('/jira/search', {
                params: {
                  jql: 'ORDER BY created DESC',
                  fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
                  maxResults: batchSize,
                  startAt: startAt
                }
              });

              const tickets = retryResponse.data.issues || [];
              allTickets = allTickets.concat(tickets);
              console.log(`✅ Batch ${i + 1}/${totalBatches} succeeded on retry: ${tickets.length} tickets loaded (Total: ${allTickets.length})`);

            } catch (retryError) {
              console.error(`❌ Batch ${i + 1}/${totalBatches} failed on retry:`, retryError.message);
              failedBatches.push({ batchIndex: i, startAt, batchSize });
            }
          } else {
            // Track non-network errors as failed batches
            failedBatches.push({ batchIndex: i, startAt, batchSize });
          }

          // Continue with next batch even if retry fails
        }
      }

      console.log(`🎉 Successfully loaded ${allTickets.length} out of ${totalTickets} tickets`);

      // Report loading statistics
      const loadingPercentage = ((allTickets.length / totalTickets) * 100).toFixed(1);
      console.log(`📊 Loading Progress: ${loadingPercentage}% complete (${allTickets.length.toLocaleString()}/${totalTickets.toLocaleString()} tickets)`);

      if (allTickets.length < totalTickets) {
        const remaining = totalTickets - allTickets.length;
        console.log(`⏳ ${remaining.toLocaleString()} tickets remaining to load`);

        if (failedBatches.length > 0) {
          console.log(`⚠️ ${failedBatches.length} batches failed during loading`);
          console.log(`💡 Consider retrying failed batches or reducing batch size further`);
        }
      }

      if (failedBatches.length > 0) {
        console.log(`📊 Loading Summary: ${allTickets.length}/${totalTickets} tickets loaded (${failedBatches.length} batches failed)`);
      }

      // Cache tickets and update timestamp for real-time detection
      this.cachedTickets = this.cleanupCachedTickets(allTickets);
      this.lastUpdateTime = new Date();

      // Save to persistent storage
      this.saveToStorage();

      // No scale factor needed - we have ALL tickets
      const scaleFactor = 1;

      // Process all tickets data using the shared method
      const dashboardData = await this.processAllTicketsData(allTickets);

      // Add batch loading metadata
      dashboardData.metadata.batchesLoaded = totalBatches;
      dashboardData.metadata.isRealTimeUpdate = false;

      return dashboardData;

    } catch (error) {
      console.error('❌ Error fetching optimized dashboard data:', error);
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Check for new tickets since last update (real-time detection)
  async checkForNewTickets() {
    try {
      if (!this.lastUpdateTime) {
        console.log('⚠️ No previous update time, performing full refresh...');
        return await this.getAllDashboardData();
      }

      // Check for tickets created since last update
      const currentConfig = loadDashboardConfig();

      // Use relative time for more reliable real-time updates (last 2 minutes)
      let baseJQL = 'created >= -2m';
      const jqlQuery = this.buildJQLQuery(currentConfig, baseJQL);

      console.log(`🔍 Checking for new tickets with JQL: ${jqlQuery}`);

      const newTicketsResponse = await this.api.get('/jira/search', {
        params: {
          jql: jqlQuery,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
          maxResults: 100 // Consistent with batch loading for stability
        }
      });

      const newTickets = newTicketsResponse.data.issues || [];

      if (newTickets.length > 0) {
        console.log(`🆕 Found ${newTickets.length} new tickets!`);

        // Add new tickets to the beginning of cached tickets
        this.cachedTickets = [...newTickets, ...this.cachedTickets];
        this.lastUpdateTime = new Date();

        // Save updated data to storage
        this.saveToStorage();

        // Return updated dashboard data
        return await this.processAllTicketsData(this.cachedTickets);
      } else {
        console.log('✅ No new tickets found');
        return null; // No updates needed
      }

    } catch (error) {
      console.error('❌ Error checking for new tickets:', error.message);

      // If there's an error, try with a simpler approach
      console.log('🔄 Trying fallback approach for real-time updates...');
      try {
        const currentConfig = loadDashboardConfig();
        // Try with relative date format (last 5 minutes) as fallback
        const fallbackJQL = this.buildJQLQuery(currentConfig, 'created >= -5m ORDER BY created DESC');

        const alternativeResponse = await this.api.get('/jira/search', {
          params: {
            jql: fallbackJQL,
            fields: 'key,summary,status,priority,assignee,project,created,updated,description,customfield_10000,customfield_10001,customfield_10002,customfield_10003,customfield_10004,customfield_10005,labels,components',
            maxResults: 50
            }
          });

          const recentTickets = alternativeResponse.data.issues || [];

          if (recentTickets.length > 0) {
            console.log(`🆕 Found ${recentTickets.length} recent tickets using alternative method!`);

            // Filter tickets that are actually newer than our last update
            const newTickets = recentTickets.filter(ticket => {
              if (!ticket.fields || !ticket.fields.created) return false;
              const ticketDate = new Date(ticket.fields.created);
              return ticketDate > this.lastUpdateTime;
            });

            if (newTickets.length > 0) {
              // Add new tickets to the beginning of cached tickets
              this.cachedTickets = [...newTickets, ...this.cachedTickets];
              this.lastUpdateTime = new Date();

              // Save updated data to storage
              this.saveToStorage();

              // Return updated data
              return await this.processAllTicketsData(this.cachedTickets);
            }
          }

          console.log('✅ No new tickets found with alternative method');
          return null;

        } catch (altError) {
          console.error('❌ Alternative method also failed:', altError.message);
        }
      }

      // For other errors, try a full refresh but less frequently
      const timeSinceLastUpdate = Date.now() - ((this.lastUpdateTime && this.lastUpdateTime.getTime()) || 0);
      if (timeSinceLastUpdate > 300000) { // Only full refresh if last update was more than 5 minutes ago
        console.log('🔄 Performing full refresh due to extended time since last update...');
        return await this.getAllDashboardData();
      }

      console.log('⏭️ Skipping update due to recent full refresh');
      return null;
    }
  }

  // Process all tickets data (used by both full load and incremental updates)
  async processAllTicketsData(allTickets) {
    const currentConfig = loadDashboardConfig();
    const scaleFactor = 1;
    const totalTickets = allTickets.length;

    // Process status counts using ALL tickets
    const statusCounts = this.processStatusCounts(allTickets, currentConfig, scaleFactor);

    // Process priority counts using ALL tickets
    const priorityCounts = this.processPriorityCounts(allTickets, currentConfig, scaleFactor);

    // Process organization data using ALL tickets
    const organizationCounts = this.processOrganizationCounts(allTickets, currentConfig, scaleFactor);

    // Generate trends data from ALL tickets
    const trendsData = this.generateTrendsData(allTickets);

    // Calculate dashboard stats from ALL tickets
    const dashboardStats = this.calculateDashboardStats(allTickets, totalTickets);

    // Transform tickets for table display (get most recent 1000 for initial display)
    const recentTickets = allTickets.slice(0, 1000).map(issue => ({
      key: issue.key,
      summary: (issue.fields && issue.fields.summary) || 'No summary',
      status: (issue.fields && issue.fields.status && issue.fields.status.name) || 'Unknown',
      priority: (issue.fields && issue.fields.priority && issue.fields.priority.name) || 'None',
      assignee: (issue.fields && issue.fields.assignee && issue.fields.assignee.displayName) || 'Unassigned',
      project: (issue.fields && issue.fields.project && issue.fields.project.name) || (issue.fields && issue.fields.project && issue.fields.project.key) || 'Unknown',
      created: (issue.fields && issue.fields.created) ? new Date(issue.fields.created).toLocaleDateString() : 'Unknown',
      updated: (issue.fields && issue.fields.updated) ? new Date(issue.fields.updated).toLocaleDateString() : 'Unknown',
      description: (issue.fields && issue.fields.description) || '',
      organization: this.extractOrganizationFromTicket(issue)
    }));

    return {
      status: statusCounts,
      priority: priorityCounts,
      organizations: organizationCounts,
      trends: trendsData,
      recentTickets: recentTickets,
      allTickets: allTickets, // Include ALL tickets for real-time filtering
      stats: dashboardStats,
      metadata: {
        totalTickets,
        actualTickets: allTickets.length,
        scaleFactor: scaleFactor.toFixed(2),
        lastUpdated: new Date().toLocaleString(),
        isRealTimeUpdate: true
      }
    };
  }

  // Format date for Jira JQL queries (yyyy-MM-dd HH:mm format)
  formatDateForJira(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }

  // Extract organization data from ticket fields
  extractOrganizationFromTicket(ticket) {
    const fields = ticket.fields || {};

    // Check various sources for organization information
    let organization = 'Other';

    // 1. Check project key first
    const projectKey = (fields.project && fields.project.key) || '';

    // Debug logging for first few tickets
    if (Math.random() < 0.05) { // Log 5% of tickets for debugging
      console.log(`🔍 Debug ticket ${ticket.key}: projectKey="${projectKey}", project="${(fields.project && fields.project.name) || ''}"`);
      console.log(`   📋 Available fields:`, Object.keys(fields));
      console.log(`   🏷️ Labels:`, fields.labels);
      console.log(`   🧩 Components:`, (fields.components && fields.components.map(c => c.name)) || []);
    }

    // Enhanced project key mapping
    if (projectKey === 'STORE' || projectKey.includes('STORE')) {
      organization = 'STORE';
    } else if (['IT', 'SUPPORT', 'TECH', 'HELPDESK', 'SERVICE'].includes(projectKey.toUpperCase())) {
      organization = 'IT Support';
    } else if (['OPS', 'OPERATIONS', 'OPERATION'].includes(projectKey.toUpperCase())) {
      organization = 'Operations';
    } else if (['MAINT', 'MAINTENANCE', 'REPAIR'].includes(projectKey.toUpperCase())) {
      organization = 'Maintenance';
    }

    // Also check project name for organization clues
    const projectName = ((fields.project && fields.project.name) || '').toLowerCase();
    if (organization === 'Other' && projectName) {
      if (projectName.includes('store') || projectName.includes('magasin') || projectName.includes('shop')) {
        organization = 'STORE';
      } else if (projectName.includes('it') || projectName.includes('support') || projectName.includes('tech') || projectName.includes('service')) {
        organization = 'IT Support';
      } else if (projectName.includes('ops') || projectName.includes('operation')) {
        organization = 'Operations';
      } else if (projectName.includes('maint') || projectName.includes('maintenance') || projectName.includes('repair')) {
        organization = 'Maintenance';
      }
    }

    // 2. Check custom fields for organization info
    const customFields = [
      fields.customfield_10000,
      fields.customfield_10001,
      fields.customfield_10002,
      fields.customfield_10003,
      fields.customfield_10004,
      fields.customfield_10005
    ];

    for (const customField of customFields) {
      if (customField) {
        const value = typeof customField === 'string' ? customField : customField.value || customField.name || '';
        const lowerValue = value.toLowerCase();

        if (lowerValue.includes('store') || lowerValue.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerValue.includes('it') || lowerValue.includes('support') || lowerValue.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerValue.includes('ops') || lowerValue.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerValue.includes('maint') || lowerValue.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    // 3. Check labels
    if (fields.labels && Array.isArray(fields.labels)) {
      for (const label of fields.labels) {
        const lowerLabel = label.toLowerCase();
        if (lowerLabel.includes('store') || lowerLabel.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (lowerLabel.includes('it') || lowerLabel.includes('support') || lowerLabel.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (lowerLabel.includes('ops') || lowerLabel.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (lowerLabel.includes('maint') || lowerLabel.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    // 4. Check components
    if (fields.components && Array.isArray(fields.components)) {
      for (const component of fields.components) {
        const componentName = (component.name || '').toLowerCase();
        if (componentName.includes('store') || componentName.includes('magasin')) {
          organization = 'STORE';
          break;
        } else if (componentName.includes('it') || componentName.includes('support') || componentName.includes('tech')) {
          organization = 'IT Support';
          break;
        } else if (componentName.includes('ops') || componentName.includes('operation')) {
          organization = 'Operations';
          break;
        } else if (componentName.includes('maint') || componentName.includes('maintenance')) {
          organization = 'Maintenance';
          break;
        }
      }
    }

    // Debug logging for organization extraction
    if (Math.random() < 0.01) { // Log 1% of tickets for debugging
      console.log(`🔍 Debug ticket ${ticket.key}: Final organization="${organization}"`);
    }

    return organization;
  }

  // Clean up cached tickets to prevent memory issues
  cleanupCachedTickets(tickets) {
    // Remove duplicates based on ticket key
    const uniqueTickets = [];
    const seenKeys = new Set();

    for (const ticket of tickets) {
      if (ticket.key && !seenKeys.has(ticket.key)) {
        seenKeys.add(ticket.key);
        uniqueTickets.push(ticket);
      }
    }

    // Sort by created date (newest first)
    uniqueTickets.sort((a, b) => {
      const dateA = new Date((a.fields && a.fields.created) || 0);
      const dateB = new Date((b.fields && b.fields.created) || 0);
      return dateB - dateA;
    });

    // Limit to reasonable number to prevent memory issues (keep most recent 50,000)
    const maxTickets = 50000;
    if (uniqueTickets.length > maxTickets) {
      console.log(`🧹 Cleaning up cached tickets: ${uniqueTickets.length} -> ${maxTickets}`);
      return uniqueTickets.slice(0, maxTickets);
    }

    return uniqueTickets;
  }

  processStatusCounts(tickets, config, scaleFactor) {
    const statusCounts = {};
    
    // Initialize all visible status cards with 0
    getVisibleStatusCards(config).forEach(card => {
      statusCounts[card.id] = 0;
    });

    // Count tickets by status
    tickets.forEach(ticket => {
      const status = (ticket.fields && ticket.fields.status && ticket.fields.status.name);
      if (status) {
        const cardId = mapJiraStatusToCard(status, config);
        if (cardId && statusCounts.hasOwnProperty(cardId)) {
          statusCounts[cardId]++;
        }
      }
    });

    // Scale up the counts and map to card titles
    const result = {};
    getVisibleStatusCards(config).forEach(card => {
      const originalCount = statusCounts[card.id] || 0;
      result[card.title] = Math.round(originalCount * scaleFactor);
    });

    return result;
  }

  processPriorityCounts(tickets, config, scaleFactor) {
    const priorityCounts = {};
    
    // Initialize all visible priority cards with 0
    getVisiblePriorityCards(config).forEach(card => {
      priorityCounts[card.id] = 0;
    });

    // Count tickets by priority
    tickets.forEach(ticket => {
      const priority = (ticket.fields && ticket.fields.priority && ticket.fields.priority.name);
      if (priority) {
        const cardId = mapJiraPriorityToCard(priority, config);
        if (cardId && priorityCounts.hasOwnProperty(cardId)) {
          priorityCounts[cardId]++;
        }
      }
    });

    // Scale up the counts and map to card titles
    const result = {};
    getVisiblePriorityCards(config).forEach(card => {
      const originalCount = priorityCounts[card.id] || 0;
      result[card.title] = Math.round(originalCount * scaleFactor);
    });

    return result;
  }

  processOrganizationCounts(tickets, config, scaleFactor) {
    const organizationCounts = {};

    // Initialize all visible organization cards with 0
    getVisibleOrganizationCards(config).forEach(card => {
      organizationCounts[card.title] = 0;
    });

    // Count tickets by organization using enhanced extraction
    tickets.forEach(ticket => {
      const orgName = this.extractOrganizationFromTicket(ticket);

      // Only count if we have a matching organization card
      if (orgName && organizationCounts.hasOwnProperty(orgName)) {
        organizationCounts[orgName] += scaleFactor;
      }
    });

    // Round the counts
    Object.keys(organizationCounts).forEach(key => {
      organizationCounts[key] = Math.round(organizationCounts[key]);
    });

    return organizationCounts;
  }

  processOrganizationData(tickets) {
    const projectCounts = {};
    
    tickets.forEach(ticket => {
      const project = (ticket.fields && ticket.fields.project && ticket.fields.project.key) || (ticket.key && ticket.key.split('-')[0]);
      if (project) {
        projectCounts[project] = (projectCounts[project] || 0) + 1;
      }
    });

    // Convert to array format for charts
    return Object.entries(projectCounts)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10 projects
  }

  generateTrendsData(tickets = []) {
    // Generate real trends data from actual tickets for the last 6 months
    const trends = [];
    const currentDate = new Date();

    for (let i = 5; i >= 0; i--) {
      const monthStart = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
      const monthEnd = new Date(currentDate.getFullYear(), currentDate.getMonth() - i + 1, 0);

      // Count tickets created in this month
      const monthTickets = tickets.filter(ticket => {
        if (!ticket.fields || !ticket.fields.created) return false;
        const createdDate = new Date(ticket.fields.created);
        return createdDate >= monthStart && createdDate <= monthEnd;
      });

      trends.push({
        name: monthStart.toLocaleDateString('en-US', { month: 'short' }),
        count: monthTickets.length
      });
    }

    return trends;
  }

  calculateDashboardStats(tickets, totalTickets) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const ticketsToday = tickets.filter(ticket => {
      const created = new Date((ticket.fields && ticket.fields.created) || 0);
      return created >= today;
    }).length;

    return {
      totalTickets,
      ticketsToday,
      recentTickets: tickets.slice(0, 10).map(ticket => ({
        key: ticket.key,
        summary: (ticket.fields && ticket.fields.summary),
        status: (ticket.fields && ticket.fields.status && ticket.fields.status.name),
        priority: (ticket.fields && ticket.fields.priority && ticket.fields.priority.name),
        created: (ticket.fields && ticket.fields.created)
      }))
    };
  }

  // Simplified method for table data
  async getTableTickets(page = 0, pageSize = 25, filters = {}) {
    try {
      let jql = 'ORDER BY created DESC';
      
      // Add filters if provided
      if (filters.status) {
        jql = `status = "${filters.status}" AND ${jql}`;
      }
      if (filters.priority) {
        jql = `priority = "${filters.priority}" AND ${jql}`;
      }
      if (filters.project) {
        jql = `project = "${filters.project}" AND ${jql}`;
      }

      const response = await this.api.get('/jira/search', {
        params: {
          jql,
          fields: 'key,summary,status,priority,assignee,project,created,updated,description',
          maxResults: pageSize,
          startAt: page * pageSize
        }
      });

      // Transform Jira API data to flat structure for table
      const transformedTickets = (response.data.issues || []).map(issue => ({
        key: issue.key,
        summary: (issue.fields && issue.fields.summary) || 'No summary',
        status: (issue.fields && issue.fields.status && issue.fields.status.name) || 'Unknown',
        priority: (issue.fields && issue.fields.priority && issue.fields.priority.name) || 'None',
        assignee: (issue.fields && issue.fields.assignee && issue.fields.assignee.displayName) || 'Unassigned',
        projectKey: (issue.fields && issue.fields.project && issue.fields.project.key) || (issue.key && issue.key.split('-')[0]) || 'Unknown',
        project: (issue.fields && issue.fields.project && issue.fields.project.name) || (issue.fields && issue.fields.project && issue.fields.project.key) || 'Unknown',
        created: (issue.fields && issue.fields.created) ? new Date(issue.fields.created).toLocaleDateString() : 'Unknown',
        updated: (issue.fields && issue.fields.updated) ? new Date(issue.fields.updated).toLocaleDateString() : 'Unknown',
        description: (issue.fields && issue.fields.description) || '',
        // Keep original issue data for reference
        originalIssue: issue
      }));

      return {
        tickets: transformedTickets,
        total: response.data.total || 0,
        page,
        pageSize
      };
    } catch (error) {
      console.error('❌ Error fetching table tickets:', error);
      throw error;
    }
  }
}

const optimizedJiraApi = new OptimizedJiraApiService();
export default optimizedJiraApi;
