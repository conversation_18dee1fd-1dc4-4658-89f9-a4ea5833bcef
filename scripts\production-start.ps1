# Jira Dashboard Production Auto-Start Script
# This script starts the Jira Dashboard in production mode with auto-restart

Write-Host "🚀 Starting Jira Dashboard Production Mode..." -ForegroundColor Green

# Get the script directory
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$projectDir = Split-Path -Parent $scriptDir

# Change to project directory
Set-Location $projectDir

# Log file for debugging
$logFile = Join-Path $projectDir "logs\production.log"
$logDir = Split-Path -Parent $logFile
if (-not (Test-Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force
}

function Write-Log {
    param($Message, $Color = "White")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host $logMessage -ForegroundColor $Color
    Add-Content -Path $logFile -Value $logMessage
}

Write-Log "🚀 Production startup initiated" "Green"

# Check if Node.js is available
if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
    Write-Log "❌ Error: Node.js is not installed or not in PATH" "Red"
    exit 1
}

# Check if npm is available
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Log "❌ Error: npm is not installed or not in PATH" "Red"
    exit 1
}

# Wait for network connectivity
Write-Log "🌐 Waiting for network connectivity..." "Yellow"
$timeout = 60
$elapsed = 0
while ($elapsed -lt $timeout) {
    try {
        $response = Test-NetConnection -ComputerName "*******" -Port 53 -InformationLevel Quiet
        if ($response) {
            Write-Log "✅ Network is available" "Green"
            break
        }
    }
    catch {
        # Continue waiting
    }
    Start-Sleep -Seconds 3
    $elapsed += 3
}

if ($elapsed -ge $timeout) {
    Write-Log "⚠️ Network timeout, continuing anyway..." "Yellow"
}

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Log "📦 Installing dependencies..." "Yellow"
    try {
        & npm install
        Write-Log "✅ Dependencies installed successfully" "Green"
    }
    catch {
        Write-Log "❌ Failed to install dependencies: $($_.Exception.Message)" "Red"
        exit 1
    }
}

# Build production version
Write-Log "🔨 Building production version..." "Yellow"
try {
    & npm run build
    Write-Log "✅ Production build completed" "Green"
}
catch {
    Write-Log "❌ Failed to build production version: $($_.Exception.Message)" "Red"
    exit 1
}

# Auto-install and start MongoDB
Write-Log "🍃 Setting up MongoDB..." "Yellow"
try {
    & powershell.exe -ExecutionPolicy Bypass -File "scripts\auto-install-mongodb.ps1"
    Write-Log "✅ MongoDB setup completed" "Green"
}
catch {
    Write-Log "❌ MongoDB setup failed: $($_.Exception.Message)" "Red"
    Write-Log "🔄 Attempting alternative MongoDB setup..." "Yellow"

    # Try alternative setup
    try {
        & node "scripts\setup-mongodb.js"
        Write-Log "✅ Alternative MongoDB setup completed" "Green"
    }
    catch {
        Write-Log "⚠️ MongoDB setup failed, application may not work properly" "Yellow"
    }
}

# Function to start the server
function Start-Server {
    Write-Log "🚀 Starting production server..." "Yellow"
    
    # Kill any existing processes on our ports
    $ports = @(3000, 5174, 5173)
    foreach ($port in $ports) {
        try {
            $processes = Get-NetTCPConnection -LocalPort $port -ErrorAction SilentlyContinue | ForEach-Object { Get-Process -Id $_.OwningProcess -ErrorAction SilentlyContinue }
            foreach ($process in $processes) {
                Write-Log "🔄 Stopping existing process on port $port (PID: $($process.Id))" "Yellow"
                Stop-Process -Id $process.Id -Force -ErrorAction SilentlyContinue
            }
        }
        catch {
            # Port not in use, continue
        }
    }
    
    # Start the production server
    $serverProcess = Start-Process -FilePath "npm" -ArgumentList "run", "preview", "--", "--host", "0.0.0.0", "--port", "3000" -WindowStyle Hidden -PassThru
    
    # Wait for server to start
    Write-Log "⏳ Waiting for server to start..." "Yellow"
    $serverStarted = $false
    $maxWait = 120
    $waited = 0
    
    while ($waited -lt $maxWait -and -not $serverStarted) {
        Start-Sleep -Seconds 3
        $waited += 3
        
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                $serverStarted = $true
                Write-Log "✅ Server started successfully on http://localhost:3000" "Green"
            }
        }
        catch {
            # Continue waiting
        }
        
        # Show progress
        if ($waited % 15 -eq 0) {
            Write-Log "⏳ Still waiting for server... ($waited/$maxWait seconds)" "Yellow"
        }
    }
    
    if (-not $serverStarted) {
        Write-Log "❌ Server failed to start within $maxWait seconds" "Red"
        return $false
    }
    
    return $serverProcess
}

# Start the server with retry logic
$maxRetries = 3
$retryCount = 0
$serverProcess = $null

while ($retryCount -lt $maxRetries -and -not $serverProcess) {
    $retryCount++
    Write-Log "🔄 Server start attempt $retryCount of $maxRetries" "Yellow"
    
    $serverProcess = Start-Server
    
    if (-not $serverProcess) {
        if ($retryCount -lt $maxRetries) {
            Write-Log "⏳ Waiting 10 seconds before retry..." "Yellow"
            Start-Sleep -Seconds 10
        }
    }
}

if (-not $serverProcess) {
    Write-Log "❌ Failed to start server after $maxRetries attempts" "Red"
    exit 1
}

# Wait a bit more to ensure server is fully ready
Start-Sleep -Seconds 5

# Open browser in full screen mode
Write-Log "🌐 Opening dashboard in full screen mode..." "Green"

# Try different browsers in order of preference
$browsers = @(
    @{Name="chrome.exe"; Args=@("--start-fullscreen", "--kiosk", "--disable-infobars", "--disable-extensions", "--no-first-run", "--disable-default-apps", "http://localhost:3000?autostart=true&fullscreen=true")},
    @{Name="msedge.exe"; Args=@("--start-fullscreen", "--kiosk", "--disable-infobars", "--disable-extensions", "--no-first-run", "http://localhost:3000?autostart=true&fullscreen=true")},
    @{Name="firefox.exe"; Args=@("-kiosk", "http://localhost:3000?autostart=true&fullscreen=true")}
)

$browserOpened = $false
foreach ($browser in $browsers) {
    try {
        if (Get-Command $browser.Name -ErrorAction SilentlyContinue) {
            Start-Process $browser.Name -ArgumentList $browser.Args
            $browserOpened = $true
            Write-Log "✅ Dashboard opened with $($browser.Name)" "Green"
            break
        }
    }
    catch {
        continue
    }
}

if (-not $browserOpened) {
    # Fallback to default browser
    Start-Process "http://localhost:3000?autostart=true&fullscreen=true"
    Write-Log "✅ Dashboard opened with default browser" "Green"
}

Write-Log "🎉 Jira Dashboard Production Auto-Start completed successfully!" "Green"
Write-Log "📊 Dashboard URL: http://localhost:3000" "Cyan"
Write-Log "📝 Log file: $logFile" "Cyan"

# Keep the script running to monitor the server
Write-Log "🔍 Monitoring server health..." "Yellow"

while ($true) {
    Start-Sleep -Seconds 30
    
    # Check if server is still running
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 10 -ErrorAction SilentlyContinue
        if ($response.StatusCode -ne 200) {
            throw "Server not responding"
        }
    }
    catch {
        Write-Log "⚠️ Server health check failed, attempting restart..." "Yellow"
        
        # Try to restart the server
        if ($serverProcess -and -not $serverProcess.HasExited) {
            $serverProcess.Kill()
        }
        
        $serverProcess = Start-Server
        if (-not $serverProcess) {
            Write-Log "❌ Failed to restart server" "Red"
            break
        }
    }
}
