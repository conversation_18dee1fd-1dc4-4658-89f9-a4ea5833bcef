@echo off
title Jira Dashboard Production Starter

echo ========================================
echo    Jira Dashboard Production Mode
echo ========================================
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

echo Starting Jira Dashboard in Production Mode...
echo.
echo This will:
echo - Build the production version
echo - Start MongoDB (if needed)
echo - Launch the dashboard server
echo - Open in full screen mode
echo - Auto-restart on failure
echo.

echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Starting production server...
powershell.exe -ExecutionPolicy Bypass -File "scripts\production-start.ps1"

echo.
echo Production server has stopped.
pause
