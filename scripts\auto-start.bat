@echo off
REM Jira Dashboard Auto-Start Script
REM This script starts the Jira Dashboard in full screen mode

echo Starting Jira Dashboard...

REM Wait for network to be available
timeout /t 10 /nobreak >nul

REM Start the dashboard server (if not already running)
cd /d "%~dp0\.."
start /min cmd /c "npm start"

REM Wait for server to start
timeout /t 15 /nobreak >nul

REM Open browser in full screen mode
start "" "http://localhost:3000?autostart=true&fullscreen=true"

echo Jira Dashboard started successfully!
